#!/usr/bin/env python3
"""简单测试网格合并功能"""

import numpy as np

def test_merge_function():
    """测试网格合并功能的核心逻辑"""
    
    # 模拟PowScanDataset类的相关常量
    MAX_POINTS_PER_SAMPLE = 180000
    MIN_POINTS_PER_SAMPLE = 90000
    GRID_OVERLAP_RATIO = 0.1
    
    def _get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k):
        """计算指定网格的边界（带重叠）"""
        cell_size = spatial_extent / grid_dims
        
        start_x = min_coord[0] + (i * cell_size[0]) - (GRID_OVERLAP_RATIO * cell_size[0])
        end_x = min_coord[0] + ((i + 1) * cell_size[0]) + (GRID_OVERLAP_RATIO * cell_size[0])
        
        start_y = min_coord[1] + (j * cell_size[1]) - (GRID_OVERLAP_RATIO * cell_size[1])
        end_y = min_coord[1] + ((j + 1) * cell_size[1]) + (GRID_OVERLAP_RATIO * cell_size[1])
        
        start_z = min_coord[2] + (k * cell_size[2]) - (GRID_OVERLAP_RATIO * cell_size[2])
        end_z = min_coord[2] + ((k + 1) * cell_size[2]) + (GRID_OVERLAP_RATIO * cell_size[2])
        
        return {
            "start_x": start_x,
            "end_x": end_x,
            "start_y": start_y,
            "end_y": end_y,
            "start_z": start_z,
            "end_z": end_z
        }
    
    def _get_merged_boundaries(min_coord, spatial_extent, grid_dims, indices):
        """计算合并网格的边界"""
        cell_size = spatial_extent / grid_dims
        
        # 计算合并网格的最小和最大坐标
        min_i = min(idx[0] for idx in indices)
        max_i = max(idx[0] for idx in indices)
        min_j = min(idx[1] for idx in indices)
        max_j = max(idx[1] for idx in indices)
        min_k = min(idx[2] for idx in indices)
        max_k = max(idx[2] for idx in indices)
        
        # 计算合并后的边界（考虑重叠）
        start_x = min_coord[0] + (min_i * cell_size[0]) - (GRID_OVERLAP_RATIO * cell_size[0])
        end_x = min_coord[0] + ((max_i + 1) * cell_size[0]) + (GRID_OVERLAP_RATIO * cell_size[0])
        
        start_y = min_coord[1] + (min_j * cell_size[1]) - (GRID_OVERLAP_RATIO * cell_size[1])
        end_y = min_coord[1] + ((max_j + 1) * cell_size[1]) + (GRID_OVERLAP_RATIO * cell_size[1])
        
        start_z = min_coord[2] + (min_k * cell_size[2]) - (GRID_OVERLAP_RATIO * cell_size[2])
        end_z = min_coord[2] + ((max_k + 1) * cell_size[2]) + (GRID_OVERLAP_RATIO * cell_size[2])
        
        return {
            "start_x": start_x,
            "end_x": end_x,
            "start_y": start_y,
            "end_y": end_y,
            "start_z": start_z,
            "end_z": end_z
        }
    
    def _calculate_grid_dimensions(grid_count, spatial_extent):
        """计算网格在三个维度上的分布"""
        # 防止除零错误
        total_extent = np.sum(spatial_extent)
        if total_extent == 0:
            return [1, 1, 1]

        # 根据空间范围的比例分配网格维度
        # 使用立方根来更均匀地分布网格
        base_dim = max(1, round(grid_count ** (1/3)))

        # 计算每个维度的比例权重
        x_ratio = spatial_extent[0] / total_extent
        y_ratio = spatial_extent[1] / total_extent
        z_ratio = spatial_extent[2] / total_extent

        # 基于比例和立方根分配维度
        x_dim = max(1, round(base_dim * (x_ratio ** (1/3)) * 2))
        y_dim = max(1, round(base_dim * (y_ratio ** (1/3)) * 2))
        z_dim = max(1, round(grid_count / (x_dim * y_dim)))

        # 确保不超过目标网格数量
        while x_dim * y_dim * z_dim > grid_count and z_dim > 1:
            z_dim -= 1
        while x_dim * y_dim * z_dim > grid_count and y_dim > 1:
            y_dim -= 1
        while x_dim * y_dim * z_dim > grid_count and x_dim > 1:
            x_dim -= 1

        # 确保至少有一个网格
        if x_dim * y_dim * z_dim == 0:
            return [1, 1, 1]

        return [x_dim, y_dim, z_dim]
    
    # 创建测试数据
    coords = np.random.rand(200000, 3) * 100  # 200000个点，分布在100x100x100的空间
    points_count = coords.shape[0]
    
    print(f"原始点数: {points_count}")
    
    # 计算边界框和空间范围
    min_coord = np.min(coords, axis=0)
    max_coord = np.max(coords, axis=0)
    spatial_extent = max_coord - min_coord
    
    # 防止空间范围为零的情况
    spatial_extent = np.maximum(spatial_extent, 1e-6)
    
    # 计算需要的网格数量，确保每个网格至少有90000点
    grid_count = max(2, int(np.ceil(points_count / MAX_POINTS_PER_SAMPLE)))
    # 确保网格在三个维度上均匀分布
    grid_dims = _calculate_grid_dimensions(grid_count, spatial_extent)
    
    print(f"网格维度: {grid_dims}")
    
    segments = []
    empty_grids = 0
    small_grids = 0
    
    # 第一次遍历：计算每个网格的点数
    grid_points = np.zeros(grid_dims, dtype=np.int32)
    for i in range(grid_dims[0]):
        for j in range(grid_dims[1]):
            for k in range(grid_dims[2]):
                boundaries = _get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
                mask = (
                    (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
                    (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
                    (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
                )
                grid_points[i, j, k] = np.sum(mask)
    
    print(f"网格点数统计完成")
    
    # 第二次遍历：合并点数不足的网格
    visited = np.zeros(grid_dims, dtype=bool)
    for i in range(grid_dims[0]):
        for j in range(grid_dims[1]):
            for k in range(grid_dims[2]):
                if visited[i, j, k]:
                    continue
                    
                if grid_points[i, j, k] >= MIN_POINTS_PER_SAMPLE:
                    # 点数足够的网格，直接添加
                    boundaries = _get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
                    mask = (
                        (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
                        (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
                        (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
                    )
                    segments.append(np.sum(mask))
                    visited[i, j, k] = True
                elif grid_points[i, j, k] > 0:
                    # 尝试与相邻网格合并
                    merged_indices = [(i, j, k)]
                    total_points = grid_points[i, j, k]
                    
                    # 检查相邻网格（6邻域）
                    neighbors = []
                    if i > 0: neighbors.append((i-1, j, k))
                    if i < grid_dims[0]-1: neighbors.append((i+1, j, k))
                    if j > 0: neighbors.append((i, j-1, k))
                    if j < grid_dims[1]-1: neighbors.append((i, j+1, k))
                    if k > 0: neighbors.append((i, j, k-1))
                    if k < grid_dims[2]-1: neighbors.append((i, j, k+1))
                    
                    # 按点数排序，优先合并点数多的网格
                    neighbors.sort(key=lambda idx: grid_points[idx], reverse=True)
                    
                    for ni, nj, nk in neighbors:
                        if visited[ni, nj, nk]:
                            continue
                        if total_points + grid_points[ni, nj, nk] <= MAX_POINTS_PER_SAMPLE:
                            merged_indices.append((ni, nj, nk))
                            total_points += grid_points[ni, nj, nk]
                            visited[ni, nj, nk] = True
                            if total_points >= MIN_POINTS_PER_SAMPLE:
                                break
                    
                    if total_points >= MIN_POINTS_PER_SAMPLE:
                        # 创建合并的网格掩码
                        merged_boundaries = _get_merged_boundaries(min_coord, spatial_extent, grid_dims, merged_indices)
                        mask = (
                            (coords[:, 0] >= merged_boundaries["start_x"]) & (coords[:, 0] < merged_boundaries["end_x"]) &
                            (coords[:, 1] >= merged_boundaries["start_y"]) & (coords[:, 1] < merged_boundaries["end_y"]) &
                            (coords[:, 2] >= merged_boundaries["start_z"]) & (coords[:, 2] < merged_boundaries["end_z"])
                        )
                        segments.append(np.sum(mask))
                    else:
                        # 仍然无法达到最小点数，记录为小网格
                        boundaries = _get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
                        mask = (
                            (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
                            (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
                            (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
                        )
                        segments.append(np.sum(mask))
                        small_grids += 1
                    visited[i, j, k] = True
                else:
                    empty_grids += 1
                    visited[i, j, k] = True
    
    print(f"生成的段数: {len(segments)}")
    print(f"空网格数: {empty_grids}")
    print(f"小网格数: {small_grids}")
    
    # 检查每个段是否满足最小点数要求
    valid_segments = 0
    for i, points in enumerate(segments):
        print(f"段 {i}: {points} 点")
        
        if points >= MIN_POINTS_PER_SAMPLE:
            valid_segments += 1
        elif points > 0:
            print(f"  警告: 段 {i} 只有 {points} 点，小于最小值 {MIN_POINTS_PER_SAMPLE}")
    
    print(f"有效段数 (>= {MIN_POINTS_PER_SAMPLE} 点): {valid_segments}")
    print(f"无效段数: {len(segments) - valid_segments}")
    
    return valid_segments == len(segments)

if __name__ == "__main__":
    success = test_merge_function()
    if success:
        print("✓ 测试通过: 所有段都满足最小点数要求")
    else:
        print("✗ 测试失败: 存在点数不足的段")