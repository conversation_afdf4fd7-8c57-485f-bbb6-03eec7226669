"""
PowScan Dataset

Author: Your Name
Please cite our work if the code is helpful to you.
"""

import os
import numpy as np
from .defaults import DefaultDataset
from .builder import DATASETS
from .powscan_constants import CLASS_NAMES, CLASS2ID, CLASS_IDS, UNLABELED_ID, IGNORE_INDEX, update_constants
from pointcept.utils.cache import shared_dict


@DATASETS.register_module()
class PowScanDataset(DefaultDataset):
    # 最大点数限制（调整为180000以确保拆分后每个样本至少有90000点）
    MAX_POINTS_PER_SAMPLE = 240000
    # 网格重叠比例
    GRID_OVERLAP_RATIO = 0.05
    # 最小点数阈值（确保每个样本至少有90000个点）
    MIN_POINTS_PER_SAMPLE = 60000
    
    def __init__(
        self,
        split="train",
        data_root="data/powscan",
        transform=None,
        test_mode=False,
        test_cfg=None,
        cache=False,
        ignore_index=IGNORE_INDEX,
        loop=1,
    ):
        # 更新全局常量
        update_constants(data_root)
        
        super(PowScanDataset, self).__init__(
            split=split,
            data_root=data_root,
            transform=transform,
            test_mode=test_mode,
            test_cfg=test_cfg,
            cache=cache,
            ignore_index=ignore_index,
            loop=loop,
        )
        
        # 设置有效的资产类型
        self.VALID_ASSETS = ["coord", "strength", "segment"]

    def get_data_list(self):
        """获取数据列表，支持大文件拆分"""
        if isinstance(self.split, str):
            split_file = os.path.join(self.data_root, self.split + ".txt")
            if os.path.exists(split_file):
                with open(split_file, 'r') as f:
                    original_data_list = [line.strip() for line in f.readlines()]
            else:
                # 如果没有分割文件，则使用所有处理后的数据
                original_data_list = []
                processed_dir = os.path.join(self.data_root, "processed")
                if os.path.exists(processed_dir):
                    for scene in os.listdir(processed_dir):
                        scene_dir = os.path.join(processed_dir, scene)
                        if os.path.isdir(scene_dir):
                            for fragment in os.listdir(scene_dir):
                                if fragment.endswith(".npy"):
                                    # 移除.npy扩展名
                                    fragment_name = fragment[:-4]
                                    original_data_list.append(os.path.join(scene_dir, fragment_name))
        else:
            raise NotImplementedError("PowScan dataset only supports string split")
        
        # 扩展数据列表以包含所有拆分样本
        expanded_data_list = []
        for data_path in original_data_list:
            # 检查文件是否需要拆分
            coord_path = data_path + ".npy"
            if os.path.exists(coord_path):
                data = np.load(coord_path)
                if data.shape[0] > self.MAX_POINTS_PER_SAMPLE:
                    # 对于大文件，添加多个虚拟路径（每个拆分一个）
                    coords = data[:, :3]
                    min_coord = np.min(coords, axis=0)
                    max_coord = np.max(coords, axis=0)
                    spatial_extent = max_coord - min_coord
                    
                    # 使用与_split_by_coordinate_grid相同的逻辑计算网格数量
                    grid_count = max(2, int(np.ceil(data.shape[0] / self.MAX_POINTS_PER_SAMPLE)))
                    grid_dims = self._calculate_grid_dimensions(grid_count, spatial_extent)
                    print(f"%%%%%Warning: split data set into {grid_dims}.")
                    
                    for i in range(grid_dims[0]):
                        for j in range(grid_dims[1]):
                            for k in range(grid_dims[2]):
                                # 为每个网格创建虚拟路径
                                segment_dict = self._extract_single_grid({"coord": coords, "name": data_path}, i, j, k)
                                virtual_path = f"{data_path}_grid_{i}_{j}_{k}"
                                if segment_dict["coord"].shape[0] >= self.MIN_POINTS_PER_SAMPLE:
                                    expanded_data_list.append(virtual_path)
                else:
                    # 检查点数是否足够
                    if data.shape[0] >= self.MIN_POINTS_PER_SAMPLE:
                        # 小文件直接添加
                        expanded_data_list.append(data_path)
                    else:
                        # 跳过点数不足的样本
                        print(f"Warning: Skipping {data_path} with only {data.shape[0]} points (less than {self.MIN_POINTS_PER_SAMPLE})")
                        continue
        
        return expanded_data_list

    def get_data(self, idx):
        """获取单个数据样本，支持大文件拆分和虚拟路径"""
        data_path = self.data_list[idx % len(self.data_list)]
        name = self.get_data_name(idx)

        if self.cache:
            cache_name = f"pointcept-{name}"
            return shared_dict(cache_name)

        try:
            # 检查是否为虚拟路径（包含_grid_）
            if "_grid_" in data_path:
                # 虚拟路径，需要从原始文件加载并提取指定网格
                # 提取原始文件路径和网格坐标
                base_path = data_path.rsplit("_grid_", 1)[0]
                grid_coords = data_path.split("_grid_")[1].split("_")
                i, j, k = map(int, grid_coords)

                # 加载原始数据
                original_data_dict = self._load_original_data(base_path)

                # 直接提取指定网格，避免全部分拆
                segment_dict = self._extract_single_grid(original_data_dict, i, j, k)

                # 验证结果
                if segment_dict["coord"].shape[0] == 0:
                    print(f"!!!!Warning: Empty grid extracted from {data_path}")
                    # 跳过空网格，返回None（后续会被过滤掉）
                    return None
                
                # 检查点数是否足够
                if segment_dict["coord"].shape[0] < self.MIN_POINTS_PER_SAMPLE:
                    print(f"####Warning: Grid {data_path} has only {segment_dict['coord'].shape[0]} points (less than {self.MIN_POINTS_PER_SAMPLE})")
                    # 跳过点数不足的网格，返回None（后续会被过滤掉）
                    return None

                return segment_dict
            else:
                # 普通路径，直接加载
                data_dict = self._load_original_data(data_path)

                # 检查点数并决定是否拆分（用于缓存模式）
                if data_dict["coord"].shape[0] > self.MAX_POINTS_PER_SAMPLE:
                    data_dicts = self._split_by_coordinate_grid(data_dict)
                    # 返回第一个拆分样本
                    if len(data_dicts) > 0 and data_dicts[0]["coord"].shape[0] > 0:
                        return data_dicts[0]
                    else:
                        print(f"Warning: Grid splitting failed for {data_path}")
                        return self._create_minimal_sample(name)
                else:
                    # 检查点数是否足够
                    if data_dict["coord"].shape[0] < self.MIN_POINTS_PER_SAMPLE:
                        print(f"Warning: Sample {data_path} has only {data_dict['coord'].shape[0]} points (less than {self.MIN_POINTS_PER_SAMPLE})")
                        # 仍然返回该样本，但记录警告
                    
                    return data_dict

        except Exception as e:
            print(f"Error loading data from {data_path}: {e}")
            # 返回一个最小的有效样本以避免训练崩溃
            return self._create_minimal_sample(name)

    def _load_original_data(self, data_path):
        """加载原始数据而不进行拆分"""
        data_dict = {}
        
        # 加载坐标、颜色和标签
        coord_path = data_path + ".npy"
        if os.path.exists(coord_path):
            data = np.load(coord_path)
            data_dict["coord"] = data[:, :3].astype(np.float32)
            
            # 自动识别数据格式版本
            num_cols = data.shape[1]
            if num_cols == 7:  # 原始格式: xyz(3) + rgb(3) + label(1)
                data_dict["color"] = data[:, 3:6].astype(np.float32)
                data_dict["segment"] = data[:, 6].astype(np.int32)
            elif num_cols == 8:  # 增强格式: xyz(3) + strength(1) + rgb(3) + label(1)
                data_dict["strength"] = data[:, 3:4].astype(np.float32)
                data_dict["color"] = data[:, 4:7].astype(np.float32)
                data_dict["segment"] = data[:, 7].astype(np.int32)
            elif num_cols == 5:  # 纯强度格式: xyz(3) + strength(1) + label(1)
                data_dict["strength"] = data[:, 3:4].astype(np.float32)
                data_dict["segment"] = data[:, 4].astype(np.int32)
            else:
                raise ValueError(f"Unsupported data format with {num_cols} columns")
        else:
            raise FileNotFoundError(f"Data file not found: {coord_path}")

        data_dict["name"] = os.path.basename(data_path)
        
        # 确保数据类型正确
        if "coord" in data_dict.keys():
            data_dict["coord"] = data_dict["coord"].astype(np.float32)
            
        if "strength" in data_dict.keys():
            data_dict["strength"] = data_dict["strength"].astype(np.float32)
            
        if "segment" in data_dict.keys():
            data_dict["segment"] = data_dict["segment"].reshape([-1]).astype(np.int32)
            # 确保标签ID与class_mapping.json一致
            # 未定义的标签映射为ignore_index
            if CLASS_IDS:  # 只有在有定义类别时才检查
                unique_labels = np.unique(data_dict["segment"])
                for label in unique_labels:
                    if label not in CLASS_IDS:
                        # 将未定义的标签映射为ignore_index
                        mask = data_dict["segment"] == label
                        data_dict["segment"][mask] = self.ignore_index
        else:
            data_dict["segment"] = (
                np.ones(data_dict["coord"].shape[0], dtype=np.int32) * self.ignore_index
            )
            
        return data_dict

    def _split_by_coordinate_grid(self, data_dict, min_points=180000):
        """基于坐标网格拆分大点云文件，确保每个样本至少有90000个点"""
        coords = data_dict["coord"]
        points_count = coords.shape[0]

        # 如果点数不够，直接返回原始数据
        if points_count <= min_points:
            return [data_dict]

        # 计算边界框和空间范围
        min_coord = np.min(coords, axis=0)
        max_coord = np.max(coords, axis=0)
        spatial_extent = max_coord - min_coord

        # 防止空间范围为零的情况
        spatial_extent = np.maximum(spatial_extent, 1e-6)

        # 计算需要的网格数量，确保每个网格至少有90000点
        grid_count = max(2, int(np.ceil(points_count / min_points)))
        # 确保网格在三个维度上均匀分布
        grid_dims = self._calculate_grid_dimensions(grid_count, spatial_extent)

        segments = []
        empty_grids = 0
        small_grids = 0

        # 第一次遍历：计算每个网格的点数
        grid_points = np.zeros(grid_dims, dtype=np.int32)
        for i in range(grid_dims[0]):
            for j in range(grid_dims[1]):
                for k in range(grid_dims[2]):
                    boundaries = self._get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
                    mask = (
                        (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
                        (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
                        (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
                    )
                    grid_points[i, j, k] = np.sum(mask)

        # 第二次遍历：合并点数不足的网格
        visited = np.zeros(grid_dims, dtype=bool)
        for i in range(grid_dims[0]):
            for j in range(grid_dims[1]):
                for k in range(grid_dims[2]):
                    if visited[i, j, k]:
                        continue
                        
                    if grid_points[i, j, k] >= self.MIN_POINTS_PER_SAMPLE:
                        # 点数足够的网格，直接添加
                        boundaries = self._get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
                        mask = (
                            (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
                            (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
                            (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
                        )
                        segment_dict = self._create_segment(data_dict, mask, i, j, k)
                        segments.append(segment_dict)
                        visited[i, j, k] = True
                    elif grid_points[i, j, k] > 0:
                        # 尝试与相邻网格合并
                        merged_mask = None
                        merged_indices = [(i, j, k)]
                        total_points = grid_points[i, j, k]
                        
                        # 检查相邻网格（6邻域）
                        neighbors = []
                        if i > 0: neighbors.append((i-1, j, k))
                        if i < grid_dims[0]-1: neighbors.append((i+1, j, k))
                        if j > 0: neighbors.append((i, j-1, k))
                        if j < grid_dims[1]-1: neighbors.append((i, j+1, k))
                        if k > 0: neighbors.append((i, j, k-1))
                        if k < grid_dims[2]-1: neighbors.append((i, j, k+1))
                        
                        # 按点数排序，优先合并点数多的网格
                        neighbors.sort(key=lambda idx: grid_points[idx], reverse=True)
                        
                        for ni, nj, nk in neighbors:
                            if visited[ni, nj, nk]:
                                continue
                            if total_points + grid_points[ni, nj, nk] <= self.MAX_POINTS_PER_SAMPLE:
                                merged_indices.append((ni, nj, nk))
                                total_points += grid_points[ni, nj, nk]
                                visited[ni, nj, nk] = True
                                if total_points >= self.MIN_POINTS_PER_SAMPLE:
                                    break
                        
                        if total_points >= self.MIN_POINTS_PER_SAMPLE:
                            # 创建合并的网格掩码
                            merged_boundaries = self._get_merged_boundaries(min_coord, spatial_extent, grid_dims, merged_indices)
                            mask = (
                                (coords[:, 0] >= merged_boundaries["start_x"]) & (coords[:, 0] < merged_boundaries["end_x"]) &
                                (coords[:, 1] >= merged_boundaries["start_y"]) & (coords[:, 1] < merged_boundaries["end_y"]) &
                                (coords[:, 2] >= merged_boundaries["start_z"]) & (coords[:, 2] < merged_boundaries["end_z"])
                            )
                            segment_dict = self._create_segment(data_dict, mask, i, j, k)
                            segment_dict["name"] = f"{data_dict['name']}_merged_{'_'.join([f'{x}_{y}_{z}' for x,y,z in merged_indices])}"
                            segments.append(segment_dict)
                        else:
                            # 仍然无法达到最小点数，尝试与更多网格合并或跳过
                            # 继续尝试合并更多相邻网格
                            for ni, nj, nk in neighbors:
                                if visited[ni, nj, nk]:
                                    continue
                                if total_points + grid_points[ni, nj, nk] <= self.MAX_POINTS_PER_SAMPLE:
                                    merged_indices.append((ni, nj, nk))
                                    total_points += grid_points[ni, nj, nk]
                                    visited[ni, nj, nk] = True
                                    if total_points >= self.MIN_POINTS_PER_SAMPLE:
                                        break
                            
                            if total_points >= self.MIN_POINTS_PER_SAMPLE:
                                # 创建合并的网格掩码
                                merged_boundaries = self._get_merged_boundaries(min_coord, spatial_extent, grid_dims, merged_indices)
                                mask = (
                                    (coords[:, 0] >= merged_boundaries["start_x"]) & (coords[:, 0] < merged_boundaries["end_x"]) &
                                    (coords[:, 1] >= merged_boundaries["start_y"]) & (coords[:, 1] < merged_boundaries["end_y"]) &
                                    (coords[:, 2] >= merged_boundaries["start_z"]) & (coords[:, 2] < merged_boundaries["end_z"])
                                )
                                segment_dict = self._create_segment(data_dict, mask, i, j, k)
                                segment_dict["name"] = f"{data_dict['name']}_merged_{'_'.join([f'{x}_{y}_{z}' for x,y,z in merged_indices])}"
                                segments.append(segment_dict)
                            else:
                                # 最终仍然无法达到最小点数，跳过这个网格
                                # 不创建小样本，避免影响训练
                                print(f"Warning: Skipping grid ({i},{j},{k}) with {total_points} points (insufficient for merging)")
                                small_grids += 1
                        visited[i, j, k] = True
                    else:
                        empty_grids += 1
                        visited[i, j, k] = True

        # 如果没有生成任何分割，则返回原始数据
        if len(segments) == 0:
            segments.append(data_dict)

        # 记录分割信息（用于调试）
        if empty_grids > 0:
            print(f"Warning: {empty_grids} empty grids found in {data_dict.get('name', 'unknown')}")
        if small_grids > 0:
            print(f"Warning: {small_grids} small grids (<{self.MIN_POINTS_PER_SAMPLE} points) found in {data_dict.get('name', 'unknown')}")

        return segments

    def _calculate_grid_dimensions(self, grid_count, spatial_extent):
        """计算网格在三个维度上的分布"""
        # 防止除零错误
        total_extent = np.sum(spatial_extent)
        if total_extent == 0:
            return [1, 1, 1]

        # 根据空间范围的比例分配网格维度
        # 使用立方根来更均匀地分布网格
        base_dim = max(1, round(grid_count ** (1/3)))

        # 计算每个维度的比例权重
        x_ratio = spatial_extent[0] / total_extent
        y_ratio = spatial_extent[1] / total_extent
        z_ratio = spatial_extent[2] / total_extent

        # 基于比例和立方根分配维度
        x_dim = max(1, round(base_dim * (x_ratio ** (1/3)) * 2))
        y_dim = max(1, round(base_dim * (y_ratio ** (1/3)) * 2))
        z_dim = max(1, round(grid_count / (x_dim * y_dim)))

        # 确保不超过目标网格数量
        while x_dim * y_dim * z_dim > grid_count and z_dim > 1:
            z_dim -= 1
        while x_dim * y_dim * z_dim > grid_count and y_dim > 1:
            y_dim -= 1
        while x_dim * y_dim * z_dim > grid_count and x_dim > 1:
            x_dim -= 1

        # 确保至少有一个网格
        if x_dim * y_dim * z_dim == 0:
            return [1, 1, 1]

        return [x_dim, y_dim, z_dim]

    def _extract_single_grid(self, data_dict, i, j, k):
        """提取单个网格样本，避免全部分拆"""
        coords = data_dict["coord"]
        points_count = coords.shape[0]
        
        # 计算边界框和空间范围
        min_coord = np.min(coords, axis=0)
        max_coord = np.max(coords, axis=0)
        spatial_extent = max_coord - min_coord
        
        # 计算网格数量（与get_data_list中一致）
        grid_count = max(2, int(np.ceil(points_count / self.MAX_POINTS_PER_SAMPLE)))
        grid_dims = self._calculate_grid_dimensions(grid_count, spatial_extent)
        
        # 确保网格坐标在范围内
        if i >= grid_dims[0] or j >= grid_dims[1] or k >= grid_dims[2]:
            # 如果坐标超出范围，返回第一个网格
            i, j, k = 0, 0, 0
        
        # 获取网格边界
        boundaries = self._get_grid_boundaries(min_coord, spatial_extent, grid_dims, i, j, k)
        
        # 选择在当前网格内的点
        mask = (
            (coords[:, 0] >= boundaries["start_x"]) & (coords[:, 0] < boundaries["end_x"]) &
            (coords[:, 1] >= boundaries["start_y"]) & (coords[:, 1] < boundaries["end_y"]) &
            (coords[:, 2] >= boundaries["start_z"]) & (coords[:, 2] < boundaries["end_z"])
        )
        
        # 使用_create_segment创建分段样本以确保一致性
        segment_dict = self._create_segment(data_dict, mask, i, j, k)
        return segment_dict

    def _create_segment(self, data_dict, mask, i, j, k):
        """从原始数据创建分段样本"""
        segment_dict = {}
        for key in data_dict:
            if key not in ["name", "split"] and hasattr(data_dict[key], '__len__'):
                segment_dict[key] = data_dict[key][mask].copy()
            else:
                segment_dict[key] = data_dict[key]
        
        # 添加网格信息到名称
        segment_dict["name"] = f"{data_dict['name']}_grid_{i}_{j}_{k}"
        return segment_dict

    def _get_grid_boundaries(self, min_coord, spatial_extent, grid_dims, i, j, k):
        """计算指定网格的边界（带重叠）"""
        cell_size = spatial_extent / grid_dims
        
        start_x = min_coord[0] + (i * cell_size[0]) - (self.GRID_OVERLAP_RATIO * cell_size[0])
        end_x = min_coord[0] + ((i + 1) * cell_size[0]) + (self.GRID_OVERLAP_RATIO * cell_size[0])
        
        start_y = min_coord[1] + (j * cell_size[1]) - (self.GRID_OVERLAP_RATIO * cell_size[1])
        end_y = min_coord[1] + ((j + 1) * cell_size[1]) + (self.GRID_OVERLAP_RATIO * cell_size[1])
        
        start_z = min_coord[2] + (k * cell_size[2]) - (self.GRID_OVERLAP_RATIO * cell_size[2])
        end_z = min_coord[2] + ((k + 1) * cell_size[2]) + (self.GRID_OVERLAP_RATIO * cell_size[2])
        
        return {
            "start_x": start_x,
            "end_x": end_x,
            "start_y": start_y,
            "end_y": end_y,
            "start_z": start_z,
            "end_z": end_z
        }

    def _get_merged_boundaries(self, min_coord, spatial_extent, grid_dims, indices):
        """计算合并网格的边界"""
        cell_size = spatial_extent / grid_dims
        
        # 计算合并网格的最小和最大坐标
        min_i = min(idx[0] for idx in indices)
        max_i = max(idx[0] for idx in indices)
        min_j = min(idx[1] for idx in indices)
        max_j = max(idx[1] for idx in indices)
        min_k = min(idx[2] for idx in indices)
        max_k = max(idx[2] for idx in indices)
        
        # 计算合并后的边界（考虑重叠）
        start_x = min_coord[0] + (min_i * cell_size[0]) - (self.GRID_OVERLAP_RATIO * cell_size[0])
        end_x = min_coord[0] + ((max_i + 1) * cell_size[0]) + (self.GRID_OVERLAP_RATIO * cell_size[0])
        
        start_y = min_coord[1] + (min_j * cell_size[1]) - (self.GRID_OVERLAP_RATIO * cell_size[1])
        end_y = min_coord[1] + ((max_j + 1) * cell_size[1]) + (self.GRID_OVERLAP_RATIO * cell_size[1])
        
        start_z = min_coord[2] + (min_k * cell_size[2]) - (self.GRID_OVERLAP_RATIO * cell_size[2])
        end_z = min_coord[2] + ((max_k + 1) * cell_size[2]) + (self.GRID_OVERLAP_RATIO * cell_size[2])
        
        return {
            "start_x": start_x,
            "end_x": end_x,
            "start_y": start_y,
            "end_y": end_y,
            "start_z": start_z,
            "end_z": end_z
        }

    def _create_minimal_sample(self, name):
        """创建一个最小的有效样本，用于错误恢复"""
        # 创建一个包含少量点的最小样本
        minimal_coords = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [0.0, 0.0, 1.0]
        ], dtype=np.float32)

        return {
            "coord": minimal_coords,
            "strength": np.ones((4, 1), dtype=np.float32),
            "segment": np.full(4, self.ignore_index, dtype=np.int32),
            "name": f"{name}_minimal"
        }

    def get_data_name(self, idx):
        """获取数据名称"""
        data_path = self.data_list[idx % len(self.data_list)]
        # 从路径中提取场景名和片段名
        path_parts = data_path.split(os.sep)
        if len(path_parts) >= 2:
            scene_name = path_parts[-2]
            fragment_name = path_parts[-1]
            return f"{scene_name}-{fragment_name}"
        return os.path.basename(data_path)