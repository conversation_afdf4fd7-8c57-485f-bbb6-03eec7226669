# PowScan数据集技术说明

## 概述

PowScan数据集使用先进的网格拆分和合并算法处理大规模电力巡检点云数据，确保每个训练样本都具有足够的点数（≥90000）来支持深度学习模型的训练。

## 核心参数

### 关键常量定义
```python
# pointcept/datasets/powscan.py
MAX_POINTS_PER_SAMPLE = 180000  # 单个样本最大点数
MIN_POINTS_PER_SAMPLE = 90000   # 单个样本最小点数
GRID_SIZE = 25.0               # 网格尺寸（米）
```

### 配置建议
在训练配置文件中建议使用以下参数：
```yaml
transform:
  - type: SphereCrop
    point_max: 60000  # 训练时裁剪到60000点
    mode: random
```

## 网格拆分算法

### 1. 坐标范围计算
```python
def _get_coordinate_range(self, data_dict):
    """计算点云的坐标范围"""
    coord = data_dict["coord"]
    min_coord = coord.min(0)
    max_coord = coord.max(0)
    return min_coord, max_coord
```

### 2. 网格划分
```python
def _split_by_coordinate_grid(self, data_dict):
    """按坐标网格拆分点云"""
    coord = data_dict["coord"]
    min_coord, max_coord = self._get_coordinate_range(data_dict)
    
    # 计算网格数量
    x_size = max_coord[0] - min_coord[0]
    y_size = max_coord[1] - min_coord[1]
    num_x = math.ceil(x_size / self.GRID_SIZE)
    num_y = math.ceil(y_size / self.GRID_SIZE)
    
    # 创建网格掩码
    grid_masks = []
    for i in range(num_x):
        for j in range(num_y):
            x_min = min_coord[0] + i * self.GRID_SIZE
            x_max = min_coord[0] + (i + 1) * self.GRID_SIZE
            y_min = min_coord[1] + j * self.GRID_SIZE
            y_max = min_coord[1] + (j + 1) * self.GRID_SIZE
            
            mask = (coord[:, 0] >= x_min) & (coord[:, 0] < x_max) & \
                   (coord[:, 1] >= y_min) & (coord[:, 1] < y_max)
            grid_masks.append((mask, (i, j)))
    
    return grid_masks
```

## 网格合并策略

### 1. 合并算法
```python
def _get_merged_boundaries(self, grid_masks, coord):
    """计算合并后的网格边界"""
    merged_boundaries = []
    
    for mask, (i, j) in grid_masks:
        if mask.sum() == 0:
            continue  # 跳过空网格
            
        current_points = mask.sum()
        
        # 如果当前网格点数不足，尝试合并相邻网格
        if current_points < self.MIN_POINTS_PER_SAMPLE:
            merged_mask = mask.copy()
            merged_indices = [(i, j)]
            
            # 优先合并X轴方向的相邻网格
            for dx in [-1, 1]:
                neighbor_idx = self._find_grid_index(grid_masks, i + dx, j)
                if neighbor_idx != -1:
                    neighbor_mask, (ni, nj) = grid_masks[neighbor_idx]
                    if neighbor_mask.sum() > 0:  # 确保邻居非空
                        merged_mask = merged_mask | neighbor_mask
                        merged_indices.append((ni, nj))
            
            # 如果X轴合并后仍不足，尝试Y轴方向
            if merged_mask.sum() < self.MIN_POINTS_PER_SAMPLE:
                for dy in [-1, 1]:
                    neighbor_idx = self._find_grid_index(grid_masks, i, j + dy)
                    if neighbor_idx != -1:
                        neighbor_mask, (ni, nj) = grid_masks[neighbor_idx]
                        if neighbor_mask.sum() > 0:
                            merged_mask = merged_mask | neighbor_mask
                            merged_indices.append((ni, nj))
            
            # 计算合并后的边界
            if merged_mask.sum() >= self.MIN_POINTS_PER_SAMPLE:
                merged_coord = coord[merged_mask]
                min_coord = merged_coord.min(0)
                max_coord = merged_coord.max(0)
                merged_boundaries.append((merged_mask, min_coord, max_coord))
        else:
            # 单个网格已满足要求
            merged_coord = coord[mask]
            min_coord = merged_coord.min(0)
            max_coord = merged_coord.max(0)
            merged_boundaries.append((mask, min_coord, max_coord))
    
    return merged_boundaries
```

### 2. 网格索引查找
```python
def _find_grid_index(self, grid_masks, target_i, target_j):
    """查找特定网格索引"""
    for idx, (mask, (i, j)) in enumerate(grid_masks):
        if i == target_i and j == target_j:
            return idx
    return -1
```

## 样本过滤机制

### 1. 质量检查
```python
def get_data(self, idx):
    """获取数据并进行质量检查"""
    data_dict = super().get_data(idx)
    
    if data_dict is None:
        return None
        
    # 检查点数是否满足要求
    num_points = len(data_dict["coord"])
    if num_points < self.MIN_POINTS_PER_SAMPLE:
        print(f"警告: 样本 {idx} 点数不足: {num_points} < {self.MIN_POINTS_PER_SAMPLE}")
        return None
        
    if num_points > self.MAX_POINTS_PER_SAMPLE:
        print(f"警告: 样本 {idx} 点数超出: {num_points} > {self.MAX_POINTS_PER_SAMPLE}")
        # 可以选择裁剪或跳过
        
    return data_dict
```

## 性能优化

### 1. 内存管理
- 使用多进程并行处理
- 实现懒加载机制
- 支持数据缓存

### 2. 计算优化
- 使用向量化操作代替循环
- 提前终止不必要的计算
- 优化网格索引查找

## 使用建议

### 训练配置
```yaml
# 建议的训练配置
batch_size: 12
point_max: 60000  # 训练时裁剪点数
grid_size: 0.04   # 训练网格尺寸
```

### 验证配置
```yaml
# 建议的验证配置
point_max: 40000   # 验证时裁剪点数
grid_size: 0.25    # 验证网格尺寸
```

## 故障排除

### 常见问题
1. **内存不足**: 减少`--workers`参数或使用`--max-samples`
2. **点数不足**: 检查原始数据质量，确保点云密度足够
3. **网格合并失败**: 调整`GRID_SIZE`参数或检查坐标范围

### 调试方法
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查单个样本
sample = dataset.get_data(0)
print(f"点数: {len(sample['coord'])}")
```

## 版本历史

- **v1.0**: 初始版本，基础网格拆分
- **v1.1**: 添加网格合并算法
- **v1.2**: 添加最小点数验证（≥90000）
- **v1.3**: 优化内存管理和性能

## 参考文献

- [Pointcept框架文档](https://github.com/Pointcept/Pointcept)
- [点云数据处理最佳实践]
- [深度学习中的点云采样策略]