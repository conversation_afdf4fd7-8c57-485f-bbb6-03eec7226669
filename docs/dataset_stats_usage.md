# 数据集统计工具使用指南

## 工具概述

`tools/dataset_stats.py` 是一个用于统计点云数据集样本信息的工具，可以统计：
- 每个样本的点数
- 三维坐标范围 (X, Y, Z轴)
- 三维尺寸范围
- 体积信息
- 验证样本质量（最小点数要求）

## PowScan数据集特殊说明

PowScan数据集使用网格拆分算法处理大规模点云场景，具有以下特点：

### 网格拆分参数
- **MAX_POINTS_PER_SAMPLE**: 180000（单个样本最大点数）
- **MIN_POINTS_PER_SAMPLE**: 90000（单个样本最小点数）
- **GRID_SIZE**: 25.0米（网格尺寸）

### 网格合并策略
当网格拆分产生点数不足的样本时，系统会自动合并相邻网格：
1. 优先合并X轴方向的相邻网格
2. 如果X轴合并后仍不足，继续合并Y轴方向的网格
3. 确保合并后的网格点数满足最小要求（≥90000点）
4. 过滤掉无法合并的空网格和点数过少的样本

## 安装依赖

```bash
pip install numpy tqdm
```

## 基本用法

### 1. 统计PowScan数据集

```bash
# 统计训练集
python tools/dataset_stats.py --dataset powscan --split train --output train_stats.json

# 统计验证集
python tools/dataset_stats.py --dataset powscan --split val --output val_stats.json

# 统计测试集
python tools/dataset_stats.py --dataset powscan --split test --output test_stats.json
```

### 2. 自定义数据根目录

```bash
python tools/dataset_stats.py --dataset powscan --data-root /path/to/your/data --split train --output stats.json
```

### 3. 限制样本数量（用于测试）

```bash
python tools/dataset_stats.py --dataset powscan --split train --max-samples 100 --output test_stats.json
```

### 4. 调整并行工作进程数

```bash
python tools/dataset_stats.py --dataset powscan --split train --workers 8 --output stats.json
```

## 输出格式

工具会生成一个JSON文件，包含以下信息：

```json
{
  "total_samples": 1000,
  "point_stats": {
    "min": 100,
    "max": 50000,
    "mean": 12000.5,
    "median": 11000.0,
    "std": 2500.2,
    "percentile_25": 8000.0,
    "percentile_75": 15000.0
  },
  "volume_stats": {
    "min": 10.5,
    "max": 500.0,
    "mean": 120.3,
    "median": 110.0
  },
  "extent_stats": {
    "x": {"min": 1.0, "max": 10.0, "mean": 5.0},
    "y": {"min": 1.0, "max": 8.0, "mean": 4.0},
    "z": {"min": 0.5, "max": 5.0, "mean": 2.5}
  },
  "quality_check": {
    "min_points_threshold": 90000,
    "samples_below_threshold": 0,
    "empty_samples": 0,
    "quality_status": "PASS"
  },
  "samples": [
    {
      "name": "sample_001",
      "num_points": 12000,
      "min_x": 0.0, "min_y": 0.0, "min_z": 0.0,
      "max_x": 10.0, "max_y": 8.0, "max_z": 3.0,
      "extent_x": 10.0, "extent_y": 8.0, "extent_z": 3.0,
      "volume": 240.0,
      "quality_status": "PASS"  // 或 "FAIL" 如果点数不足
    },
    // ... 更多样本
  ]
}
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--dataset` | str | `powscan` | 数据集名称 |
| `--split` | str | `train` | 数据分割 (train/val/test) |
| `--data-root` | str | `data/powscan` | 数据集根目录 |
| `--output` | str | `dataset_stats.json` | 输出文件路径 |
| `--workers` | int | `4` | 并行工作进程数 |
| `--max-samples` | int | `None` | 最大样本数限制 |
| `--config` | str | `None` | 自定义配置文件路径 |

## 使用自定义配置

如果需要使用其他数据集或自定义配置，可以创建YAML配置文件：

```yaml
# custom_config.yaml
type: PowScanDataset
split: train
data_root: data/powscan
transform:
  - type: CenterShift
    apply_z: true
  - type: NormalizeColor
  - type: ToTensor
  - type: Collect
    keys: ('coord', 'segment')
    feat_keys: ['coord', 'color']
test_mode: false
cache: false
ignore_index: -1
```

然后使用配置文件运行：

```bash
python tools/dataset_stats.py --config custom_config.yaml --output stats.json
```

## 质量验证

### PowScan数据集质量要求
- **最小点数**: 每个样本必须包含至少90000个点
- **空样本过滤**: 自动过滤掉点数为0的样本
- **网格合并**: 自动合并相邻网格以满足最小点数要求

### 验证方法
```bash
# 运行统计并验证质量
python tools/dataset_stats.py --dataset powscan --split train --output train_stats.json

# 检查质量状态
cat train_stats.json | grep "quality_status"
```

### 质量状态说明
- **PASS**: 所有样本满足最小点数要求
- **WARNING**: 存在少量样本接近阈值
- **FAIL**: 存在样本不满足最小点数要求

## 性能建议

1. **内存使用**: 对于大型数据集，建议使用 `--max-samples` 参数限制样本数量
2. **并行处理**: 根据CPU核心数调整 `--workers` 参数
3. **存储空间**: 输出文件可能较大，建议使用SSD存储

## 错误处理

- 如果遇到内存不足错误，减少 `--workers` 或使用 `--max-samples`
- 如果数据集路径错误，检查 `--data-root` 参数
- 如果遇到导入错误，确保pointcept包已正确安装
- 如果质量验证失败，检查数据集预处理是否正确

## 示例输出

运行后会显示实时进度和最终统计摘要：

```
开始收集 1000 个样本的统计信息...
处理样本: 100%|██████████| 1000/1000 [02:30<00:00, 6.67样本/s]

=== 数据集统计摘要 ===
样本总数: 1000
点数范围: 100 - 50000
平均点数: 12000.5
X轴范围: 1.00 - 10.00 m
Y轴范围: 1.00 - 8.00 m
Z轴范围: 0.50 - 5.00 m

详细统计已保存到: stats.json
```

## 进阶用法

### 批量处理多个分割

```bash
#!/bin/bash
for split in train val test; do
    echo "Processing $split split..."
    python tools/dataset_stats.py --dataset powscan --split $split --output ${split}_stats.json
done
```

### 与其他工具集成

统计结果可以用于：
- 数据集分析和可视化
- 训练参数调优
- 内存使用预估
- 数据预处理策略制定