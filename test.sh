#!/usr/bin/bash

# Create logs directory if it doesn't exist
mkdir -p logs

# Generate timestamp for log files
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/test_${TIMESTAMP}.log"
ERROR_LOG_FILE="logs/test_error_${TIMESTAMP}.log"

NUM_GPU=1 \
DATASET_NAME=powscan \
CONFIG_NAME=semseg-pt-v2m2-0-lovasz \
EXP_NAME=default \
ARGS="-g ${NUM_GPU} -d ${DATASET_NAME} -c ${CONFIG_NAME} -n ${EXP_NAME}"

echo "Starting testing at $(date)" | tee -a "$LOG_FILE"
echo "Arguments: $ARGS" | tee -a "$LOG_FILE"

# Run testing with both stdout and stderr logging
sh scripts/test.sh ${ARGS} 2>&1 | tee -a "$LOG_FILE"

# Capture exit code
EXIT_CODE=${PIPESTATUS[0]}

# Log completion status
if [ $EXIT_CODE -eq 0 ]; then
    echo "Testing completed successfully at $(date)" | tee -a "$LOG_FILE"
else
    echo "Testing failed with exit code $EXIT_CODE at $(date)" | tee -a "$LOG_FILE" | tee -a "$ERROR_LOG_FILE"
fi

echo "Full log saved to: $LOG_FILE"
if [ $EXIT_CODE -ne 0 ]; then
    echo "Error log saved to: $ERROR_LOG_FILE"
fi

exit $EXIT_CODE