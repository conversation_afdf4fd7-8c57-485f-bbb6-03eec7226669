_base_ = ["../_base_/default_runtime.py"]
# misc custom setting
batch_size = 10
mix_prob = 0.8
empty_cache = False
enable_amp = True

# model settings
model = dict(
    type="DefaultSegmentor",
    backbone=dict(
        type="PT-v2m2",
        in_channels=4,  # 修改输入通道为xyz+strength (3+1)
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend="interp",
    ),
    criteria=[
        dict(
            type="CrossEntropyLoss",
            weight=[
                0,
                5.753576339147521,
                0.3181036781804986,
                4.6331033270414075,
                5.688047109372652,
                4.1993555920488115,
                6.285231158810844,
                5.624712115286278,
                1.4943104479420062,
                5.169177961926781,
                7.75380462244782,
                8.642659322546105,
            ],
            loss_weight=1.0,
            ignore_index=0,
        ),
        dict(type="LovaszLoss", mode="multiclass", loss_weight=1.0, ignore_index=0),
    ],
)

# scheduler settings
epoch = 3000
optimizer = dict(type="AdamW", lr=0.002, weight_decay=0.005)
scheduler = dict(
    type="OneCycleLR",
    max_lr=optimizer["lr"],
    pct_start=0.04,
    anneal_strategy="cos",
    div_factor=10.0,
    final_div_factor=100.0,
)

# dataset settings
dataset_type = "PowScanDataset"
data_root = "data/powscan"

data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        "unlabeled",
        "铁塔",
        "中等植被点",
        "公路",
        "临时建筑物",
        "建筑物点",
        "交叉跨越下",
        "其他线路",
        "地面点",
        "导线",
        "其他",
        "交叉跨越上",
    ],
    train=dict(
        type=dataset_type,
        split="train",
        data_root=data_root,
        transform=[
            dict(type="CenterShift", apply_z=True),
            dict(type="RandomScale", scale=[0.9, 1.1]),
            dict(type="RandomFlip", p=0.5),
            dict(type="RandomJitter", sigma=0.005, clip=0.02),
            dict(
                type="GridSample",
                grid_size=0.04,
                hash_type="fnv",
                mode="train",
                return_grid_coord=True,
            ),
            dict(type="SphereCrop", point_max=60000, mode="random"),
            dict(type="CenterShift", apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0), 
            dict(type="ToTensor"),
            dict(
                type="Collect",
                # keys=("coord", "grid_coord", "segment"),
                keys=("coord", "segment"),
                feat_keys=["coord", "strength"],  # 修改特征键名
            ),
        ],
        test_mode=False,
    ),
    val=dict(
        type=dataset_type,
        split="val",
        data_root=data_root,
        transform=[
            dict(type="CenterShift", apply_z=True),
            dict(type="Copy", keys_dict={"segment": "origin_segment"}),
            dict(
                type="GridSample",
                grid_size=0.04,
                hash_type="fnv",
                mode="train",
                return_grid_coord=True,
                return_inverse=True,
            ),
            dict(type="SphereCrop", point_max=60000, mode="random"),
            dict(type="CenterShift", apply_z=False),
            dict(type="ToTensor"),
            dict(
                type="Collect",
                keys=("coord", "segment", "origin_segment"),
                feat_keys=["coord", "strength"],  # 修改特征键名
            ),
        ],
        test_mode=False,
    ),
    test=dict(
        type=dataset_type,
        split="test",
        data_root=data_root,
        transform=[dict(type="CenterShift", apply_z=True), dict(type="NormalizeColor")],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type="GridSample",
                grid_size=0.3,
                hash_type="fnv",
                mode="test",
                return_grid_coord=True,
            ),
            crop=None,
            post_transform=[
                dict(type="CenterShift", apply_z=False),
                dict(type="ToTensor"),
                dict(
                    type="Collect",
                    keys=("coord", "grid_coord", "index"),
                    feat_keys=("coord", "strength"),  # 修改特征键名
                ),
            ],
            aug_transform=[
                [dict(type="RandomScale", scale=[1, 1])],
            ],
        ),
    ),
)
