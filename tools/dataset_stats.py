"""
Dataset Statistics Tool for Pointcept
统计指定数据集的单个样本点数和三维尺寸范围

Author: Roo
Usage: python tools/dataset_stats.py --dataset powscan --split train --output stats.json
"""

import os
import sys
import json
import argparse
import numpy as np
from concurrent.futures import ProcessPoolExecutor, as_completed
from collections import defaultdict

# 可选依赖：tqdm用于进度显示
try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    # 简单的进度显示替代方案
    def tqdm(iterable, **kwargs):
        return iterable

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from pointcept.datasets import build_dataset
    from pointcept.utils.logger import get_root_logger
except ImportError:
    print("Warning: Pointcept package not found, using standalone mode")


class DatasetStatsCollector:
    """数据集统计收集器"""
    
    def __init__(self, dataset_cfg, num_workers=4, max_samples=None):
        """
        初始化统计收集器
        
        Args:
            dataset_cfg: 数据集配置字典
            num_workers: 并行工作进程数
            max_samples: 最大样本数（用于测试）
        """
        self.dataset_cfg = dataset_cfg
        self.num_workers = num_workers
        self.max_samples = max_samples
        self.stats = defaultdict(list)
        
    def _get_sample_stats(self, idx, dataset):
        """获取单个样本的统计信息"""
        try:
            data = dataset.get_data(idx)
            if data is None:
                return None
                
            coord = data.get('coord')
            if coord is None or len(coord) == 0:
                return None
                
            # 计算统计信息
            num_points = len(coord)
            min_coord = np.min(coord, axis=0)
            max_coord = np.max(coord, axis=0)
            extent = max_coord - min_coord
            
            return {
                'name': data.get('name', f'sample_{idx}'),
                'num_points': num_points,
                'min_x': float(min_coord[0]),
                'min_y': float(min_coord[1]),
                'min_z': float(min_coord[2]),
                'max_x': float(max_coord[0]),
                'max_y': float(max_coord[1]),
                'max_z': float(max_coord[2]),
                'extent_x': float(extent[0]),
                'extent_y': float(extent[1]),
                'extent_z': float(extent[2]),
                'volume': float(extent[0] * extent[1] * extent[2])
            }
            
        except Exception as e:
            print(f"Error processing sample {idx}: {e}")
            return None
    
    def collect_stats(self):
        """收集数据集统计信息"""
        # 构建数据集
        dataset = build_dataset(self.dataset_cfg)
        total_samples = len(dataset)
        
        if self.max_samples:
            total_samples = min(total_samples, self.max_samples)
        
        print(f"开始收集 {total_samples} 个样本的统计信息...")
        
        # 使用多进程并行处理
        results = []
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            futures = []
            
            for idx in range(total_samples):
                futures.append(executor.submit(self._get_sample_stats, idx, dataset))
            
            # 显示进度
            if HAS_TQDM:
                progress_iter = tqdm(as_completed(futures), total=total_samples, desc="处理样本")
            else:
                print(f"开始处理 {total_samples} 个样本...")
                progress_iter = as_completed(futures)
            
            for future in progress_iter:
                result = future.result()
                if result:
                    results.append(result)
                    
            if not HAS_TQDM:
                print(f"处理完成，收集到 {len(results)} 个有效样本")
        
        return results
    
    def analyze_stats(self, stats_results):
        """分析统计结果"""
        if not stats_results:
            return {}
        
        # 基本统计
        num_points = [s['num_points'] for s in stats_results]
        volumes = [s['volume'] for s in stats_results]
        extents_x = [s['extent_x'] for s in stats_results]
        extents_y = [s['extent_y'] for s in stats_results]
        extents_z = [s['extent_z'] for s in stats_results]
        
        analysis = {
            'total_samples': len(stats_results),
            'point_stats': {
                'min': int(np.min(num_points)),
                'max': int(np.max(num_points)),
                'mean': float(np.mean(num_points)),
                'median': float(np.median(num_points)),
                'std': float(np.std(num_points)),
                'percentile_25': float(np.percentile(num_points, 25)),
                'percentile_75': float(np.percentile(num_points, 75))
            },
            'volume_stats': {
                'min': float(np.min(volumes)),
                'max': float(np.max(volumes)),
                'mean': float(np.mean(volumes)),
                'median': float(np.median(volumes))
            },
            'extent_stats': {
                'x': {
                    'min': float(np.min(extents_x)),
                    'max': float(np.max(extents_x)),
                    'mean': float(np.mean(extents_x))
                },
                'y': {
                    'min': float(np.min(extents_y)),
                    'max': float(np.max(extents_y)),
                    'mean': float(np.mean(extents_y))
                },
                'z': {
                    'min': float(np.min(extents_z)),
                    'max': float(np.max(extents_z)),
                    'mean': float(np.mean(extents_z))
                }
            },
            'samples': stats_results  # 包含所有样本的详细数据
        }
        
        return analysis


def create_powscan_config(data_root="data/powscan", split="train"):
    """创建PowScan数据集配置"""
    return {
        'type': 'PowScanDataset',
        'split': split,
        'data_root': data_root,
        'transform': [
            {'type': 'CenterShift', 'apply_z': True},
            {'type': 'NormalizeColor'},
            {'type': 'ToTensor'},
            {
                'type': 'Collect',
                'keys': ('coord', 'segment'),
                'feat_keys': ['coord', 'color']
            }
        ],
        'test_mode': False,
        'cache': False,
        'ignore_index': -1
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据集统计工具")
    parser.add_argument("--dataset", type=str, default="powscan", 
                       help="数据集名称 (powscan, scannet, etc.)")
    parser.add_argument("--split", type=str, default="train", 
                       help="数据分割 (train, val, test)")
    parser.add_argument("--data-root", type=str, default="data/powscan", 
                       help="数据集根目录")
    parser.add_argument("--output", type=str, default="dataset_stats.json", 
                       help="输出文件路径")
    parser.add_argument("--workers", type=int, default=4, 
                       help="并行工作进程数")
    parser.add_argument("--max-samples", type=int, default=None,
                       help="最大样本数（用于测试）")
    parser.add_argument("--config", type=str, default=None,
                       help="自定义配置文件路径")
    
    args = parser.parse_args()
    
    # 创建数据集配置
    if args.config:
        # 从文件加载配置
        import yaml
        with open(args.config, 'r') as f:
            dataset_cfg = yaml.safe_load(f)
    else:
        # 使用默认配置
        if args.dataset.lower() == "powscan":
            dataset_cfg = create_powscan_config(args.data_root, args.split)
        else:
            print(f"不支持的数据集类型: {args.dataset}")
            return
    
    # 创建统计收集器
    collector = DatasetStatsCollector(dataset_cfg, args.workers, args.max_samples)
    
    # 收集统计信息
    stats_results = collector.collect_stats()
    
    if not stats_results:
        print("没有收集到有效的统计信息")
        return
    
    # 分析统计结果
    analysis = collector.analyze_stats(stats_results)
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    
    # 打印摘要
    print(f"\n=== 数据集统计摘要 ===")
    print(f"样本总数: {analysis['total_samples']}")
    print(f"点数范围: {analysis['point_stats']['min']} - {analysis['point_stats']['max']}")
    print(f"平均点数: {analysis['point_stats']['mean']:.1f}")
    print(f"X轴范围: {analysis['extent_stats']['x']['min']:.2f} - {analysis['extent_stats']['x']['max']:.2f} m")
    print(f"Y轴范围: {analysis['extent_stats']['y']['min']:.2f} - {analysis['extent_stats']['y']['max']:.2f} m")
    print(f"Z轴范围: {analysis['extent_stats']['z']['min']:.2f} - {analysis['extent_stats']['z']['max']:.2f} m")
    print(f"\n详细统计已保存到: {args.output}")


if __name__ == "__main__":
    main()