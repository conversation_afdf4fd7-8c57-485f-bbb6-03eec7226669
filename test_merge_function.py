#!/usr/bin/env python3
"""测试网格合并功能"""

import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pointcept.datasets.powscan import PowScanDataset

def test_merge_function():
    """测试网格合并功能"""
    print("测试网格合并功能...")
    
    # 创建一个模拟数据集实例
    dataset = PowScanDataset(
        split="train",
        data_root="/tmp/test",
        transform=None,
        test_mode=False
    )
    
    # 创建测试数据
    test_data = {
        "coord": np.random.rand(200000, 3) * 100,  # 200000个点，分布在100x100x100的空间
        "color": np.random.rand(200000, 3),
        "normal": np.random.rand(200000, 3),
        "semantic_gt": np.random.randint(0, 20, 200000),
        "instance_gt": np.random.randint(0, 100, 200000),
        "name": "test_sample",
        "split": "train"
    }
    
    print(f"原始点数: {test_data['coord'].shape[0]}")
    
    # 测试网格拆分
    segments = dataset._split_by_coordinate_grid(test_data)
    
    print(f"生成的段数: {len(segments)}")
    
    # 检查每个段是否满足最小点数要求
    valid_segments = 0
    for i, segment in enumerate(segments):
        points_count = segment["coord"].shape[0]
        print(f"段 {i}: {points_count} 点")
        
        if points_count >= dataset.MIN_POINTS_PER_SAMPLE:
            valid_segments += 1
        elif points_count > 0:
            print(f"  警告: 段 {i} 只有 {points_count} 点，小于最小值 {dataset.MIN_POINTS_PER_SAMPLE}")
    
    print(f"有效段数 (>= {dataset.MIN_POINTS_PER_SAMPLE} 点): {valid_segments}")
    print(f"无效段数: {len(segments) - valid_segments}")
    
    return valid_segments == len(segments)

if __name__ == "__main__":
    success = test_merge_function()
    if success:
        print("✓ 测试通过: 所有段都满足最小点数要求")
    else:
        print("✗ 测试失败: 存在点数不足的段")