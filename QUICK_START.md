# 数据集统计工具快速指南

## 快速开始

### 1. 统计PowScan训练集（前100个样本用于测试）
```bash
python tools/dataset_stats.py --dataset powscan --split train --max-samples 100 --output train_stats_100.json
```

### 2. 统计完整训练集（需要较长时间）
```bash
python tools/dataset_stats.py --dataset powscan --split train --output train_stats_full.json
```

### 3. 统计验证集
```bash
python tools/dataset_stats.py --dataset powscan --split val --output val_stats.json
```

### 4. 统计测试集
```bash
python tools/dataset_stats.py --dataset powscan --split test --output test_stats.json
```

## 输出示例

运行后会生成JSON文件，包含：
- 总体统计（点数范围、平均值、标准差等）
- 三维尺寸统计（X/Y/Z轴范围）
- 每个样本的详细数据

## 性能提示

- 使用 `--max-samples` 参数限制样本数量进行测试
- 使用 `--workers` 调整并行进程数（默认4）
- 完整数据集统计可能需要较长时间（1453个样本）

## 查看结果

```bash
# 查看统计摘要
cat train_stats.json | jq '.point_stats'
cat train_stats.json | jq '.extent_stats'

# 查看前5个样本详情
cat train_stats.json | jq '.samples[0:5]'
```

## 典型输出

```json
{
  "total_samples": 1453,
  "point_stats": {
    "min": 42276,
    "max": 381457,
    "mean": 157434.7,
    "median": 122585.5,
    "std": 115968.3
  },
  "extent_stats": {
    "x": {"min": 8.31, "max": 12.31, "mean": 10.84},
    "y": {"min": 24.25, "max": 26.25, "mean": 25.60},
    "z": {"min": 12.88, "max": 23.96, "mean": 15.35}
  }
}
```

## 注意事项

- 确保数据目录 `data/powscan` 存在且包含预处理后的数据
- 工具会自动处理网格拆分样本
- 输出文件可能较大，建议使用SSD存储