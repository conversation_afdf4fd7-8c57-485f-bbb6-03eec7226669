#!/usr/bin/bash
export CUDA_LAUNCH_BLOCKING=1

# Create logs directory if it doesn't exist
mkdir -p logs

# Generate timestamp for log files
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/run_${TIMESTAMP}.log"
ERROR_LOG_FILE="logs/run_error_${TIMESTAMP}.log"

NUM_GPU=2 \
DATASET_NAME=powscan \
CONFIG_NAME=semseg-pt-v2m2-2-lovasz \
EXP_NAME=splitSections \
IS_RESTORE=true \
ARGS="-g ${NUM_GPU} -d ${DATASET_NAME} -c ${CONFIG_NAME} -n ${EXP_NAME} -r ${IS_RESTORE}"

echo "Starting training at $(date)" | tee -a "$LOG_FILE"
echo "Arguments: $ARGS" | tee -a "$LOG_FILE"

# Run training with both stdout and stderr logging
sh scripts/train.sh ${ARGS} 2>&1 | tee -a "$LOG_FILE"

# Capture exit code
EXIT_CODE=${PIPESTATUS[0]}

# Log completion status
if [ $EXIT_CODE -eq 0 ]; then
    echo "Training completed successfully at $(date)" | tee -a "$LOG_FILE"
else
    echo "Training failed with exit code $EXIT_CODE at $(date)" | tee -a "$LOG_FILE" | tee -a "$ERROR_LOG_FILE"
fi

echo "Full log saved to: $LOG_FILE"
if [ $EXIT_CODE -ne 0 ]; then
    echo "Error log saved to: $ERROR_LOG_FILE"
fi

exit $EXIT_CODE
