#!/usr/bin/env python3
"""
测试PowScan数据集修改后的功能
验证每个样本至少有90000个点
"""

import os
import sys
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pointcept.datasets.powscan import PowScanDataset

def test_powscan_modifications():
    """测试PowScan数据集修改后的功能"""
    print("测试PowScan数据集修改...")
    
    # 创建一个测试数据集实例
    dataset = PowScanDataset(
        split="train",
        data_root="/home/<USER>/Workspace/Pointcept/data/powscan",  # 根据实际情况调整路径
        transform=None,
        test_mode=False,
        cache=False
    )
    
    print(f"MAX_POINTS_PER_SAMPLE: {dataset.MAX_POINTS_PER_SAMPLE}")
    print(f"MIN_POINTS_PER_SAMPLE: {dataset.MIN_POINTS_PER_SAMPLE}")
    print(f"GRID_OVERLAP_RATIO: {dataset.GRID_OVERLAP_RATIO}")
    
    # 测试_split_by_coordinate_grid方法
    print("\n测试_split_by_coordinate_grid方法...")
    
    # 创建一个测试数据字典
    test_data = {
        "name": "test_sample",
        "coord": np.random.rand(250000, 3) * 10.0,  # 25万个点
        "color": np.random.rand(250000, 3),
        "segment": np.random.randint(0, 20, 250000)
    }
    
    # 测试拆分
    segments = dataset._split_by_coordinate_grid(test_data)
    print(f"拆分后的段数: {len(segments)}")
    
    # 检查每个段的点数
    for i, segment in enumerate(segments):
        points_count = segment["coord"].shape[0]
        print(f"段 {i}: {points_count} 个点")
        
        # 验证每个段至少有90000个点
        if points_count < dataset.MIN_POINTS_PER_SAMPLE:
            print(f"警告: 段 {i} 只有 {points_count} 个点，少于 {dataset.MIN_POINTS_PER_SAMPLE}")
        else:
            print(f"✓ 段 {i} 满足最小点数要求")
    
    # 测试_get_grid_boundaries方法
    print("\n测试_get_grid_boundaries方法...")
    min_coord = np.array([0, 0, 0])
    spatial_extent = np.array([10, 10, 10])
    grid_dims = [2, 2, 2]
    
    boundaries = dataset._get_grid_boundaries(min_coord, spatial_extent, grid_dims, 0, 0, 0)
    print(f"网格边界: {boundaries}")
    
    # 测试_extract_single_grid方法
    print("\n测试_extract_single_grid方法...")
    test_data_dict = {
        "name": "test_sample_2",
        "coord": np.random.rand(200000, 3) * 10.0,  # 20万个点
        "color": np.random.rand(200000, 3),
        "segment": np.random.randint(0, 20, 200000)
    }
    
    segment = dataset._extract_single_grid(test_data_dict, 0, 0, 0)
    points_count = segment["coord"].shape[0]
    print(f"提取的网格点数: {points_count}")
    
    if points_count >= dataset.MIN_POINTS_PER_SAMPLE:
        print("✓ 提取的网格满足最小点数要求")
    else:
        print(f"警告: 提取的网格只有 {points_count} 个点")
    
    print("\n测试完成!")

if __name__ == "__main__":
    test_powscan_modifications()