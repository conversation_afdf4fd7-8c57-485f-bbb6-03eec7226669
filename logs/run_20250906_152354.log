Starting training at 2025年 09月 06日 星期六 15:23:54 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-06 15:24:16,165 INFO train.py line 136 1407060] => Loading config ...
[2025-09-06 15:24:16,166 INFO train.py line 138 1407060] Save path: exp/powscan/splitSections
[2025-09-06 15:24:16,302 INFO train.py line 139 1407060] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 12
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 6
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-06 15:24:16,302 INFO train.py line 140 1407060] => Building model ...
[2025-09-06 15:24:16,355 INFO train.py line 241 1407060] Num params: 3908496
[2025-09-06 15:24:16,402 INFO train.py line 142 1407060] => Building writer ...
[2025-09-06 15:24:16,404 INFO train.py line 251 1407060] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-06 15:24:16,404 INFO train.py line 144 1407060] => Building train dataset & dataloader ...
[2025-09-06 15:24:35,271 INFO defaults.py line 70 1407060] Totally 585 x 30 samples in powscan train set.
[2025-09-06 15:24:35,272 INFO train.py line 146 1407060] => Building val dataset & dataloader ...
[2025-09-06 15:24:36,645 INFO defaults.py line 70 1407060] Totally 80 x 1 samples in powscan val set.
[2025-09-06 15:24:36,645 INFO train.py line 148 1407060] => Building optimize, scheduler, scaler(amp) ...
[2025-09-06 15:24:36,795 INFO train.py line 152 1407060] => Building hooks ...
[2025-09-06 15:24:36,796 INFO misc.py line 237 1407060] => Loading checkpoint & weight ...
[2025-09-06 15:24:36,796 INFO misc.py line 239 1407060] Loading weight at: exp/powscan/splitSections/model/model_last.pth
[2025-09-06 15:24:37,011 INFO misc.py line 245 1407060] Loading layer weights with keyword: , replace keyword with: 
[2025-09-06 15:24:37,039 INFO misc.py line 262 1407060] Missing keys: []
[2025-09-06 15:24:37,039 INFO misc.py line 264 1407060] Resuming train at eval epoch: 13
[2025-09-06 15:24:37,045 INFO train.py line 159 1407060] >>>>>>>>>>>>>>>> Start Training >>>>>>>>>>>>>>>>
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
[2025-09-06 15:24:53,909 INFO misc.py line 117 1407060] Train: [14/100][1/1462] Data 13.263 (13.263) Batch 16.815 (16.815) Remain 594:05:47 loss: 0.2806 Lr: 0.00200
[2025-09-06 15:24:57,178 INFO misc.py line 117 1407060] Train: [14/100][2/1462] Data 0.002 (0.002) Batch 3.269 (3.269) Remain 115:28:51 loss: 0.5064 Lr: 0.00200
[2025-09-06 15:25:00,457 INFO misc.py line 117 1407060] Train: [14/100][3/1462] Data 0.001 (0.001) Batch 3.279 (3.279) Remain 115:51:08 loss: 0.3336 Lr: 0.00200
[2025-09-06 15:25:03,288 INFO misc.py line 117 1407060] Train: [14/100][4/1462] Data 0.001 (0.001) Batch 2.831 (2.831) Remain 100:01:13 loss: 0.3845 Lr: 0.00200
[2025-09-06 15:25:05,945 INFO misc.py line 117 1407060] Train: [14/100][5/1462] Data 0.001 (0.001) Batch 2.657 (2.744) Remain 96:57:00 loss: 0.3848 Lr: 0.00200
[2025-09-06 15:25:08,633 INFO misc.py line 117 1407060] Train: [14/100][6/1462] Data 0.002 (0.002) Batch 2.688 (2.725) Remain 96:17:17 loss: 0.4506 Lr: 0.00200
[2025-09-06 15:25:11,499 INFO misc.py line 117 1407060] Train: [14/100][7/1462] Data 0.001 (0.001) Batch 2.866 (2.761) Remain 97:31:49 loss: 0.2723 Lr: 0.00200
[2025-09-06 15:25:14,053 INFO misc.py line 117 1407060] Train: [14/100][8/1462] Data 0.002 (0.002) Batch 2.554 (2.719) Remain 96:04:11 loss: 0.3736 Lr: 0.00200
[2025-09-06 15:25:16,852 INFO misc.py line 117 1407060] Train: [14/100][9/1462] Data 0.002 (0.002) Batch 2.799 (2.733) Remain 96:32:18 loss: 0.7186 Lr: 0.00200
[2025-09-06 15:25:20,033 INFO misc.py line 117 1407060] Train: [14/100][10/1462] Data 0.001 (0.002) Batch 3.181 (2.797) Remain 98:48:10 loss: 0.2464 Lr: 0.00200
[2025-09-06 15:25:22,961 INFO misc.py line 117 1407060] Train: [14/100][11/1462] Data 0.001 (0.002) Batch 2.928 (2.813) Remain 99:22:48 loss: 0.2759 Lr: 0.00200
[2025-09-06 15:25:25,849 INFO misc.py line 117 1407060] Train: [14/100][12/1462] Data 0.001 (0.001) Batch 2.888 (2.821) Remain 99:40:25 loss: 0.3470 Lr: 0.00200
[2025-09-06 15:25:28,542 INFO misc.py line 117 1407060] Train: [14/100][13/1462] Data 0.002 (0.002) Batch 2.693 (2.809) Remain 99:13:10 loss: 0.3210 Lr: 0.00200
[2025-09-06 15:25:31,356 INFO misc.py line 117 1407060] Train: [14/100][14/1462] Data 0.002 (0.002) Batch 2.814 (2.809) Remain 99:14:16 loss: 0.3520 Lr: 0.00200
[2025-09-06 15:25:34,289 INFO misc.py line 117 1407060] Train: [14/100][15/1462] Data 0.001 (0.002) Batch 2.933 (2.819) Remain 99:36:06 loss: 0.3907 Lr: 0.00200
[2025-09-06 15:25:37,575 INFO misc.py line 117 1407060] Train: [14/100][16/1462] Data 0.001 (0.002) Batch 3.285 (2.855) Remain 100:52:03 loss: 0.7960 Lr: 0.00200
[2025-09-06 15:25:40,620 INFO misc.py line 117 1407060] Train: [14/100][17/1462] Data 0.001 (0.002) Batch 3.045 (2.869) Remain 101:20:46 loss: 0.3433 Lr: 0.00200
[2025-09-06 15:25:43,471 INFO misc.py line 117 1407060] Train: [14/100][18/1462] Data 0.001 (0.002) Batch 2.851 (2.868) Remain 101:18:09 loss: 0.3826 Lr: 0.00200
[2025-09-06 15:25:46,525 INFO misc.py line 117 1407060] Train: [14/100][19/1462] Data 0.001 (0.002) Batch 3.054 (2.879) Remain 101:42:46 loss: 0.4264 Lr: 0.00200
[2025-09-06 15:25:49,227 INFO misc.py line 117 1407060] Train: [14/100][20/1462] Data 0.002 (0.002) Batch 2.702 (2.869) Remain 101:20:38 loss: 0.2551 Lr: 0.00200
[2025-09-06 15:25:52,382 INFO misc.py line 117 1407060] Train: [14/100][21/1462] Data 0.001 (0.002) Batch 3.155 (2.885) Remain 101:54:17 loss: 0.3905 Lr: 0.00200
[2025-09-06 15:25:55,092 INFO misc.py line 117 1407060] Train: [14/100][22/1462] Data 0.001 (0.002) Batch 2.710 (2.876) Remain 101:34:47 loss: 0.3014 Lr: 0.00200
[2025-09-06 15:25:57,746 INFO misc.py line 117 1407060] Train: [14/100][23/1462] Data 0.002 (0.002) Batch 2.654 (2.864) Remain 101:11:13 loss: 0.7456 Lr: 0.00200
[2025-09-06 15:26:00,517 INFO misc.py line 117 1407060] Train: [14/100][24/1462] Data 0.002 (0.002) Batch 2.771 (2.860) Remain 101:01:44 loss: 0.6076 Lr: 0.00200
[2025-09-06 15:26:03,472 INFO misc.py line 117 1407060] Train: [14/100][25/1462] Data 0.001 (0.002) Batch 2.955 (2.864) Remain 101:10:52 loss: 0.5505 Lr: 0.00200
[2025-09-06 15:26:06,350 INFO misc.py line 117 1407060] Train: [14/100][26/1462] Data 0.002 (0.002) Batch 2.878 (2.865) Remain 101:12:05 loss: 0.4245 Lr: 0.00200
[2025-09-06 15:26:09,530 INFO misc.py line 117 1407060] Train: [14/100][27/1462] Data 0.001 (0.002) Batch 3.180 (2.878) Remain 101:39:51 loss: 0.3007 Lr: 0.00200
[2025-09-06 15:26:12,335 INFO misc.py line 117 1407060] Train: [14/100][28/1462] Data 0.001 (0.002) Batch 2.805 (2.875) Remain 101:33:38 loss: 0.6282 Lr: 0.00200
[2025-09-06 15:26:15,324 INFO misc.py line 117 1407060] Train: [14/100][29/1462] Data 0.002 (0.002) Batch 2.989 (2.880) Remain 101:42:53 loss: 0.5939 Lr: 0.00200
[2025-09-06 15:26:17,955 INFO misc.py line 117 1407060] Train: [14/100][30/1462] Data 0.001 (0.002) Batch 2.631 (2.870) Remain 101:23:18 loss: 0.7621 Lr: 0.00200
[2025-09-06 15:26:20,865 INFO misc.py line 117 1407060] Train: [14/100][31/1462] Data 0.001 (0.002) Batch 2.910 (2.872) Remain 101:26:16 loss: 0.4903 Lr: 0.00200
[2025-09-06 15:26:23,595 INFO misc.py line 117 1407060] Train: [14/100][32/1462] Data 0.001 (0.002) Batch 2.730 (2.867) Remain 101:15:51 loss: 0.5755 Lr: 0.00200
[2025-09-06 15:26:26,645 INFO misc.py line 117 1407060] Train: [14/100][33/1462] Data 0.001 (0.002) Batch 3.051 (2.873) Remain 101:28:47 loss: 0.3110 Lr: 0.00200
[2025-09-06 15:26:29,418 INFO misc.py line 117 1407060] Train: [14/100][34/1462] Data 0.001 (0.002) Batch 2.773 (2.870) Remain 101:21:54 loss: 0.3312 Lr: 0.00200
[2025-09-06 15:26:32,667 INFO misc.py line 117 1407060] Train: [14/100][35/1462] Data 0.002 (0.002) Batch 3.248 (2.882) Remain 101:46:55 loss: 0.3178 Lr: 0.00200
[2025-09-06 15:26:35,592 INFO misc.py line 117 1407060] Train: [14/100][36/1462] Data 0.001 (0.002) Batch 2.926 (2.883) Remain 101:49:43 loss: 0.3678 Lr: 0.00200
[2025-09-06 15:26:38,656 INFO misc.py line 117 1407060] Train: [14/100][37/1462] Data 0.001 (0.002) Batch 3.063 (2.888) Remain 102:00:55 loss: 0.6043 Lr: 0.00200
[2025-09-06 15:26:41,817 INFO misc.py line 117 1407060] Train: [14/100][38/1462] Data 0.001 (0.001) Batch 3.161 (2.896) Remain 102:17:24 loss: 0.3548 Lr: 0.00200
[2025-09-06 15:26:44,922 INFO misc.py line 117 1407060] Train: [14/100][39/1462] Data 0.001 (0.001) Batch 3.105 (2.902) Remain 102:29:39 loss: 0.3662 Lr: 0.00200
[2025-09-06 15:26:47,810 INFO misc.py line 117 1407060] Train: [14/100][40/1462] Data 0.001 (0.001) Batch 2.888 (2.901) Remain 102:28:49 loss: 0.5984 Lr: 0.00200
[2025-09-06 15:26:50,687 INFO misc.py line 117 1407060] Train: [14/100][41/1462] Data 0.001 (0.001) Batch 2.877 (2.901) Remain 102:27:23 loss: 0.2131 Lr: 0.00200
[2025-09-06 15:26:53,562 INFO misc.py line 117 1407060] Train: [14/100][42/1462] Data 0.001 (0.001) Batch 2.875 (2.900) Remain 102:25:55 loss: 0.2328 Lr: 0.00200
[2025-09-06 15:26:56,406 INFO misc.py line 117 1407060] Train: [14/100][43/1462] Data 0.002 (0.001) Batch 2.845 (2.899) Remain 102:22:56 loss: 0.2901 Lr: 0.00200
[2025-09-06 15:26:59,156 INFO misc.py line 117 1407060] Train: [14/100][44/1462] Data 0.002 (0.001) Batch 2.750 (2.895) Remain 102:15:11 loss: 0.5460 Lr: 0.00200
[2025-09-06 15:27:02,114 INFO misc.py line 117 1407060] Train: [14/100][45/1462] Data 0.001 (0.001) Batch 2.958 (2.897) Remain 102:18:18 loss: 0.3931 Lr: 0.00200
[2025-09-06 15:27:05,135 INFO misc.py line 117 1407060] Train: [14/100][46/1462] Data 0.002 (0.001) Batch 3.022 (2.899) Remain 102:24:25 loss: 0.4379 Lr: 0.00200
[2025-09-06 15:27:07,906 INFO misc.py line 117 1407060] Train: [14/100][47/1462] Data 0.001 (0.001) Batch 2.770 (2.897) Remain 102:18:09 loss: 0.3612 Lr: 0.00200
[2025-09-06 15:27:10,740 INFO misc.py line 117 1407060] Train: [14/100][48/1462] Data 0.001 (0.001) Batch 2.834 (2.895) Remain 102:15:09 loss: 0.4049 Lr: 0.00200
[2025-09-06 15:27:13,677 INFO misc.py line 117 1407060] Train: [14/100][49/1462] Data 0.001 (0.001) Batch 2.938 (2.896) Remain 102:17:04 loss: 0.4247 Lr: 0.00200
[2025-09-06 15:27:16,619 INFO misc.py line 117 1407060] Train: [14/100][50/1462] Data 0.001 (0.001) Batch 2.941 (2.897) Remain 102:19:03 loss: 0.7000 Lr: 0.00200
[2025-09-06 15:27:19,203 INFO misc.py line 117 1407060] Train: [14/100][51/1462] Data 0.001 (0.001) Batch 2.584 (2.891) Remain 102:05:12 loss: 0.5204 Lr: 0.00200
[2025-09-06 15:27:21,926 INFO misc.py line 117 1407060] Train: [14/100][52/1462] Data 0.001 (0.001) Batch 2.723 (2.887) Remain 101:57:54 loss: 0.3152 Lr: 0.00200
[2025-09-06 15:27:24,822 INFO misc.py line 117 1407060] Train: [14/100][53/1462] Data 0.001 (0.001) Batch 2.897 (2.887) Remain 101:58:15 loss: 0.5952 Lr: 0.00200
[2025-09-06 15:27:27,963 INFO misc.py line 117 1407060] Train: [14/100][54/1462] Data 0.001 (0.001) Batch 3.141 (2.892) Remain 102:08:45 loss: 0.2296 Lr: 0.00200
[2025-09-06 15:27:30,491 INFO misc.py line 117 1407060] Train: [14/100][55/1462] Data 0.001 (0.001) Batch 2.527 (2.885) Remain 101:53:50 loss: 0.8914 Lr: 0.00200
[2025-09-06 15:27:33,092 INFO misc.py line 117 1407060] Train: [14/100][56/1462] Data 0.001 (0.001) Batch 2.601 (2.880) Remain 101:42:24 loss: 0.9913 Lr: 0.00200
[2025-09-06 15:27:35,926 INFO misc.py line 117 1407060] Train: [14/100][57/1462] Data 0.001 (0.001) Batch 2.835 (2.879) Remain 101:40:35 loss: 0.3684 Lr: 0.00200
[2025-09-06 15:27:38,807 INFO misc.py line 117 1407060] Train: [14/100][58/1462] Data 0.002 (0.001) Batch 2.880 (2.879) Remain 101:40:35 loss: 0.3908 Lr: 0.00200
[2025-09-06 15:27:41,906 INFO misc.py line 117 1407060] Train: [14/100][59/1462] Data 0.001 (0.001) Batch 3.099 (2.883) Remain 101:48:52 loss: 0.2867 Lr: 0.00200
[2025-09-06 15:27:44,425 INFO misc.py line 117 1407060] Train: [14/100][60/1462] Data 0.001 (0.001) Batch 2.519 (2.877) Remain 101:35:17 loss: 0.2634 Lr: 0.00200
[2025-09-06 15:27:46,990 INFO misc.py line 117 1407060] Train: [14/100][61/1462] Data 0.001 (0.001) Batch 2.564 (2.871) Remain 101:23:50 loss: 0.6207 Lr: 0.00200
[2025-09-06 15:27:50,129 INFO misc.py line 117 1407060] Train: [14/100][62/1462] Data 0.001 (0.001) Batch 3.139 (2.876) Remain 101:33:25 loss: 0.4767 Lr: 0.00200
[2025-09-06 15:27:52,764 INFO misc.py line 117 1407060] Train: [14/100][63/1462] Data 0.001 (0.001) Batch 2.635 (2.872) Remain 101:24:53 loss: 0.3400 Lr: 0.00200
[2025-09-06 15:27:55,435 INFO misc.py line 117 1407060] Train: [14/100][64/1462] Data 0.002 (0.001) Batch 2.671 (2.868) Remain 101:17:51 loss: 0.9026 Lr: 0.00200
[2025-09-06 15:27:58,026 INFO misc.py line 117 1407060] Train: [14/100][65/1462] Data 0.001 (0.001) Batch 2.591 (2.864) Remain 101:08:19 loss: 0.3493 Lr: 0.00200
[2025-09-06 15:28:00,671 INFO misc.py line 117 1407060] Train: [14/100][66/1462] Data 0.001 (0.001) Batch 2.645 (2.861) Remain 101:00:54 loss: 0.3458 Lr: 0.00200
[2025-09-06 15:28:03,354 INFO misc.py line 117 1407060] Train: [14/100][67/1462] Data 0.002 (0.001) Batch 2.683 (2.858) Remain 100:54:58 loss: 0.5726 Lr: 0.00200
[2025-09-06 15:28:06,288 INFO misc.py line 117 1407060] Train: [14/100][68/1462] Data 0.001 (0.001) Batch 2.934 (2.859) Remain 100:57:25 loss: 0.4623 Lr: 0.00200
[2025-09-06 15:28:09,119 INFO misc.py line 117 1407060] Train: [14/100][69/1462] Data 0.001 (0.001) Batch 2.831 (2.859) Remain 100:56:28 loss: 0.6361 Lr: 0.00200
[2025-09-06 15:28:11,726 INFO misc.py line 117 1407060] Train: [14/100][70/1462] Data 0.001 (0.001) Batch 2.607 (2.855) Remain 100:48:29 loss: 0.5465 Lr: 0.00200
[2025-09-06 15:28:14,552 INFO misc.py line 117 1407060] Train: [14/100][71/1462] Data 0.001 (0.001) Batch 2.826 (2.854) Remain 100:47:32 loss: 0.3564 Lr: 0.00200
[2025-09-06 15:28:17,335 INFO misc.py line 117 1407060] Train: [14/100][72/1462] Data 0.001 (0.001) Batch 2.783 (2.853) Remain 100:45:18 loss: 0.4636 Lr: 0.00200
[2025-09-06 15:28:20,038 INFO misc.py line 117 1407060] Train: [14/100][73/1462] Data 0.001 (0.001) Batch 2.703 (2.851) Remain 100:40:41 loss: 0.2378 Lr: 0.00200
[2025-09-06 15:28:22,863 INFO misc.py line 117 1407060] Train: [14/100][74/1462] Data 0.001 (0.001) Batch 2.826 (2.851) Remain 100:39:53 loss: 0.3298 Lr: 0.00200
[2025-09-06 15:28:25,562 INFO misc.py line 117 1407060] Train: [14/100][75/1462] Data 0.001 (0.001) Batch 2.698 (2.849) Remain 100:35:21 loss: 0.5682 Lr: 0.00200
[2025-09-06 15:28:28,270 INFO misc.py line 117 1407060] Train: [14/100][76/1462] Data 0.001 (0.001) Batch 2.709 (2.847) Remain 100:31:14 loss: 0.7360 Lr: 0.00200
[2025-09-06 15:28:31,116 INFO misc.py line 117 1407060] Train: [14/100][77/1462] Data 0.001 (0.001) Batch 2.846 (2.847) Remain 100:31:09 loss: 0.3560 Lr: 0.00200
[2025-09-06 15:28:33,925 INFO misc.py line 117 1407060] Train: [14/100][78/1462] Data 0.002 (0.001) Batch 2.809 (2.846) Remain 100:30:02 loss: 0.2654 Lr: 0.00200
[2025-09-06 15:28:37,302 INFO misc.py line 117 1407060] Train: [14/100][79/1462] Data 0.001 (0.001) Batch 3.377 (2.853) Remain 100:44:48 loss: 0.4198 Lr: 0.00200
[2025-09-06 15:28:39,986 INFO misc.py line 117 1407060] Train: [14/100][80/1462] Data 0.001 (0.001) Batch 2.684 (2.851) Remain 100:40:05 loss: 0.7159 Lr: 0.00200
[2025-09-06 15:28:43,017 INFO misc.py line 117 1407060] Train: [14/100][81/1462] Data 0.002 (0.001) Batch 3.031 (2.853) Remain 100:44:55 loss: 0.6940 Lr: 0.00200
[2025-09-06 15:28:45,752 INFO misc.py line 117 1407060] Train: [14/100][82/1462] Data 0.002 (0.001) Batch 2.736 (2.852) Remain 100:41:43 loss: 0.3905 Lr: 0.00200
[2025-09-06 15:28:48,657 INFO misc.py line 117 1407060] Train: [14/100][83/1462] Data 0.001 (0.001) Batch 2.905 (2.853) Remain 100:43:04 loss: 0.4334 Lr: 0.00200
[2025-09-06 15:28:51,640 INFO misc.py line 117 1407060] Train: [14/100][84/1462] Data 0.002 (0.001) Batch 2.983 (2.854) Remain 100:46:26 loss: 0.5182 Lr: 0.00200
[2025-09-06 15:28:54,559 INFO misc.py line 117 1407060] Train: [14/100][85/1462] Data 0.001 (0.001) Batch 2.919 (2.855) Remain 100:48:03 loss: 0.3576 Lr: 0.00200
[2025-09-06 15:28:57,339 INFO misc.py line 117 1407060] Train: [14/100][86/1462] Data 0.002 (0.001) Batch 2.781 (2.854) Remain 100:46:06 loss: 0.2319 Lr: 0.00200
[2025-09-06 15:29:00,020 INFO misc.py line 117 1407060] Train: [14/100][87/1462] Data 0.002 (0.001) Batch 2.680 (2.852) Remain 100:41:41 loss: 0.5961 Lr: 0.00200
[2025-09-06 15:29:02,676 INFO misc.py line 117 1407060] Train: [14/100][88/1462] Data 0.002 (0.001) Batch 2.656 (2.850) Remain 100:36:45 loss: 0.4026 Lr: 0.00200
[2025-09-06 15:29:05,635 INFO misc.py line 117 1407060] Train: [14/100][89/1462] Data 0.002 (0.001) Batch 2.959 (2.851) Remain 100:39:23 loss: 0.3600 Lr: 0.00200
[2025-09-06 15:29:08,681 INFO misc.py line 117 1407060] Train: [14/100][90/1462] Data 0.002 (0.001) Batch 3.046 (2.853) Remain 100:44:06 loss: 0.4630 Lr: 0.00200
[2025-09-06 15:29:11,684 INFO misc.py line 117 1407060] Train: [14/100][91/1462] Data 0.002 (0.001) Batch 3.003 (2.855) Remain 100:47:40 loss: 0.5893 Lr: 0.00200
[2025-09-06 15:29:14,886 INFO misc.py line 117 1407060] Train: [14/100][92/1462] Data 0.002 (0.001) Batch 3.202 (2.859) Remain 100:55:53 loss: 0.5100 Lr: 0.00200
[2025-09-06 15:29:17,974 INFO misc.py line 117 1407060] Train: [14/100][93/1462] Data 0.002 (0.001) Batch 3.088 (2.861) Remain 101:01:13 loss: 0.3637 Lr: 0.00200
[2025-09-06 15:29:20,691 INFO misc.py line 117 1407060] Train: [14/100][94/1462] Data 0.001 (0.001) Batch 2.717 (2.860) Remain 100:57:49 loss: 0.8141 Lr: 0.00200
[2025-09-06 15:29:23,609 INFO misc.py line 117 1407060] Train: [14/100][95/1462] Data 0.002 (0.001) Batch 2.919 (2.860) Remain 100:59:08 loss: 0.6914 Lr: 0.00200
[2025-09-06 15:29:26,557 INFO misc.py line 117 1407060] Train: [14/100][96/1462] Data 0.001 (0.001) Batch 2.948 (2.861) Remain 101:01:04 loss: 0.5767 Lr: 0.00200
[2025-09-06 15:29:29,268 INFO misc.py line 117 1407060] Train: [14/100][97/1462] Data 0.001 (0.001) Batch 2.711 (2.860) Remain 100:57:38 loss: 0.3902 Lr: 0.00200
[2025-09-06 15:29:32,073 INFO misc.py line 117 1407060] Train: [14/100][98/1462] Data 0.001 (0.001) Batch 2.804 (2.859) Remain 100:56:21 loss: 0.3743 Lr: 0.00200
[2025-09-06 15:29:34,909 INFO misc.py line 117 1407060] Train: [14/100][99/1462] Data 0.001 (0.001) Batch 2.836 (2.859) Remain 100:55:48 loss: 0.2842 Lr: 0.00200
[2025-09-06 15:29:37,513 INFO misc.py line 117 1407060] Train: [14/100][100/1462] Data 0.002 (0.001) Batch 2.604 (2.856) Remain 100:50:11 loss: 0.3643 Lr: 0.00200
[2025-09-06 15:29:40,365 INFO misc.py line 117 1407060] Train: [14/100][101/1462] Data 0.002 (0.001) Batch 2.853 (2.856) Remain 100:50:04 loss: 0.6801 Lr: 0.00200
[2025-09-06 15:29:43,512 INFO misc.py line 117 1407060] Train: [14/100][102/1462] Data 0.002 (0.001) Batch 3.147 (2.859) Remain 100:56:14 loss: 0.5542 Lr: 0.00200
[2025-09-06 15:29:46,768 INFO misc.py line 117 1407060] Train: [14/100][103/1462] Data 0.001 (0.001) Batch 3.256 (2.863) Remain 101:04:35 loss: 0.5186 Lr: 0.00200
[2025-09-06 15:29:49,511 INFO misc.py line 117 1407060] Train: [14/100][104/1462] Data 0.002 (0.001) Batch 2.743 (2.862) Remain 101:02:01 loss: 0.6351 Lr: 0.00200
[2025-09-06 15:29:52,072 INFO misc.py line 117 1407060] Train: [14/100][105/1462] Data 0.002 (0.001) Batch 2.561 (2.859) Remain 100:55:44 loss: 0.5037 Lr: 0.00200
[2025-09-06 15:29:54,626 INFO misc.py line 117 1407060] Train: [14/100][106/1462] Data 0.002 (0.001) Batch 2.554 (2.856) Remain 100:49:24 loss: 0.5311 Lr: 0.00200
[2025-09-06 15:29:57,840 INFO misc.py line 117 1407060] Train: [14/100][107/1462] Data 0.001 (0.001) Batch 3.213 (2.859) Remain 100:56:38 loss: 0.3803 Lr: 0.00200
[2025-09-06 15:30:00,843 INFO misc.py line 117 1407060] Train: [14/100][108/1462] Data 0.002 (0.001) Batch 3.003 (2.861) Remain 100:59:29 loss: 0.5379 Lr: 0.00200
[2025-09-06 15:30:03,944 INFO misc.py line 117 1407060] Train: [14/100][109/1462] Data 0.001 (0.001) Batch 3.101 (2.863) Remain 101:04:15 loss: 0.2218 Lr: 0.00200
[2025-09-06 15:30:06,830 INFO misc.py line 117 1407060] Train: [14/100][110/1462] Data 0.001 (0.001) Batch 2.886 (2.863) Remain 101:04:39 loss: 0.5661 Lr: 0.00200
[2025-09-06 15:30:09,568 INFO misc.py line 117 1407060] Train: [14/100][111/1462] Data 0.001 (0.001) Batch 2.738 (2.862) Remain 101:02:09 loss: 0.3777 Lr: 0.00200
[2025-09-06 15:30:12,923 INFO misc.py line 117 1407060] Train: [14/100][112/1462] Data 0.001 (0.001) Batch 3.355 (2.867) Remain 101:11:40 loss: 0.3172 Lr: 0.00200
[2025-09-06 15:30:15,974 INFO misc.py line 117 1407060] Train: [14/100][113/1462] Data 0.001 (0.001) Batch 3.051 (2.868) Remain 101:15:11 loss: 2.0973 Lr: 0.00200
[2025-09-06 15:30:18,861 INFO misc.py line 117 1407060] Train: [14/100][114/1462] Data 0.001 (0.001) Batch 2.887 (2.869) Remain 101:15:29 loss: 0.3754 Lr: 0.00200
[2025-09-06 15:30:21,840 INFO misc.py line 117 1407060] Train: [14/100][115/1462] Data 0.002 (0.001) Batch 2.979 (2.869) Remain 101:17:32 loss: 0.6177 Lr: 0.00200
[2025-09-06 15:30:24,761 INFO misc.py line 117 1407060] Train: [14/100][116/1462] Data 0.001 (0.001) Batch 2.920 (2.870) Remain 101:18:26 loss: 0.6897 Lr: 0.00200
[2025-09-06 15:30:28,090 INFO misc.py line 117 1407060] Train: [14/100][117/1462] Data 0.001 (0.001) Batch 3.329 (2.874) Remain 101:26:55 loss: 0.4941 Lr: 0.00200
[2025-09-06 15:30:31,129 INFO misc.py line 117 1407060] Train: [14/100][118/1462] Data 0.001 (0.001) Batch 3.039 (2.875) Remain 101:29:55 loss: 0.4423 Lr: 0.00200
[2025-09-06 15:30:33,964 INFO misc.py line 117 1407060] Train: [14/100][119/1462] Data 0.002 (0.001) Batch 2.835 (2.875) Remain 101:29:08 loss: 0.5832 Lr: 0.00200
[2025-09-06 15:30:36,776 INFO misc.py line 117 1407060] Train: [14/100][120/1462] Data 0.001 (0.001) Batch 2.812 (2.875) Remain 101:27:56 loss: 0.6316 Lr: 0.00200
[2025-09-06 15:30:39,619 INFO misc.py line 117 1407060] Train: [14/100][121/1462] Data 0.001 (0.001) Batch 2.843 (2.874) Remain 101:27:20 loss: 0.4269 Lr: 0.00200
[2025-09-06 15:30:42,103 INFO misc.py line 117 1407060] Train: [14/100][122/1462] Data 0.001 (0.001) Batch 2.484 (2.871) Remain 101:20:21 loss: 0.4872 Lr: 0.00200
[2025-09-06 15:30:44,866 INFO misc.py line 117 1407060] Train: [14/100][123/1462] Data 0.001 (0.001) Batch 2.763 (2.870) Remain 101:18:23 loss: 0.6186 Lr: 0.00200
[2025-09-06 15:30:47,835 INFO misc.py line 117 1407060] Train: [14/100][124/1462] Data 0.001 (0.001) Batch 2.968 (2.871) Remain 101:20:03 loss: 0.3356 Lr: 0.00200
[2025-09-06 15:30:50,797 INFO misc.py line 117 1407060] Train: [14/100][125/1462] Data 0.001 (0.001) Batch 2.962 (2.872) Remain 101:21:36 loss: 0.4490 Lr: 0.00200
[2025-09-06 15:30:53,409 INFO misc.py line 117 1407060] Train: [14/100][126/1462] Data 0.001 (0.001) Batch 2.612 (2.870) Remain 101:17:04 loss: 0.5693 Lr: 0.00200
[2025-09-06 15:30:56,304 INFO misc.py line 117 1407060] Train: [14/100][127/1462] Data 0.001 (0.001) Batch 2.895 (2.870) Remain 101:17:28 loss: 0.4114 Lr: 0.00200
[2025-09-06 15:30:59,317 INFO misc.py line 117 1407060] Train: [14/100][128/1462] Data 0.001 (0.001) Batch 3.013 (2.871) Remain 101:19:51 loss: 0.4051 Lr: 0.00200
[2025-09-06 15:31:01,844 INFO misc.py line 117 1407060] Train: [14/100][129/1462] Data 0.002 (0.001) Batch 2.526 (2.868) Remain 101:14:01 loss: 0.2826 Lr: 0.00200
[2025-09-06 15:31:04,588 INFO misc.py line 117 1407060] Train: [14/100][130/1462] Data 0.001 (0.001) Batch 2.744 (2.867) Remain 101:11:54 loss: 0.2982 Lr: 0.00200
[2025-09-06 15:31:07,810 INFO misc.py line 117 1407060] Train: [14/100][131/1462] Data 0.001 (0.001) Batch 3.222 (2.870) Remain 101:17:44 loss: 0.5143 Lr: 0.00200
[2025-09-06 15:31:10,796 INFO misc.py line 117 1407060] Train: [14/100][132/1462] Data 0.002 (0.001) Batch 2.986 (2.871) Remain 101:19:35 loss: 0.3736 Lr: 0.00200
[2025-09-06 15:31:13,840 INFO misc.py line 117 1407060] Train: [14/100][133/1462] Data 0.001 (0.001) Batch 3.044 (2.872) Remain 101:22:21 loss: 0.7286 Lr: 0.00200
[2025-09-06 15:31:16,923 INFO misc.py line 117 1407060] Train: [14/100][134/1462] Data 0.001 (0.001) Batch 3.084 (2.874) Remain 101:25:43 loss: 0.6353 Lr: 0.00200
[2025-09-06 15:31:20,188 INFO misc.py line 117 1407060] Train: [14/100][135/1462] Data 0.001 (0.001) Batch 3.265 (2.877) Remain 101:31:57 loss: 0.3426 Lr: 0.00200
[2025-09-06 15:31:23,061 INFO misc.py line 117 1407060] Train: [14/100][136/1462] Data 0.001 (0.001) Batch 2.872 (2.877) Remain 101:31:50 loss: 0.5958 Lr: 0.00200
[2025-09-06 15:31:25,620 INFO misc.py line 117 1407060] Train: [14/100][137/1462] Data 0.001 (0.001) Batch 2.560 (2.874) Remain 101:26:46 loss: 0.5411 Lr: 0.00200
[2025-09-06 15:31:28,623 INFO misc.py line 117 1407060] Train: [14/100][138/1462] Data 0.001 (0.001) Batch 3.003 (2.875) Remain 101:28:44 loss: 0.3847 Lr: 0.00200
[2025-09-06 15:31:31,473 INFO misc.py line 117 1407060] Train: [14/100][139/1462] Data 0.001 (0.001) Batch 2.850 (2.875) Remain 101:28:18 loss: 0.4764 Lr: 0.00200
[2025-09-06 15:31:34,194 INFO misc.py line 117 1407060] Train: [14/100][140/1462] Data 0.002 (0.001) Batch 2.721 (2.874) Remain 101:25:52 loss: 0.3573 Lr: 0.00200
[2025-09-06 15:31:37,101 INFO misc.py line 117 1407060] Train: [14/100][141/1462] Data 0.001 (0.001) Batch 2.907 (2.874) Remain 101:26:19 loss: 0.3307 Lr: 0.00200
[2025-09-06 15:31:39,902 INFO misc.py line 117 1407060] Train: [14/100][142/1462] Data 0.002 (0.001) Batch 2.801 (2.874) Remain 101:25:10 loss: 0.5821 Lr: 0.00200
[2025-09-06 15:31:42,530 INFO misc.py line 117 1407060] Train: [14/100][143/1462] Data 0.001 (0.001) Batch 2.627 (2.872) Remain 101:21:23 loss: 0.4744 Lr: 0.00200
[2025-09-06 15:31:45,488 INFO misc.py line 117 1407060] Train: [14/100][144/1462] Data 0.001 (0.001) Batch 2.959 (2.873) Remain 101:22:39 loss: 0.2863 Lr: 0.00200
[2025-09-06 15:31:48,191 INFO misc.py line 117 1407060] Train: [14/100][145/1462] Data 0.001 (0.001) Batch 2.702 (2.871) Remain 101:20:03 loss: 0.4108 Lr: 0.00200
[2025-09-06 15:31:51,386 INFO misc.py line 117 1407060] Train: [14/100][146/1462] Data 0.001 (0.001) Batch 3.196 (2.874) Remain 101:24:49 loss: 0.3281 Lr: 0.00200
[2025-09-06 15:31:54,128 INFO misc.py line 117 1407060] Train: [14/100][147/1462] Data 0.001 (0.001) Batch 2.741 (2.873) Remain 101:22:49 loss: 0.7737 Lr: 0.00200
[2025-09-06 15:31:56,838 INFO misc.py line 117 1407060] Train: [14/100][148/1462] Data 0.001 (0.001) Batch 2.710 (2.872) Remain 101:20:24 loss: 0.5481 Lr: 0.00200
[2025-09-06 15:31:59,659 INFO misc.py line 117 1407060] Train: [14/100][149/1462] Data 0.001 (0.001) Batch 2.821 (2.871) Remain 101:19:37 loss: 0.3331 Lr: 0.00200
[2025-09-06 15:32:02,443 INFO misc.py line 117 1407060] Train: [14/100][150/1462] Data 0.001 (0.001) Batch 2.785 (2.871) Remain 101:18:19 loss: 0.5461 Lr: 0.00200
[2025-09-06 15:32:05,089 INFO misc.py line 117 1407060] Train: [14/100][151/1462] Data 0.001 (0.001) Batch 2.646 (2.869) Remain 101:15:03 loss: 0.5094 Lr: 0.00200
[2025-09-06 15:32:07,872 INFO misc.py line 117 1407060] Train: [14/100][152/1462] Data 0.001 (0.001) Batch 2.783 (2.869) Remain 101:13:47 loss: 0.5773 Lr: 0.00200
[2025-09-06 15:32:10,545 INFO misc.py line 117 1407060] Train: [14/100][153/1462] Data 0.001 (0.001) Batch 2.673 (2.867) Remain 101:10:58 loss: 0.3768 Lr: 0.00200
[2025-09-06 15:32:13,364 INFO misc.py line 117 1407060] Train: [14/100][154/1462] Data 0.001 (0.001) Batch 2.819 (2.867) Remain 101:10:15 loss: 0.3186 Lr: 0.00200
[2025-09-06 15:32:16,154 INFO misc.py line 117 1407060] Train: [14/100][155/1462] Data 0.001 (0.001) Batch 2.790 (2.866) Remain 101:09:07 loss: 0.5805 Lr: 0.00200
[2025-09-06 15:32:18,913 INFO misc.py line 117 1407060] Train: [14/100][156/1462] Data 0.001 (0.001) Batch 2.759 (2.866) Remain 101:07:36 loss: 0.2785 Lr: 0.00200
[2025-09-06 15:32:21,640 INFO misc.py line 117 1407060] Train: [14/100][157/1462] Data 0.001 (0.001) Batch 2.727 (2.865) Remain 101:05:38 loss: 0.4345 Lr: 0.00200
[2025-09-06 15:32:24,599 INFO misc.py line 117 1407060] Train: [14/100][158/1462] Data 0.001 (0.001) Batch 2.959 (2.865) Remain 101:06:53 loss: 0.3498 Lr: 0.00200
[2025-09-06 15:32:27,684 INFO misc.py line 117 1407060] Train: [14/100][159/1462] Data 0.001 (0.001) Batch 3.085 (2.867) Remain 101:09:49 loss: 0.5483 Lr: 0.00200
[2025-09-06 15:32:30,691 INFO misc.py line 117 1407060] Train: [14/100][160/1462] Data 0.001 (0.001) Batch 3.007 (2.868) Remain 101:11:39 loss: 0.2249 Lr: 0.00200
[2025-09-06 15:32:33,438 INFO misc.py line 117 1407060] Train: [14/100][161/1462] Data 0.001 (0.001) Batch 2.747 (2.867) Remain 101:09:59 loss: 0.3600 Lr: 0.00200
[2025-09-06 15:32:36,389 INFO misc.py line 117 1407060] Train: [14/100][162/1462] Data 0.001 (0.001) Batch 2.951 (2.867) Remain 101:11:03 loss: 0.4818 Lr: 0.00200
[2025-09-06 15:32:39,497 INFO misc.py line 117 1407060] Train: [14/100][163/1462] Data 0.001 (0.001) Batch 3.108 (2.869) Remain 101:14:12 loss: 0.3490 Lr: 0.00200
[2025-09-06 15:32:42,330 INFO misc.py line 117 1407060] Train: [14/100][164/1462] Data 0.001 (0.001) Batch 2.833 (2.869) Remain 101:13:40 loss: 0.5099 Lr: 0.00200
[2025-09-06 15:32:45,247 INFO misc.py line 117 1407060] Train: [14/100][165/1462] Data 0.001 (0.001) Batch 2.917 (2.869) Remain 101:14:15 loss: 0.4179 Lr: 0.00200
[2025-09-06 15:32:49,263 INFO misc.py line 117 1407060] Train: [14/100][166/1462] Data 0.001 (0.001) Batch 4.016 (2.876) Remain 101:29:06 loss: 0.2575 Lr: 0.00200
[2025-09-06 15:32:52,349 INFO misc.py line 117 1407060] Train: [14/100][167/1462] Data 0.001 (0.001) Batch 3.086 (2.877) Remain 101:31:46 loss: 0.3316 Lr: 0.00200
[2025-09-06 15:32:55,159 INFO misc.py line 117 1407060] Train: [14/100][168/1462] Data 0.001 (0.001) Batch 2.810 (2.877) Remain 101:30:51 loss: 0.3370 Lr: 0.00200
[2025-09-06 15:32:58,021 INFO misc.py line 117 1407060] Train: [14/100][169/1462] Data 0.001 (0.001) Batch 2.861 (2.877) Remain 101:30:36 loss: 0.3391 Lr: 0.00200
[2025-09-06 15:33:00,760 INFO misc.py line 117 1407060] Train: [14/100][170/1462] Data 0.002 (0.001) Batch 2.739 (2.876) Remain 101:28:49 loss: 0.3753 Lr: 0.00200
[2025-09-06 15:33:03,480 INFO misc.py line 117 1407060] Train: [14/100][171/1462] Data 0.001 (0.001) Batch 2.720 (2.875) Remain 101:26:48 loss: 0.3178 Lr: 0.00200
[2025-09-06 15:33:06,411 INFO misc.py line 117 1407060] Train: [14/100][172/1462] Data 0.001 (0.001) Batch 2.931 (2.875) Remain 101:27:27 loss: 0.4575 Lr: 0.00200
[2025-09-06 15:33:09,768 INFO misc.py line 117 1407060] Train: [14/100][173/1462] Data 0.001 (0.001) Batch 3.357 (2.878) Remain 101:33:24 loss: 0.5614 Lr: 0.00200
[2025-09-06 15:33:12,670 INFO misc.py line 117 1407060] Train: [14/100][174/1462] Data 0.001 (0.001) Batch 2.902 (2.878) Remain 101:33:39 loss: 0.2834 Lr: 0.00200
[2025-09-06 15:33:15,581 INFO misc.py line 117 1407060] Train: [14/100][175/1462] Data 0.002 (0.001) Batch 2.911 (2.879) Remain 101:34:00 loss: 0.3290 Lr: 0.00200
[2025-09-06 15:33:18,606 INFO misc.py line 117 1407060] Train: [14/100][176/1462] Data 0.002 (0.001) Batch 3.025 (2.879) Remain 101:35:45 loss: 0.3168 Lr: 0.00200
[2025-09-06 15:33:21,679 INFO misc.py line 117 1407060] Train: [14/100][177/1462] Data 0.002 (0.001) Batch 3.073 (2.881) Remain 101:38:03 loss: 0.3997 Lr: 0.00200
[2025-09-06 15:33:24,339 INFO misc.py line 117 1407060] Train: [14/100][178/1462] Data 0.002 (0.001) Batch 2.660 (2.879) Remain 101:35:20 loss: 0.4136 Lr: 0.00200
[2025-09-06 15:33:27,239 INFO misc.py line 117 1407060] Train: [14/100][179/1462] Data 0.002 (0.001) Batch 2.900 (2.879) Remain 101:35:32 loss: 0.6067 Lr: 0.00200
[2025-09-06 15:33:30,177 INFO misc.py line 117 1407060] Train: [14/100][180/1462] Data 0.002 (0.001) Batch 2.938 (2.880) Remain 101:36:11 loss: 0.2830 Lr: 0.00200
[2025-09-06 15:33:32,997 INFO misc.py line 117 1407060] Train: [14/100][181/1462] Data 0.001 (0.001) Batch 2.821 (2.879) Remain 101:35:26 loss: 0.3289 Lr: 0.00200
[2025-09-06 15:33:36,405 INFO misc.py line 117 1407060] Train: [14/100][182/1462] Data 0.001 (0.001) Batch 3.407 (2.882) Remain 101:41:37 loss: 0.3693 Lr: 0.00200
[2025-09-06 15:33:38,957 INFO misc.py line 117 1407060] Train: [14/100][183/1462] Data 0.002 (0.001) Batch 2.552 (2.881) Remain 101:37:41 loss: 0.3464 Lr: 0.00200
[2025-09-06 15:33:41,543 INFO misc.py line 117 1407060] Train: [14/100][184/1462] Data 0.002 (0.001) Batch 2.586 (2.879) Remain 101:34:12 loss: 0.3416 Lr: 0.00200
[2025-09-06 15:33:44,427 INFO misc.py line 117 1407060] Train: [14/100][185/1462] Data 0.001 (0.001) Batch 2.884 (2.879) Remain 101:34:13 loss: 0.2657 Lr: 0.00200
[2025-09-06 15:33:47,445 INFO misc.py line 117 1407060] Train: [14/100][186/1462] Data 0.001 (0.001) Batch 3.018 (2.880) Remain 101:35:46 loss: 0.3802 Lr: 0.00200
[2025-09-06 15:33:49,972 INFO misc.py line 117 1407060] Train: [14/100][187/1462] Data 0.002 (0.001) Batch 2.527 (2.878) Remain 101:31:40 loss: 0.4513 Lr: 0.00200
[2025-09-06 15:33:52,866 INFO misc.py line 117 1407060] Train: [14/100][188/1462] Data 0.001 (0.001) Batch 2.894 (2.878) Remain 101:31:49 loss: 0.3757 Lr: 0.00200
[2025-09-06 15:33:55,825 INFO misc.py line 117 1407060] Train: [14/100][189/1462] Data 0.001 (0.001) Batch 2.959 (2.878) Remain 101:32:41 loss: 0.3288 Lr: 0.00200
[2025-09-06 15:33:58,444 INFO misc.py line 117 1407060] Train: [14/100][190/1462] Data 0.002 (0.001) Batch 2.619 (2.877) Remain 101:29:42 loss: 0.5855 Lr: 0.00200
[2025-09-06 15:34:01,131 INFO misc.py line 117 1407060] Train: [14/100][191/1462] Data 0.002 (0.001) Batch 2.687 (2.876) Remain 101:27:31 loss: 0.3049 Lr: 0.00200
[2025-09-06 15:34:03,895 INFO misc.py line 117 1407060] Train: [14/100][192/1462] Data 0.002 (0.001) Batch 2.763 (2.875) Remain 101:26:12 loss: 0.3394 Lr: 0.00200
[2025-09-06 15:34:06,718 INFO misc.py line 117 1407060] Train: [14/100][193/1462] Data 0.001 (0.001) Batch 2.823 (2.875) Remain 101:25:35 loss: 0.5607 Lr: 0.00200
[2025-09-06 15:34:09,642 INFO misc.py line 117 1407060] Train: [14/100][194/1462] Data 0.002 (0.001) Batch 2.924 (2.875) Remain 101:26:04 loss: 0.5684 Lr: 0.00200
[2025-09-06 15:34:12,406 INFO misc.py line 117 1407060] Train: [14/100][195/1462] Data 0.001 (0.001) Batch 2.764 (2.875) Remain 101:24:48 loss: 0.4664 Lr: 0.00200
[2025-09-06 15:34:15,126 INFO misc.py line 117 1407060] Train: [14/100][196/1462] Data 0.001 (0.001) Batch 2.720 (2.874) Remain 101:23:03 loss: 0.7406 Lr: 0.00200
[2025-09-06 15:34:18,081 INFO misc.py line 117 1407060] Train: [14/100][197/1462] Data 0.002 (0.001) Batch 2.955 (2.874) Remain 101:23:53 loss: 0.3391 Lr: 0.00200
[2025-09-06 15:34:21,056 INFO misc.py line 117 1407060] Train: [14/100][198/1462] Data 0.002 (0.001) Batch 2.976 (2.875) Remain 101:24:56 loss: 0.4154 Lr: 0.00200
[2025-09-06 15:34:23,980 INFO misc.py line 117 1407060] Train: [14/100][199/1462] Data 0.002 (0.001) Batch 2.924 (2.875) Remain 101:25:25 loss: 0.4569 Lr: 0.00200
[2025-09-06 15:34:27,034 INFO misc.py line 117 1407060] Train: [14/100][200/1462] Data 0.002 (0.001) Batch 3.054 (2.876) Remain 101:27:18 loss: 0.5008 Lr: 0.00200
[2025-09-06 15:34:29,677 INFO misc.py line 117 1407060] Train: [14/100][201/1462] Data 0.002 (0.001) Batch 2.643 (2.875) Remain 101:24:45 loss: 0.7791 Lr: 0.00200
[2025-09-06 15:34:32,673 INFO misc.py line 117 1407060] Train: [14/100][202/1462] Data 0.002 (0.001) Batch 2.996 (2.875) Remain 101:26:00 loss: 0.4561 Lr: 0.00200
[2025-09-06 15:34:35,475 INFO misc.py line 117 1407060] Train: [14/100][203/1462] Data 0.002 (0.001) Batch 2.802 (2.875) Remain 101:25:10 loss: 0.4370 Lr: 0.00200
[2025-09-06 15:34:38,342 INFO misc.py line 117 1407060] Train: [14/100][204/1462] Data 0.002 (0.001) Batch 2.867 (2.875) Remain 101:25:02 loss: 0.3859 Lr: 0.00200
[2025-09-06 15:34:41,663 INFO misc.py line 117 1407060] Train: [14/100][205/1462] Data 0.002 (0.001) Batch 3.321 (2.877) Remain 101:29:39 loss: 0.5455 Lr: 0.00200
[2025-09-06 15:34:44,632 INFO misc.py line 117 1407060] Train: [14/100][206/1462] Data 0.002 (0.001) Batch 2.969 (2.878) Remain 101:30:34 loss: 0.5005 Lr: 0.00200
[2025-09-06 15:34:47,470 INFO misc.py line 117 1407060] Train: [14/100][207/1462] Data 0.002 (0.001) Batch 2.839 (2.878) Remain 101:30:07 loss: 0.5782 Lr: 0.00200
[2025-09-06 15:34:50,414 INFO misc.py line 117 1407060] Train: [14/100][208/1462] Data 0.001 (0.001) Batch 2.944 (2.878) Remain 101:30:45 loss: 0.2449 Lr: 0.00200
[2025-09-06 15:34:52,985 INFO misc.py line 117 1407060] Train: [14/100][209/1462] Data 0.001 (0.001) Batch 2.571 (2.876) Remain 101:27:33 loss: 0.4984 Lr: 0.00200
[2025-09-06 15:34:55,705 INFO misc.py line 117 1407060] Train: [14/100][210/1462] Data 0.002 (0.001) Batch 2.720 (2.876) Remain 101:25:54 loss: 0.4670 Lr: 0.00200
[2025-09-06 15:34:58,378 INFO misc.py line 117 1407060] Train: [14/100][211/1462] Data 0.002 (0.001) Batch 2.673 (2.875) Remain 101:23:47 loss: 0.4085 Lr: 0.00200
[2025-09-06 15:35:01,183 INFO misc.py line 117 1407060] Train: [14/100][212/1462] Data 0.002 (0.001) Batch 2.805 (2.874) Remain 101:23:02 loss: 0.3917 Lr: 0.00200
[2025-09-06 15:35:03,884 INFO misc.py line 117 1407060] Train: [14/100][213/1462] Data 0.002 (0.001) Batch 2.701 (2.873) Remain 101:21:15 loss: 0.5308 Lr: 0.00200
[2025-09-06 15:35:06,774 INFO misc.py line 117 1407060] Train: [14/100][214/1462] Data 0.002 (0.001) Batch 2.890 (2.874) Remain 101:21:22 loss: 0.2985 Lr: 0.00200
[2025-09-06 15:35:09,247 INFO misc.py line 117 1407060] Train: [14/100][215/1462] Data 0.002 (0.001) Batch 2.473 (2.872) Remain 101:17:19 loss: 0.3189 Lr: 0.00200
[2025-09-06 15:35:12,087 INFO misc.py line 117 1407060] Train: [14/100][216/1462] Data 0.001 (0.001) Batch 2.840 (2.872) Remain 101:16:57 loss: 0.3689 Lr: 0.00200
[2025-09-06 15:35:14,699 INFO misc.py line 117 1407060] Train: [14/100][217/1462] Data 0.001 (0.001) Batch 2.612 (2.870) Remain 101:14:20 loss: 0.2465 Lr: 0.00200
[2025-09-06 15:35:17,416 INFO misc.py line 117 1407060] Train: [14/100][218/1462] Data 0.001 (0.001) Batch 2.717 (2.870) Remain 101:12:47 loss: 0.4758 Lr: 0.00200
[2025-09-06 15:35:20,224 INFO misc.py line 117 1407060] Train: [14/100][219/1462] Data 0.001 (0.001) Batch 2.808 (2.869) Remain 101:12:08 loss: 0.4699 Lr: 0.00200
[2025-09-06 15:35:23,167 INFO misc.py line 117 1407060] Train: [14/100][220/1462] Data 0.001 (0.001) Batch 2.943 (2.870) Remain 101:12:48 loss: 0.2911 Lr: 0.00200
[2025-09-06 15:35:25,976 INFO misc.py line 117 1407060] Train: [14/100][221/1462] Data 0.001 (0.001) Batch 2.809 (2.869) Remain 101:12:10 loss: 0.3845 Lr: 0.00200
[2025-09-06 15:35:29,206 INFO misc.py line 117 1407060] Train: [14/100][222/1462] Data 0.001 (0.001) Batch 3.230 (2.871) Remain 101:15:36 loss: 0.3625 Lr: 0.00200
[2025-09-06 15:35:32,041 INFO misc.py line 117 1407060] Train: [14/100][223/1462] Data 0.001 (0.001) Batch 2.835 (2.871) Remain 101:15:13 loss: 0.6422 Lr: 0.00200
[2025-09-06 15:35:34,546 INFO misc.py line 117 1407060] Train: [14/100][224/1462] Data 0.001 (0.001) Batch 2.504 (2.869) Remain 101:11:39 loss: 0.3472 Lr: 0.00200
[2025-09-06 15:35:37,417 INFO misc.py line 117 1407060] Train: [14/100][225/1462] Data 0.001 (0.001) Batch 2.871 (2.869) Remain 101:11:38 loss: 0.3298 Lr: 0.00200
[2025-09-06 15:35:40,284 INFO misc.py line 117 1407060] Train: [14/100][226/1462] Data 0.002 (0.001) Batch 2.867 (2.869) Remain 101:11:33 loss: 0.3484 Lr: 0.00200
[2025-09-06 15:35:43,093 INFO misc.py line 117 1407060] Train: [14/100][227/1462] Data 0.002 (0.001) Batch 2.809 (2.869) Remain 101:10:56 loss: 0.2441 Lr: 0.00200
[2025-09-06 15:35:46,063 INFO misc.py line 117 1407060] Train: [14/100][228/1462] Data 0.002 (0.001) Batch 2.970 (2.869) Remain 101:11:51 loss: 0.5733 Lr: 0.00200
[2025-09-06 15:35:49,160 INFO misc.py line 117 1407060] Train: [14/100][229/1462] Data 0.001 (0.001) Batch 3.097 (2.870) Remain 101:13:56 loss: 0.2912 Lr: 0.00200
[2025-09-06 15:35:51,986 INFO misc.py line 117 1407060] Train: [14/100][230/1462] Data 0.001 (0.001) Batch 2.825 (2.870) Remain 101:13:28 loss: 0.3092 Lr: 0.00200
[2025-09-06 15:35:55,000 INFO misc.py line 117 1407060] Train: [14/100][231/1462] Data 0.001 (0.001) Batch 3.015 (2.871) Remain 101:14:45 loss: 0.3723 Lr: 0.00200
[2025-09-06 15:35:57,907 INFO misc.py line 117 1407060] Train: [14/100][232/1462] Data 0.001 (0.001) Batch 2.906 (2.871) Remain 101:15:02 loss: 0.3266 Lr: 0.00200
[2025-09-06 15:36:00,737 INFO misc.py line 117 1407060] Train: [14/100][233/1462] Data 0.002 (0.001) Batch 2.830 (2.871) Remain 101:14:37 loss: 0.2931 Lr: 0.00200
[2025-09-06 15:36:03,953 INFO misc.py line 117 1407060] Train: [14/100][234/1462] Data 0.001 (0.001) Batch 3.216 (2.872) Remain 101:17:44 loss: 0.6616 Lr: 0.00200
[2025-09-06 15:36:07,235 INFO misc.py line 117 1407060] Train: [14/100][235/1462] Data 0.002 (0.001) Batch 3.282 (2.874) Remain 101:21:25 loss: 0.2971 Lr: 0.00200
[2025-09-06 15:36:10,302 INFO misc.py line 117 1407060] Train: [14/100][236/1462] Data 0.002 (0.001) Batch 3.067 (2.875) Remain 101:23:07 loss: 0.3795 Lr: 0.00200
[2025-09-06 15:36:13,550 INFO misc.py line 117 1407060] Train: [14/100][237/1462] Data 0.001 (0.001) Batch 3.248 (2.876) Remain 101:26:27 loss: 0.3500 Lr: 0.00200
[2025-09-06 15:36:16,755 INFO misc.py line 117 1407060] Train: [14/100][238/1462] Data 0.002 (0.001) Batch 3.205 (2.878) Remain 101:29:22 loss: 0.6212 Lr: 0.00200
[2025-09-06 15:36:19,440 INFO misc.py line 117 1407060] Train: [14/100][239/1462] Data 0.001 (0.001) Batch 2.685 (2.877) Remain 101:27:35 loss: 0.5221 Lr: 0.00200
[2025-09-06 15:36:22,342 INFO misc.py line 117 1407060] Train: [14/100][240/1462] Data 0.001 (0.001) Batch 2.901 (2.877) Remain 101:27:45 loss: 0.3092 Lr: 0.00200
[2025-09-06 15:36:25,172 INFO misc.py line 117 1407060] Train: [14/100][241/1462] Data 0.001 (0.001) Batch 2.830 (2.877) Remain 101:27:17 loss: 0.3486 Lr: 0.00200
[2025-09-06 15:36:28,151 INFO misc.py line 117 1407060] Train: [14/100][242/1462] Data 0.002 (0.001) Batch 2.979 (2.877) Remain 101:28:09 loss: 0.3817 Lr: 0.00200
[2025-09-06 15:36:30,752 INFO misc.py line 117 1407060] Train: [14/100][243/1462] Data 0.001 (0.001) Batch 2.601 (2.876) Remain 101:25:39 loss: 1.7567 Lr: 0.00200
[2025-09-06 15:36:34,404 INFO misc.py line 117 1407060] Train: [14/100][244/1462] Data 0.001 (0.001) Batch 3.652 (2.879) Remain 101:32:25 loss: 0.5856 Lr: 0.00200
[2025-09-06 15:36:37,376 INFO misc.py line 117 1407060] Train: [14/100][245/1462] Data 0.001 (0.001) Batch 2.972 (2.880) Remain 101:33:11 loss: 0.3033 Lr: 0.00200
[2025-09-06 15:36:40,087 INFO misc.py line 117 1407060] Train: [14/100][246/1462] Data 0.001 (0.001) Batch 2.712 (2.879) Remain 101:31:40 loss: 0.2989 Lr: 0.00200
[2025-09-06 15:36:42,724 INFO misc.py line 117 1407060] Train: [14/100][247/1462] Data 0.001 (0.001) Batch 2.636 (2.878) Remain 101:29:31 loss: 0.4504 Lr: 0.00200
[2025-09-06 15:36:45,745 INFO misc.py line 117 1407060] Train: [14/100][248/1462] Data 0.001 (0.001) Batch 3.021 (2.879) Remain 101:30:42 loss: 0.3376 Lr: 0.00200
[2025-09-06 15:36:49,248 INFO misc.py line 117 1407060] Train: [14/100][249/1462] Data 0.002 (0.001) Batch 3.503 (2.881) Remain 101:36:01 loss: 0.5995 Lr: 0.00200
[2025-09-06 15:36:52,162 INFO misc.py line 117 1407060] Train: [14/100][250/1462] Data 0.001 (0.001) Batch 2.914 (2.881) Remain 101:36:15 loss: 0.5621 Lr: 0.00200
[2025-09-06 15:36:54,955 INFO misc.py line 117 1407060] Train: [14/100][251/1462] Data 0.001 (0.001) Batch 2.793 (2.881) Remain 101:35:27 loss: 0.6212 Lr: 0.00200
[2025-09-06 15:36:57,951 INFO misc.py line 117 1407060] Train: [14/100][252/1462] Data 0.001 (0.001) Batch 2.996 (2.882) Remain 101:36:23 loss: 0.5101 Lr: 0.00200
[2025-09-06 15:37:00,608 INFO misc.py line 117 1407060] Train: [14/100][253/1462] Data 0.002 (0.001) Batch 2.657 (2.881) Remain 101:34:26 loss: 0.4212 Lr: 0.00200
[2025-09-06 15:37:03,675 INFO misc.py line 117 1407060] Train: [14/100][254/1462] Data 0.001 (0.001) Batch 3.067 (2.881) Remain 101:35:58 loss: 0.2968 Lr: 0.00200
[2025-09-06 15:37:06,456 INFO misc.py line 117 1407060] Train: [14/100][255/1462] Data 0.002 (0.001) Batch 2.781 (2.881) Remain 101:35:04 loss: 0.3855 Lr: 0.00200
[2025-09-06 15:37:09,250 INFO misc.py line 117 1407060] Train: [14/100][256/1462] Data 0.002 (0.001) Batch 2.794 (2.881) Remain 101:34:18 loss: 0.5524 Lr: 0.00200
[2025-09-06 15:37:12,191 INFO misc.py line 117 1407060] Train: [14/100][257/1462] Data 0.002 (0.001) Batch 2.941 (2.881) Remain 101:34:45 loss: 0.3147 Lr: 0.00200
[2025-09-06 15:37:14,943 INFO misc.py line 117 1407060] Train: [14/100][258/1462] Data 0.001 (0.001) Batch 2.751 (2.880) Remain 101:33:38 loss: 0.3254 Lr: 0.00200
[2025-09-06 15:37:18,121 INFO misc.py line 117 1407060] Train: [14/100][259/1462] Data 0.002 (0.001) Batch 3.178 (2.881) Remain 101:36:03 loss: 0.5076 Lr: 0.00200
[2025-09-06 15:37:20,945 INFO misc.py line 117 1407060] Train: [14/100][260/1462] Data 0.002 (0.001) Batch 2.824 (2.881) Remain 101:35:31 loss: 0.4137 Lr: 0.00200
[2025-09-06 15:37:23,642 INFO misc.py line 117 1407060] Train: [14/100][261/1462] Data 0.002 (0.001) Batch 2.697 (2.881) Remain 101:33:58 loss: 0.5658 Lr: 0.00200
[2025-09-06 15:37:26,100 INFO misc.py line 117 1407060] Train: [14/100][262/1462] Data 0.002 (0.001) Batch 2.458 (2.879) Remain 101:30:28 loss: 0.5208 Lr: 0.00200
[2025-09-06 15:37:29,048 INFO misc.py line 117 1407060] Train: [14/100][263/1462] Data 0.002 (0.001) Batch 2.948 (2.879) Remain 101:30:58 loss: 0.5699 Lr: 0.00200
[2025-09-06 15:37:31,841 INFO misc.py line 117 1407060] Train: [14/100][264/1462] Data 0.001 (0.001) Batch 2.793 (2.879) Remain 101:30:14 loss: 0.7399 Lr: 0.00200
[2025-09-06 15:37:34,595 INFO misc.py line 117 1407060] Train: [14/100][265/1462] Data 0.002 (0.001) Batch 2.755 (2.878) Remain 101:29:11 loss: 0.5825 Lr: 0.00200
[2025-09-06 15:37:37,698 INFO misc.py line 117 1407060] Train: [14/100][266/1462] Data 0.002 (0.001) Batch 3.103 (2.879) Remain 101:30:56 loss: 0.3105 Lr: 0.00200
[2025-09-06 15:37:40,555 INFO misc.py line 117 1407060] Train: [14/100][267/1462] Data 0.002 (0.001) Batch 2.856 (2.879) Remain 101:30:42 loss: 0.5965 Lr: 0.00200
[2025-09-06 15:37:43,461 INFO misc.py line 117 1407060] Train: [14/100][268/1462] Data 0.002 (0.001) Batch 2.906 (2.879) Remain 101:30:52 loss: 0.2795 Lr: 0.00200
[2025-09-06 15:37:46,394 INFO misc.py line 117 1407060] Train: [14/100][269/1462] Data 0.002 (0.001) Batch 2.933 (2.879) Remain 101:31:15 loss: 0.4199 Lr: 0.00200
[2025-09-06 15:37:49,725 INFO misc.py line 117 1407060] Train: [14/100][270/1462] Data 0.001 (0.001) Batch 3.331 (2.881) Remain 101:34:47 loss: 0.4228 Lr: 0.00200
[2025-09-06 15:37:52,798 INFO misc.py line 117 1407060] Train: [14/100][271/1462] Data 0.002 (0.001) Batch 3.072 (2.882) Remain 101:36:15 loss: 0.4119 Lr: 0.00200
[2025-09-06 15:37:55,535 INFO misc.py line 117 1407060] Train: [14/100][272/1462] Data 0.001 (0.001) Batch 2.737 (2.881) Remain 101:35:03 loss: 0.4509 Lr: 0.00200
[2025-09-06 15:37:58,569 INFO misc.py line 117 1407060] Train: [14/100][273/1462] Data 0.001 (0.001) Batch 3.035 (2.882) Remain 101:36:13 loss: 0.4602 Lr: 0.00200
[2025-09-06 15:38:01,391 INFO misc.py line 117 1407060] Train: [14/100][274/1462] Data 0.001 (0.001) Batch 2.822 (2.882) Remain 101:35:42 loss: 0.2293 Lr: 0.00200
[2025-09-06 15:38:03,993 INFO misc.py line 117 1407060] Train: [14/100][275/1462] Data 0.002 (0.001) Batch 2.602 (2.881) Remain 101:33:28 loss: 0.4009 Lr: 0.00200
[2025-09-06 15:38:06,607 INFO misc.py line 117 1407060] Train: [14/100][276/1462] Data 0.001 (0.001) Batch 2.614 (2.880) Remain 101:31:21 loss: 0.2790 Lr: 0.00200
[2025-09-06 15:38:09,497 INFO misc.py line 117 1407060] Train: [14/100][277/1462] Data 0.002 (0.001) Batch 2.890 (2.880) Remain 101:31:23 loss: 0.3642 Lr: 0.00200
[2025-09-06 15:38:12,327 INFO misc.py line 117 1407060] Train: [14/100][278/1462] Data 0.002 (0.001) Batch 2.830 (2.880) Remain 101:30:58 loss: 0.7062 Lr: 0.00200
[2025-09-06 15:38:15,057 INFO misc.py line 117 1407060] Train: [14/100][279/1462] Data 0.002 (0.001) Batch 2.730 (2.879) Remain 101:29:46 loss: 0.5292 Lr: 0.00200
[2025-09-06 15:38:18,259 INFO misc.py line 117 1407060] Train: [14/100][280/1462] Data 0.002 (0.001) Batch 3.201 (2.880) Remain 101:32:11 loss: 0.5638 Lr: 0.00200
[2025-09-06 15:38:20,977 INFO misc.py line 117 1407060] Train: [14/100][281/1462] Data 0.002 (0.001) Batch 2.718 (2.880) Remain 101:30:54 loss: 0.4602 Lr: 0.00200
[2025-09-06 15:38:23,775 INFO misc.py line 117 1407060] Train: [14/100][282/1462] Data 0.002 (0.001) Batch 2.798 (2.879) Remain 101:30:14 loss: 0.4932 Lr: 0.00200
[2025-09-06 15:38:26,607 INFO misc.py line 117 1407060] Train: [14/100][283/1462] Data 0.001 (0.001) Batch 2.832 (2.879) Remain 101:29:50 loss: 0.2648 Lr: 0.00200
[2025-09-06 15:38:29,485 INFO misc.py line 117 1407060] Train: [14/100][284/1462] Data 0.001 (0.001) Batch 2.878 (2.879) Remain 101:29:46 loss: 0.4247 Lr: 0.00200
[2025-09-06 15:38:32,241 INFO misc.py line 117 1407060] Train: [14/100][285/1462] Data 0.002 (0.001) Batch 2.756 (2.879) Remain 101:28:48 loss: 0.5040 Lr: 0.00200
[2025-09-06 15:38:35,059 INFO misc.py line 117 1407060] Train: [14/100][286/1462] Data 0.001 (0.001) Batch 2.818 (2.878) Remain 101:28:18 loss: 0.3933 Lr: 0.00200
[2025-09-06 15:38:37,961 INFO misc.py line 117 1407060] Train: [14/100][287/1462] Data 0.001 (0.001) Batch 2.902 (2.879) Remain 101:28:26 loss: 0.4018 Lr: 0.00200
[2025-09-06 15:38:40,749 INFO misc.py line 117 1407060] Train: [14/100][288/1462] Data 0.002 (0.001) Batch 2.788 (2.878) Remain 101:27:43 loss: 0.3525 Lr: 0.00200
[2025-09-06 15:38:43,356 INFO misc.py line 117 1407060] Train: [14/100][289/1462] Data 0.002 (0.001) Batch 2.607 (2.877) Remain 101:25:39 loss: 0.2341 Lr: 0.00200
[2025-09-06 15:38:46,051 INFO misc.py line 117 1407060] Train: [14/100][290/1462] Data 0.002 (0.001) Batch 2.694 (2.877) Remain 101:24:16 loss: 0.3853 Lr: 0.00200
[2025-09-06 15:38:48,729 INFO misc.py line 117 1407060] Train: [14/100][291/1462] Data 0.002 (0.001) Batch 2.679 (2.876) Remain 101:22:45 loss: 0.3211 Lr: 0.00200
[2025-09-06 15:38:51,324 INFO misc.py line 117 1407060] Train: [14/100][292/1462] Data 0.002 (0.001) Batch 2.594 (2.875) Remain 101:20:39 loss: 0.3743 Lr: 0.00200
[2025-09-06 15:38:53,943 INFO misc.py line 117 1407060] Train: [14/100][293/1462] Data 0.002 (0.001) Batch 2.619 (2.874) Remain 101:18:44 loss: 0.6210 Lr: 0.00200
[2025-09-06 15:38:57,125 INFO misc.py line 117 1407060] Train: [14/100][294/1462] Data 0.002 (0.001) Batch 3.182 (2.875) Remain 101:20:56 loss: 0.3810 Lr: 0.00200
[2025-09-06 15:38:59,932 INFO misc.py line 117 1407060] Train: [14/100][295/1462] Data 0.001 (0.001) Batch 2.807 (2.875) Remain 101:20:23 loss: 0.4513 Lr: 0.00200
[2025-09-06 15:39:02,503 INFO misc.py line 117 1407060] Train: [14/100][296/1462] Data 0.001 (0.001) Batch 2.571 (2.874) Remain 101:18:09 loss: 0.2798 Lr: 0.00200
[2025-09-06 15:39:05,141 INFO misc.py line 117 1407060] Train: [14/100][297/1462] Data 0.001 (0.001) Batch 2.638 (2.873) Remain 101:16:24 loss: 0.6869 Lr: 0.00200
[2025-09-06 15:39:08,232 INFO misc.py line 117 1407060] Train: [14/100][298/1462] Data 0.001 (0.001) Batch 3.091 (2.874) Remain 101:17:55 loss: 0.6656 Lr: 0.00200
[2025-09-06 15:39:11,712 INFO misc.py line 117 1407060] Train: [14/100][299/1462] Data 0.001 (0.001) Batch 3.481 (2.876) Remain 101:22:12 loss: 0.3767 Lr: 0.00200
[2025-09-06 15:39:14,779 INFO misc.py line 117 1407060] Train: [14/100][300/1462] Data 0.001 (0.001) Batch 3.067 (2.877) Remain 101:23:31 loss: 0.3218 Lr: 0.00200
[2025-09-06 15:39:17,606 INFO misc.py line 117 1407060] Train: [14/100][301/1462] Data 0.001 (0.001) Batch 2.827 (2.876) Remain 101:23:07 loss: 0.2581 Lr: 0.00200
[2025-09-06 15:39:20,719 INFO misc.py line 117 1407060] Train: [14/100][302/1462] Data 0.001 (0.001) Batch 3.113 (2.877) Remain 101:24:44 loss: 0.2283 Lr: 0.00200
[2025-09-06 15:39:23,184 INFO misc.py line 117 1407060] Train: [14/100][303/1462] Data 0.001 (0.001) Batch 2.465 (2.876) Remain 101:21:47 loss: 0.4178 Lr: 0.00200
[2025-09-06 15:39:26,018 INFO misc.py line 117 1407060] Train: [14/100][304/1462] Data 0.001 (0.001) Batch 2.834 (2.876) Remain 101:21:26 loss: 0.4508 Lr: 0.00200
[2025-09-06 15:39:28,881 INFO misc.py line 117 1407060] Train: [14/100][305/1462] Data 0.002 (0.001) Batch 2.863 (2.876) Remain 101:21:18 loss: 0.3204 Lr: 0.00200
[2025-09-06 15:39:31,903 INFO misc.py line 117 1407060] Train: [14/100][306/1462] Data 0.002 (0.001) Batch 3.022 (2.876) Remain 101:22:17 loss: 0.2736 Lr: 0.00200
[2025-09-06 15:39:34,583 INFO misc.py line 117 1407060] Train: [14/100][307/1462] Data 0.001 (0.001) Batch 2.680 (2.875) Remain 101:20:52 loss: 0.5401 Lr: 0.00200
[2025-09-06 15:39:37,729 INFO misc.py line 117 1407060] Train: [14/100][308/1462] Data 0.002 (0.001) Batch 3.146 (2.876) Remain 101:22:42 loss: 0.2487 Lr: 0.00200
[2025-09-06 15:39:40,640 INFO misc.py line 117 1407060] Train: [14/100][309/1462] Data 0.001 (0.001) Batch 2.911 (2.876) Remain 101:22:53 loss: 0.5986 Lr: 0.00200
[2025-09-06 15:39:43,380 INFO misc.py line 117 1407060] Train: [14/100][310/1462] Data 0.002 (0.001) Batch 2.740 (2.876) Remain 101:21:54 loss: 0.3929 Lr: 0.00200
[2025-09-06 15:39:46,017 INFO misc.py line 117 1407060] Train: [14/100][311/1462] Data 0.002 (0.001) Batch 2.637 (2.875) Remain 101:20:13 loss: 0.3734 Lr: 0.00200
[2025-09-06 15:39:49,161 INFO misc.py line 117 1407060] Train: [14/100][312/1462] Data 0.002 (0.001) Batch 3.144 (2.876) Remain 101:22:00 loss: 0.6874 Lr: 0.00200
[2025-09-06 15:39:52,125 INFO misc.py line 117 1407060] Train: [14/100][313/1462] Data 0.002 (0.001) Batch 2.964 (2.876) Remain 101:22:33 loss: 0.6048 Lr: 0.00200
[2025-09-06 15:39:54,683 INFO misc.py line 117 1407060] Train: [14/100][314/1462] Data 0.002 (0.001) Batch 2.558 (2.875) Remain 101:20:21 loss: 0.3520 Lr: 0.00200
[2025-09-06 15:39:57,856 INFO misc.py line 117 1407060] Train: [14/100][315/1462] Data 0.001 (0.001) Batch 3.173 (2.876) Remain 101:22:19 loss: 0.6127 Lr: 0.00200
[2025-09-06 15:40:00,756 INFO misc.py line 117 1407060] Train: [14/100][316/1462] Data 0.002 (0.001) Batch 2.900 (2.876) Remain 101:22:25 loss: 0.3784 Lr: 0.00200
[2025-09-06 15:40:03,777 INFO misc.py line 117 1407060] Train: [14/100][317/1462] Data 0.001 (0.001) Batch 3.021 (2.877) Remain 101:23:21 loss: 0.2538 Lr: 0.00200
[2025-09-06 15:40:06,413 INFO misc.py line 117 1407060] Train: [14/100][318/1462] Data 0.001 (0.001) Batch 2.637 (2.876) Remain 101:21:41 loss: 0.5728 Lr: 0.00200
[2025-09-06 15:40:09,266 INFO misc.py line 117 1407060] Train: [14/100][319/1462] Data 0.001 (0.001) Batch 2.853 (2.876) Remain 101:21:29 loss: 0.5479 Lr: 0.00200
[2025-09-06 15:40:12,012 INFO misc.py line 117 1407060] Train: [14/100][320/1462] Data 0.002 (0.001) Batch 2.746 (2.876) Remain 101:20:34 loss: 0.5340 Lr: 0.00200
[2025-09-06 15:40:14,898 INFO misc.py line 117 1407060] Train: [14/100][321/1462] Data 0.002 (0.001) Batch 2.887 (2.876) Remain 101:20:36 loss: 0.3812 Lr: 0.00200
[2025-09-06 15:40:17,639 INFO misc.py line 117 1407060] Train: [14/100][322/1462] Data 0.001 (0.001) Batch 2.741 (2.875) Remain 101:19:39 loss: 0.4533 Lr: 0.00200
[2025-09-06 15:40:20,764 INFO misc.py line 117 1407060] Train: [14/100][323/1462] Data 0.001 (0.001) Batch 3.125 (2.876) Remain 101:21:15 loss: 0.5004 Lr: 0.00200
[2025-09-06 15:40:23,406 INFO misc.py line 117 1407060] Train: [14/100][324/1462] Data 0.001 (0.001) Batch 2.642 (2.875) Remain 101:19:40 loss: 0.4183 Lr: 0.00200
[2025-09-06 15:40:26,020 INFO misc.py line 117 1407060] Train: [14/100][325/1462] Data 0.001 (0.001) Batch 2.614 (2.874) Remain 101:17:54 loss: 0.3856 Lr: 0.00200
[2025-09-06 15:40:28,904 INFO misc.py line 117 1407060] Train: [14/100][326/1462] Data 0.001 (0.001) Batch 2.884 (2.874) Remain 101:17:55 loss: 0.2315 Lr: 0.00200
[2025-09-06 15:40:32,198 INFO misc.py line 117 1407060] Train: [14/100][327/1462] Data 0.002 (0.001) Batch 3.294 (2.876) Remain 101:20:37 loss: 0.2716 Lr: 0.00200
[2025-09-06 15:40:35,127 INFO misc.py line 117 1407060] Train: [14/100][328/1462] Data 0.001 (0.001) Batch 2.928 (2.876) Remain 101:20:54 loss: 0.5838 Lr: 0.00200
[2025-09-06 15:40:37,801 INFO misc.py line 117 1407060] Train: [14/100][329/1462] Data 0.001 (0.001) Batch 2.674 (2.875) Remain 101:19:33 loss: 0.3196 Lr: 0.00200
[2025-09-06 15:40:40,478 INFO misc.py line 117 1407060] Train: [14/100][330/1462] Data 0.001 (0.001) Batch 2.677 (2.875) Remain 101:18:13 loss: 0.3116 Lr: 0.00200
[2025-09-06 15:40:43,386 INFO misc.py line 117 1407060] Train: [14/100][331/1462] Data 0.001 (0.001) Batch 2.908 (2.875) Remain 101:18:23 loss: 0.5910 Lr: 0.00200
[2025-09-06 15:40:46,400 INFO misc.py line 117 1407060] Train: [14/100][332/1462] Data 0.002 (0.001) Batch 3.014 (2.875) Remain 101:19:14 loss: 0.4493 Lr: 0.00200
[2025-09-06 15:40:49,355 INFO misc.py line 117 1407060] Train: [14/100][333/1462] Data 0.002 (0.001) Batch 2.955 (2.875) Remain 101:19:42 loss: 0.3098 Lr: 0.00200
[2025-09-06 15:40:51,990 INFO misc.py line 117 1407060] Train: [14/100][334/1462] Data 0.001 (0.001) Batch 2.635 (2.875) Remain 101:18:07 loss: 0.2237 Lr: 0.00200
[2025-09-06 15:40:54,827 INFO misc.py line 117 1407060] Train: [14/100][335/1462] Data 0.001 (0.001) Batch 2.837 (2.875) Remain 101:17:49 loss: 0.4566 Lr: 0.00200
[2025-09-06 15:40:58,091 INFO misc.py line 117 1407060] Train: [14/100][336/1462] Data 0.001 (0.001) Batch 3.264 (2.876) Remain 101:20:15 loss: 0.2915 Lr: 0.00200
[2025-09-06 15:41:01,050 INFO misc.py line 117 1407060] Train: [14/100][337/1462] Data 0.001 (0.001) Batch 2.958 (2.876) Remain 101:20:43 loss: 0.3554 Lr: 0.00200
[2025-09-06 15:41:03,806 INFO misc.py line 117 1407060] Train: [14/100][338/1462] Data 0.001 (0.001) Batch 2.757 (2.876) Remain 101:19:55 loss: 0.4264 Lr: 0.00200
[2025-09-06 15:41:06,880 INFO misc.py line 117 1407060] Train: [14/100][339/1462] Data 0.001 (0.001) Batch 3.074 (2.876) Remain 101:21:07 loss: 0.3765 Lr: 0.00200
[2025-09-06 15:41:09,541 INFO misc.py line 117 1407060] Train: [14/100][340/1462] Data 0.001 (0.001) Batch 2.660 (2.876) Remain 101:19:43 loss: 0.6125 Lr: 0.00200
[2025-09-06 15:41:12,317 INFO misc.py line 117 1407060] Train: [14/100][341/1462] Data 0.001 (0.001) Batch 2.776 (2.875) Remain 101:19:03 loss: 0.6468 Lr: 0.00200
[2025-09-06 15:41:15,117 INFO misc.py line 117 1407060] Train: [14/100][342/1462] Data 0.001 (0.001) Batch 2.800 (2.875) Remain 101:18:32 loss: 0.4902 Lr: 0.00200
[2025-09-06 15:41:17,759 INFO misc.py line 117 1407060] Train: [14/100][343/1462] Data 0.001 (0.001) Batch 2.642 (2.874) Remain 101:17:02 loss: 0.2830 Lr: 0.00200
[2025-09-06 15:41:20,500 INFO misc.py line 117 1407060] Train: [14/100][344/1462] Data 0.001 (0.001) Batch 2.741 (2.874) Remain 101:16:10 loss: 0.2396 Lr: 0.00200
[2025-09-06 15:41:23,316 INFO misc.py line 117 1407060] Train: [14/100][345/1462] Data 0.001 (0.001) Batch 2.816 (2.874) Remain 101:15:45 loss: 0.4373 Lr: 0.00200
[2025-09-06 15:41:26,425 INFO misc.py line 117 1407060] Train: [14/100][346/1462] Data 0.001 (0.001) Batch 3.109 (2.875) Remain 101:17:09 loss: 0.2846 Lr: 0.00200
[2025-09-06 15:41:29,369 INFO misc.py line 117 1407060] Train: [14/100][347/1462] Data 0.001 (0.001) Batch 2.943 (2.875) Remain 101:17:32 loss: 0.3278 Lr: 0.00200
[2025-09-06 15:41:32,179 INFO misc.py line 117 1407060] Train: [14/100][348/1462] Data 0.001 (0.001) Batch 2.811 (2.875) Remain 101:17:06 loss: 0.3792 Lr: 0.00200
[2025-09-06 15:41:34,879 INFO misc.py line 117 1407060] Train: [14/100][349/1462] Data 0.001 (0.001) Batch 2.700 (2.874) Remain 101:15:59 loss: 0.3708 Lr: 0.00200
[2025-09-06 15:41:37,781 INFO misc.py line 117 1407060] Train: [14/100][350/1462] Data 0.002 (0.001) Batch 2.902 (2.874) Remain 101:16:06 loss: 0.5083 Lr: 0.00200
[2025-09-06 15:41:40,604 INFO misc.py line 117 1407060] Train: [14/100][351/1462] Data 0.002 (0.001) Batch 2.822 (2.874) Remain 101:15:44 loss: 0.2993 Lr: 0.00200
[2025-09-06 15:41:43,190 INFO misc.py line 117 1407060] Train: [14/100][352/1462] Data 0.001 (0.001) Batch 2.586 (2.873) Remain 101:13:57 loss: 0.4746 Lr: 0.00200
[2025-09-06 15:41:46,291 INFO misc.py line 117 1407060] Train: [14/100][353/1462] Data 0.001 (0.001) Batch 3.101 (2.874) Remain 101:15:16 loss: 0.5219 Lr: 0.00200
[2025-09-06 15:41:49,081 INFO misc.py line 117 1407060] Train: [14/100][354/1462] Data 0.001 (0.001) Batch 2.790 (2.874) Remain 101:14:43 loss: 0.3766 Lr: 0.00200
[2025-09-06 15:41:52,004 INFO misc.py line 117 1407060] Train: [14/100][355/1462] Data 0.001 (0.001) Batch 2.922 (2.874) Remain 101:14:58 loss: 0.4137 Lr: 0.00200
[2025-09-06 15:41:55,106 INFO misc.py line 117 1407060] Train: [14/100][356/1462] Data 0.002 (0.001) Batch 3.102 (2.874) Remain 101:16:17 loss: 0.2566 Lr: 0.00200
[2025-09-06 15:41:57,957 INFO misc.py line 117 1407060] Train: [14/100][357/1462] Data 0.002 (0.001) Batch 2.851 (2.874) Remain 101:16:06 loss: 0.2745 Lr: 0.00200
[2025-09-06 15:42:00,977 INFO misc.py line 117 1407060] Train: [14/100][358/1462] Data 0.002 (0.001) Batch 3.020 (2.875) Remain 101:16:55 loss: 0.4444 Lr: 0.00200
[2025-09-06 15:42:03,918 INFO misc.py line 117 1407060] Train: [14/100][359/1462] Data 0.001 (0.001) Batch 2.941 (2.875) Remain 101:17:16 loss: 0.4037 Lr: 0.00200
[2025-09-06 15:42:06,863 INFO misc.py line 117 1407060] Train: [14/100][360/1462] Data 0.001 (0.001) Batch 2.945 (2.875) Remain 101:17:38 loss: 0.4870 Lr: 0.00200
[2025-09-06 15:42:09,673 INFO misc.py line 117 1407060] Train: [14/100][361/1462] Data 0.001 (0.001) Batch 2.810 (2.875) Remain 101:17:12 loss: 0.2630 Lr: 0.00200
[2025-09-06 15:42:12,268 INFO misc.py line 117 1407060] Train: [14/100][362/1462] Data 0.001 (0.001) Batch 2.594 (2.874) Remain 101:15:30 loss: 0.8480 Lr: 0.00200
[2025-09-06 15:42:15,321 INFO misc.py line 117 1407060] Train: [14/100][363/1462] Data 0.001 (0.001) Batch 3.054 (2.875) Remain 101:16:31 loss: 0.4464 Lr: 0.00200
[2025-09-06 15:42:18,006 INFO misc.py line 117 1407060] Train: [14/100][364/1462] Data 0.001 (0.001) Batch 2.684 (2.874) Remain 101:15:21 loss: 0.7077 Lr: 0.00200
[2025-09-06 15:42:20,757 INFO misc.py line 117 1407060] Train: [14/100][365/1462] Data 0.002 (0.001) Batch 2.751 (2.874) Remain 101:14:35 loss: 0.3314 Lr: 0.00200
[2025-09-06 15:42:23,666 INFO misc.py line 117 1407060] Train: [14/100][366/1462] Data 0.001 (0.001) Batch 2.909 (2.874) Remain 101:14:44 loss: 0.6432 Lr: 0.00200
[2025-09-06 15:42:26,429 INFO misc.py line 117 1407060] Train: [14/100][367/1462] Data 0.002 (0.001) Batch 2.763 (2.874) Remain 101:14:03 loss: 0.4662 Lr: 0.00200
[2025-09-06 15:42:29,432 INFO misc.py line 117 1407060] Train: [14/100][368/1462] Data 0.002 (0.001) Batch 3.003 (2.874) Remain 101:14:45 loss: 0.4788 Lr: 0.00200
[2025-09-06 15:42:32,541 INFO misc.py line 117 1407060] Train: [14/100][369/1462] Data 0.001 (0.001) Batch 3.109 (2.875) Remain 101:16:04 loss: 0.3738 Lr: 0.00200
[2025-09-06 15:42:35,443 INFO misc.py line 117 1407060] Train: [14/100][370/1462] Data 0.001 (0.001) Batch 2.902 (2.875) Remain 101:16:10 loss: 0.7420 Lr: 0.00200
[2025-09-06 15:42:38,539 INFO misc.py line 117 1407060] Train: [14/100][371/1462] Data 0.002 (0.001) Batch 3.096 (2.875) Remain 101:17:24 loss: 0.4302 Lr: 0.00200
[2025-09-06 15:42:41,682 INFO misc.py line 117 1407060] Train: [14/100][372/1462] Data 0.001 (0.001) Batch 3.143 (2.876) Remain 101:18:53 loss: 0.4716 Lr: 0.00200
[2025-09-06 15:42:44,867 INFO misc.py line 117 1407060] Train: [14/100][373/1462] Data 0.002 (0.001) Batch 3.184 (2.877) Remain 101:20:36 loss: 0.5275 Lr: 0.00200
[2025-09-06 15:42:47,753 INFO misc.py line 117 1407060] Train: [14/100][374/1462] Data 0.001 (0.001) Batch 2.886 (2.877) Remain 101:20:36 loss: 0.3930 Lr: 0.00200
[2025-09-06 15:42:50,536 INFO misc.py line 117 1407060] Train: [14/100][375/1462] Data 0.001 (0.001) Batch 2.783 (2.877) Remain 101:20:01 loss: 0.4010 Lr: 0.00200
[2025-09-06 15:42:53,661 INFO misc.py line 117 1407060] Train: [14/100][376/1462] Data 0.002 (0.001) Batch 3.125 (2.877) Remain 101:21:23 loss: 0.5357 Lr: 0.00200
[2025-09-06 15:42:56,550 INFO misc.py line 117 1407060] Train: [14/100][377/1462] Data 0.002 (0.001) Batch 2.889 (2.877) Remain 101:21:24 loss: 0.5460 Lr: 0.00200
[2025-09-06 15:42:59,554 INFO misc.py line 117 1407060] Train: [14/100][378/1462] Data 0.001 (0.001) Batch 3.004 (2.878) Remain 101:22:04 loss: 0.2927 Lr: 0.00200
[2025-09-06 15:43:02,530 INFO misc.py line 117 1407060] Train: [14/100][379/1462] Data 0.002 (0.001) Batch 2.976 (2.878) Remain 101:22:34 loss: 0.6794 Lr: 0.00200
[2025-09-06 15:43:05,188 INFO misc.py line 117 1407060] Train: [14/100][380/1462] Data 0.002 (0.001) Batch 2.658 (2.877) Remain 101:21:18 loss: 0.5420 Lr: 0.00200
[2025-09-06 15:43:07,933 INFO misc.py line 117 1407060] Train: [14/100][381/1462] Data 0.002 (0.001) Batch 2.745 (2.877) Remain 101:20:30 loss: 0.6363 Lr: 0.00200
[2025-09-06 15:43:10,642 INFO misc.py line 117 1407060] Train: [14/100][382/1462] Data 0.001 (0.001) Batch 2.709 (2.876) Remain 101:19:31 loss: 0.4110 Lr: 0.00200
[2025-09-06 15:43:13,403 INFO misc.py line 117 1407060] Train: [14/100][383/1462] Data 0.001 (0.001) Batch 2.761 (2.876) Remain 101:18:50 loss: 0.5294 Lr: 0.00200
[2025-09-06 15:43:16,652 INFO misc.py line 117 1407060] Train: [14/100][384/1462] Data 0.002 (0.001) Batch 3.249 (2.877) Remain 101:20:51 loss: 0.3995 Lr: 0.00200
[2025-09-06 15:43:19,068 INFO misc.py line 117 1407060] Train: [14/100][385/1462] Data 0.001 (0.001) Batch 2.416 (2.876) Remain 101:18:15 loss: 0.6777 Lr: 0.00200
[2025-09-06 15:43:22,204 INFO misc.py line 117 1407060] Train: [14/100][386/1462] Data 0.001 (0.001) Batch 3.136 (2.877) Remain 101:19:38 loss: 0.6933 Lr: 0.00200
[2025-09-06 15:43:25,257 INFO misc.py line 117 1407060] Train: [14/100][387/1462] Data 0.001 (0.001) Batch 3.054 (2.877) Remain 101:20:34 loss: 0.2944 Lr: 0.00200
[2025-09-06 15:43:28,082 INFO misc.py line 117 1407060] Train: [14/100][388/1462] Data 0.001 (0.001) Batch 2.825 (2.877) Remain 101:20:14 loss: 0.6706 Lr: 0.00200
[2025-09-06 15:43:30,901 INFO misc.py line 117 1407060] Train: [14/100][389/1462] Data 0.001 (0.001) Batch 2.819 (2.877) Remain 101:19:52 loss: 0.7936 Lr: 0.00200
[2025-09-06 15:43:34,264 INFO misc.py line 117 1407060] Train: [14/100][390/1462] Data 0.002 (0.001) Batch 3.363 (2.878) Remain 101:22:28 loss: 0.3370 Lr: 0.00200
[2025-09-06 15:43:36,827 INFO misc.py line 117 1407060] Train: [14/100][391/1462] Data 0.001 (0.001) Batch 2.563 (2.877) Remain 101:20:42 loss: 0.6113 Lr: 0.00200
[2025-09-06 15:43:39,756 INFO misc.py line 117 1407060] Train: [14/100][392/1462] Data 0.002 (0.001) Batch 2.929 (2.877) Remain 101:20:56 loss: 0.3943 Lr: 0.00200
[2025-09-06 15:43:42,475 INFO misc.py line 117 1407060] Train: [14/100][393/1462] Data 0.002 (0.001) Batch 2.719 (2.877) Remain 101:20:02 loss: 0.6719 Lr: 0.00200
[2025-09-06 15:43:45,424 INFO misc.py line 117 1407060] Train: [14/100][394/1462] Data 0.001 (0.001) Batch 2.949 (2.877) Remain 101:20:22 loss: 0.4467 Lr: 0.00200
[2025-09-06 15:43:48,536 INFO misc.py line 117 1407060] Train: [14/100][395/1462] Data 0.001 (0.001) Batch 3.112 (2.878) Remain 101:21:36 loss: 0.4171 Lr: 0.00200
[2025-09-06 15:43:51,330 INFO misc.py line 117 1407060] Train: [14/100][396/1462] Data 0.001 (0.001) Batch 2.794 (2.878) Remain 101:21:06 loss: 0.5998 Lr: 0.00200
[2025-09-06 15:43:54,420 INFO misc.py line 117 1407060] Train: [14/100][397/1462] Data 0.001 (0.001) Batch 3.090 (2.878) Remain 101:22:11 loss: 0.7498 Lr: 0.00200
[2025-09-06 15:43:57,091 INFO misc.py line 117 1407060] Train: [14/100][398/1462] Data 0.001 (0.001) Batch 2.671 (2.878) Remain 101:21:02 loss: 0.3161 Lr: 0.00200
[2025-09-06 15:44:00,212 INFO misc.py line 117 1407060] Train: [14/100][399/1462] Data 0.002 (0.001) Batch 3.121 (2.878) Remain 101:22:17 loss: 0.4294 Lr: 0.00200
[2025-09-06 15:44:03,383 INFO misc.py line 117 1407060] Train: [14/100][400/1462] Data 0.002 (0.001) Batch 3.171 (2.879) Remain 101:23:47 loss: 0.3080 Lr: 0.00200
[2025-09-06 15:44:06,407 INFO misc.py line 117 1407060] Train: [14/100][401/1462] Data 0.001 (0.001) Batch 3.024 (2.879) Remain 101:24:31 loss: 0.2933 Lr: 0.00200
[2025-09-06 15:44:09,356 INFO misc.py line 117 1407060] Train: [14/100][402/1462] Data 0.001 (0.001) Batch 2.949 (2.879) Remain 101:24:50 loss: 0.4461 Lr: 0.00200
[2025-09-06 15:44:12,027 INFO misc.py line 117 1407060] Train: [14/100][403/1462] Data 0.002 (0.001) Batch 2.670 (2.879) Remain 101:23:41 loss: 0.7139 Lr: 0.00200
[2025-09-06 15:44:14,731 INFO misc.py line 117 1407060] Train: [14/100][404/1462] Data 0.001 (0.001) Batch 2.704 (2.878) Remain 101:22:43 loss: 0.3468 Lr: 0.00200
[2025-09-06 15:44:17,830 INFO misc.py line 117 1407060] Train: [14/100][405/1462] Data 0.001 (0.001) Batch 3.099 (2.879) Remain 101:23:50 loss: 0.2728 Lr: 0.00200
[2025-09-06 15:44:20,742 INFO misc.py line 117 1407060] Train: [14/100][406/1462] Data 0.002 (0.001) Batch 2.913 (2.879) Remain 101:23:57 loss: 0.7782 Lr: 0.00200
[2025-09-06 15:44:23,515 INFO misc.py line 117 1407060] Train: [14/100][407/1462] Data 0.001 (0.001) Batch 2.773 (2.879) Remain 101:23:21 loss: 0.3106 Lr: 0.00200
[2025-09-06 15:44:26,311 INFO misc.py line 117 1407060] Train: [14/100][408/1462] Data 0.001 (0.001) Batch 2.796 (2.879) Remain 101:22:52 loss: 0.2758 Lr: 0.00200
[2025-09-06 15:44:29,333 INFO misc.py line 117 1407060] Train: [14/100][409/1462] Data 0.001 (0.001) Batch 3.022 (2.879) Remain 101:23:34 loss: 0.2953 Lr: 0.00200
[2025-09-06 15:44:32,411 INFO misc.py line 117 1407060] Train: [14/100][410/1462] Data 0.002 (0.001) Batch 3.078 (2.879) Remain 101:24:33 loss: 0.5206 Lr: 0.00200
[2025-09-06 15:44:35,277 INFO misc.py line 117 1407060] Train: [14/100][411/1462] Data 0.001 (0.001) Batch 2.866 (2.879) Remain 101:24:26 loss: 0.3715 Lr: 0.00200
[2025-09-06 15:44:38,074 INFO misc.py line 117 1407060] Train: [14/100][412/1462] Data 0.002 (0.001) Batch 2.797 (2.879) Remain 101:23:58 loss: 0.4867 Lr: 0.00200
[2025-09-06 15:44:41,130 INFO misc.py line 117 1407060] Train: [14/100][413/1462] Data 0.001 (0.001) Batch 3.056 (2.880) Remain 101:24:49 loss: 0.3510 Lr: 0.00200
[2025-09-06 15:44:44,102 INFO misc.py line 117 1407060] Train: [14/100][414/1462] Data 0.002 (0.001) Batch 2.972 (2.880) Remain 101:25:15 loss: 0.6213 Lr: 0.00200
[2025-09-06 15:44:47,002 INFO misc.py line 117 1407060] Train: [14/100][415/1462] Data 0.002 (0.001) Batch 2.901 (2.880) Remain 101:25:18 loss: 0.5011 Lr: 0.00200
[2025-09-06 15:44:49,960 INFO misc.py line 117 1407060] Train: [14/100][416/1462] Data 0.001 (0.001) Batch 2.958 (2.880) Remain 101:25:39 loss: 0.4413 Lr: 0.00200
[2025-09-06 15:44:53,046 INFO misc.py line 117 1407060] Train: [14/100][417/1462] Data 0.002 (0.001) Batch 3.086 (2.881) Remain 101:26:40 loss: 0.4845 Lr: 0.00200
[2025-09-06 15:44:55,969 INFO misc.py line 117 1407060] Train: [14/100][418/1462] Data 0.001 (0.001) Batch 2.923 (2.881) Remain 101:26:50 loss: 0.4239 Lr: 0.00200
[2025-09-06 15:44:58,958 INFO misc.py line 117 1407060] Train: [14/100][419/1462] Data 0.001 (0.001) Batch 2.989 (2.881) Remain 101:27:20 loss: 0.2650 Lr: 0.00200
[2025-09-06 15:45:01,935 INFO misc.py line 117 1407060] Train: [14/100][420/1462] Data 0.002 (0.001) Batch 2.977 (2.881) Remain 101:27:46 loss: 0.3853 Lr: 0.00200
[2025-09-06 15:45:04,561 INFO misc.py line 117 1407060] Train: [14/100][421/1462] Data 0.001 (0.001) Batch 2.626 (2.881) Remain 101:26:26 loss: 0.5789 Lr: 0.00200
[2025-09-06 15:45:07,566 INFO misc.py line 117 1407060] Train: [14/100][422/1462] Data 0.001 (0.001) Batch 3.005 (2.881) Remain 101:27:00 loss: 0.4095 Lr: 0.00200
[2025-09-06 15:45:10,457 INFO misc.py line 117 1407060] Train: [14/100][423/1462] Data 0.001 (0.001) Batch 2.891 (2.881) Remain 101:27:01 loss: 0.3960 Lr: 0.00200
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [0,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [1,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [2,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [3,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [4,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [5,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [6,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [7,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [8,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [9,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [10,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [11,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [12,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [13,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [14,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [15,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [16,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [17,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [18,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [19,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [20,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [21,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [22,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [23,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [24,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [25,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [26,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [27,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [28,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [29,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [30,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [114,0,0], thread: [31,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
[2025-09-06 15:45:12,302 ERROR events.py line 611 1407061] Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 175, in train
    self.run_step()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 202, in run_step
    self.scaler.scale(loss).backward()
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_tensor.py", line 581, in backward
    torch.autograd.backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/__init__.py", line 347, in backward
    _engine_run_backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/graph.py", line 825, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


[rank1]:[W906 15:45:12.801618530 CUDAGuardImpl.h:119] Warning: CUDA warning: device-side assert triggered (function destroyEvent)
terminate called after throwing an instance of 'c10::Error'
  what():  CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

Exception raised from c10_cuda_check_implementation at /opt/conda/conda-bld/pytorch_1728929546833/work/c10/cuda/CUDAException.cpp:43 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::string) + 0x96 (0x7fe50d070446 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: c10::detail::torchCheckFail(char const*, char const*, unsigned int, std::string const&) + 0x64 (0x7fe50d01a6e4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #2: c10::cuda::c10_cuda_check_implementation(int, char const*, char const*, int, bool) + 0x118 (0x7fe50d15da18 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #3: <unknown function> + 0x1f92e (0x7fe50d12492e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #4: <unknown function> + 0x20a57 (0x7fe50d125a57 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #5: <unknown function> + 0x20c5f (0x7fe50d125c5f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #6: <unknown function> + 0x5faf70 (0x7fe55e2c6f70 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #7: <unknown function> + 0x6f69f (0x7fe50d05169f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #8: c10::TensorImpl::~TensorImpl() + 0x21b (0x7fe50d04a37b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #9: c10::TensorImpl::~TensorImpl() + 0x9 (0x7fe50d04a529 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #10: c10d::Reducer::~Reducer() + 0x5c4 (0x7fe555f7c3b4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #11: std::_Sp_counted_ptr<c10d::Reducer*, (__gnu_cxx::_Lock_policy)2>::_M_dispose() + 0x12 (0x7fe55ea6ebf2 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #12: std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release() + 0x48 (0x7fe55e18a078 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #13: <unknown function> + 0xdadc21 (0x7fe55ea79c21 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #14: <unknown function> + 0x4c90f3 (0x7fe55e1950f3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #15: <unknown function> + 0x4c9c71 (0x7fe55e195c71 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #16: <unknown function> + 0x128b86 (0x55678344bb86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #17: <unknown function> + 0x14b2a0 (0x55678346e2a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #18: <unknown function> + 0x128b86 (0x55678344bb86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #19: <unknown function> + 0x14b2a0 (0x55678346e2a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #20: <unknown function> + 0x134be7 (0x556783457be7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #21: <unknown function> + 0x134ccb (0x556783457ccb in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #22: <unknown function> + 0x144c3b (0x556783467c3b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #23: <unknown function> + 0x149f1c (0x55678346cf1c in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #24: <unknown function> + 0x121412 (0x556783444412 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #25: <unknown function> + 0x203bb1 (0x556783526bb1 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #26: _PyEval_EvalFrameDefault + 0x46cf (0x55678345377f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #27: _PyFunction_Vectorcall + 0x6c (0x55678345f0ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #28: _PyEval_EvalFrameDefault + 0x700 (0x55678344f7b0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #29: _PyFunction_Vectorcall + 0x6c (0x55678345f0ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #30: _PyEval_EvalFrameDefault + 0x30c (0x55678344f3bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #31: _PyFunction_Vectorcall + 0x6c (0x55678345f0ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #32: _PyEval_EvalFrameDefault + 0x1340 (0x5567834503f0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #33: <unknown function> + 0x1cc3bc (0x5567834ef3bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #34: PyEval_EvalCode + 0x87 (0x5567834ef307 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #35: <unknown function> + 0x1fc96a (0x55678351f96a in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #36: <unknown function> + 0x1f7df3 (0x55678351adf3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #37: PyRun_StringFlags + 0x7d (0x5567835134ad in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #38: PyRun_SimpleStringFlags + 0x3c (0x5567835133ac in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #39: Py_RunMain + 0x3ba (0x5567835125ba in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #40: Py_BytesMain + 0x37 (0x5567834e2ef7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #41: <unknown function> + 0x29d90 (0x7fe5673edd90 in /lib/x86_64-linux-gnu/libc.so.6)
frame #42: __libc_start_main + 0x80 (0x7fe5673ede40 in /lib/x86_64-linux-gnu/libc.so.6)
frame #43: <unknown function> + 0x1bfe0e (0x5567834e2e0e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)

W0906 15:45:13.017000 1406899 site-packages/torch/multiprocessing/spawn.py:160] Terminating process 1407060 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 38, in <module>
    main()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 27, in main
    launch(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 74, in launch
    mp.spawn(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 328, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 284, in start_processes
    while not context.join():
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 184, in join
    raise ProcessExitedException(
torch.multiprocessing.spawn.ProcessExitedException: process 1 terminated with signal SIGABRT
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 35 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
