Starting training at 2025年 09月 06日 星期六 17:12:07 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-06 17:12:29,223 INFO train.py line 136 1419922] => Loading config ...
[2025-09-06 17:12:29,223 INFO train.py line 138 1419922] Save path: exp/powscan/splitSections
[2025-09-06 17:12:29,362 INFO train.py line 139 1419922] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 12
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 6
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-06 17:12:29,362 INFO train.py line 140 1419922] => Building model ...
[2025-09-06 17:12:29,418 INFO train.py line 241 1419922] Num params: 3908496
[2025-09-06 17:12:29,491 INFO train.py line 142 1419922] => Building writer ...
[2025-09-06 17:12:29,493 INFO train.py line 251 1419922] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-06 17:12:29,493 INFO train.py line 144 1419922] => Building train dataset & dataloader ...
[2025-09-06 17:12:48,523 INFO defaults.py line 70 1419922] Totally 585 x 30 samples in powscan train set.
[2025-09-06 17:12:48,524 INFO train.py line 146 1419922] => Building val dataset & dataloader ...
[2025-09-06 17:12:49,931 INFO defaults.py line 70 1419922] Totally 80 x 1 samples in powscan val set.
[2025-09-06 17:12:49,931 INFO train.py line 148 1419922] => Building optimize, scheduler, scaler(amp) ...
[2025-09-06 17:12:50,094 INFO train.py line 152 1419922] => Building hooks ...
[2025-09-06 17:12:50,095 INFO misc.py line 237 1419922] => Loading checkpoint & weight ...
[2025-09-06 17:12:50,095 INFO misc.py line 239 1419922] Loading weight at: exp/powscan/splitSections/model/model_last.pth
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
[2025-09-06 17:12:50,330 INFO misc.py line 245 1419922] Loading layer weights with keyword: , replace keyword with: 
[2025-09-06 17:12:50,359 INFO misc.py line 262 1419922] Missing keys: []
[2025-09-06 17:12:50,359 INFO misc.py line 264 1419922] Resuming train at eval epoch: 13
[2025-09-06 17:12:50,365 INFO train.py line 159 1419922] >>>>>>>>>>>>>>>> Start Training >>>>>>>>>>>>>>>>
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
[2025-09-06 17:13:07,315 INFO misc.py line 117 1419922] Train: [14/100][1/1462] Data 13.343 (13.343) Batch 16.908 (16.908) Remain 597:23:04 loss: 0.2806 Lr: 0.00200
[2025-09-06 17:13:10,591 INFO misc.py line 117 1419922] Train: [14/100][2/1462] Data 0.002 (0.002) Batch 3.276 (3.276) Remain 115:43:51 loss: 0.5064 Lr: 0.00200
[2025-09-06 17:13:13,878 INFO misc.py line 117 1419922] Train: [14/100][3/1462] Data 0.001 (0.001) Batch 3.287 (3.287) Remain 116:07:50 loss: 0.3336 Lr: 0.00200
[2025-09-06 17:13:16,715 INFO misc.py line 117 1419922] Train: [14/100][4/1462] Data 0.001 (0.001) Batch 2.837 (2.837) Remain 100:14:39 loss: 0.3845 Lr: 0.00200
[2025-09-06 17:13:19,385 INFO misc.py line 117 1419922] Train: [14/100][5/1462] Data 0.002 (0.002) Batch 2.670 (2.754) Remain 97:17:07 loss: 0.3848 Lr: 0.00200
[2025-09-06 17:13:22,078 INFO misc.py line 117 1419922] Train: [14/100][6/1462] Data 0.001 (0.001) Batch 2.693 (2.733) Remain 96:34:16 loss: 0.4506 Lr: 0.00200
[2025-09-06 17:13:24,930 INFO misc.py line 117 1419922] Train: [14/100][7/1462] Data 0.001 (0.001) Batch 2.852 (2.763) Remain 97:36:59 loss: 0.2723 Lr: 0.00200
[2025-09-06 17:13:27,489 INFO misc.py line 117 1419922] Train: [14/100][8/1462] Data 0.001 (0.001) Batch 2.560 (2.722) Remain 96:10:42 loss: 0.3736 Lr: 0.00200
[2025-09-06 17:13:30,287 INFO misc.py line 117 1419922] Train: [14/100][9/1462] Data 0.001 (0.001) Batch 2.797 (2.735) Remain 96:37:09 loss: 0.7186 Lr: 0.00200
[2025-09-06 17:13:33,468 INFO misc.py line 117 1419922] Train: [14/100][10/1462] Data 0.001 (0.001) Batch 3.181 (2.799) Remain 98:52:18 loss: 0.2464 Lr: 0.00200
[2025-09-06 17:13:36,393 INFO misc.py line 117 1419922] Train: [14/100][11/1462] Data 0.001 (0.001) Batch 2.925 (2.814) Remain 99:25:46 loss: 0.2759 Lr: 0.00200
[2025-09-06 17:13:39,272 INFO misc.py line 117 1419922] Train: [14/100][12/1462] Data 0.002 (0.001) Batch 2.879 (2.822) Remain 99:40:56 loss: 0.3470 Lr: 0.00200
[2025-09-06 17:13:41,970 INFO misc.py line 117 1419922] Train: [14/100][13/1462] Data 0.001 (0.001) Batch 2.698 (2.809) Remain 99:14:37 loss: 0.3210 Lr: 0.00200
[2025-09-06 17:13:44,771 INFO misc.py line 117 1419922] Train: [14/100][14/1462] Data 0.001 (0.001) Batch 2.801 (2.808) Remain 99:13:01 loss: 0.3520 Lr: 0.00200
[2025-09-06 17:13:47,703 INFO misc.py line 117 1419922] Train: [14/100][15/1462] Data 0.002 (0.001) Batch 2.932 (2.819) Remain 99:34:51 loss: 0.3907 Lr: 0.00200
[2025-09-06 17:13:50,987 INFO misc.py line 117 1419922] Train: [14/100][16/1462] Data 0.002 (0.001) Batch 3.284 (2.855) Remain 100:50:39 loss: 0.7960 Lr: 0.00200
[2025-09-06 17:13:54,038 INFO misc.py line 117 1419922] Train: [14/100][17/1462] Data 0.002 (0.001) Batch 3.050 (2.869) Remain 101:20:14 loss: 0.3433 Lr: 0.00200
[2025-09-06 17:13:56,888 INFO misc.py line 117 1419922] Train: [14/100][18/1462] Data 0.001 (0.001) Batch 2.851 (2.867) Remain 101:17:39 loss: 0.3826 Lr: 0.00200
[2025-09-06 17:13:59,943 INFO misc.py line 117 1419922] Train: [14/100][19/1462] Data 0.002 (0.001) Batch 3.055 (2.879) Remain 101:42:29 loss: 0.4264 Lr: 0.00200
[2025-09-06 17:14:02,640 INFO misc.py line 117 1419922] Train: [14/100][20/1462] Data 0.002 (0.001) Batch 2.697 (2.868) Remain 101:19:42 loss: 0.2551 Lr: 0.00200
[2025-09-06 17:14:05,795 INFO misc.py line 117 1419922] Train: [14/100][21/1462] Data 0.002 (0.001) Batch 3.155 (2.884) Remain 101:53:21 loss: 0.3905 Lr: 0.00200
[2025-09-06 17:14:08,493 INFO misc.py line 117 1419922] Train: [14/100][22/1462] Data 0.002 (0.001) Batch 2.698 (2.874) Remain 101:32:32 loss: 0.3014 Lr: 0.00200
[2025-09-06 17:14:11,141 INFO misc.py line 117 1419922] Train: [14/100][23/1462] Data 0.002 (0.001) Batch 2.648 (2.863) Remain 101:08:30 loss: 0.7456 Lr: 0.00200
[2025-09-06 17:14:13,895 INFO misc.py line 117 1419922] Train: [14/100][24/1462] Data 0.002 (0.001) Batch 2.755 (2.858) Remain 100:57:29 loss: 0.6076 Lr: 0.00200
[2025-09-06 17:14:16,854 INFO misc.py line 117 1419922] Train: [14/100][25/1462] Data 0.002 (0.002) Batch 2.959 (2.863) Remain 101:07:09 loss: 0.5505 Lr: 0.00200
[2025-09-06 17:14:19,729 INFO misc.py line 117 1419922] Train: [14/100][26/1462] Data 0.002 (0.002) Batch 2.875 (2.863) Remain 101:08:15 loss: 0.4245 Lr: 0.00200
[2025-09-06 17:14:22,916 INFO misc.py line 117 1419922] Train: [14/100][27/1462] Data 0.002 (0.002) Batch 3.187 (2.877) Remain 101:36:46 loss: 0.3007 Lr: 0.00200
[2025-09-06 17:14:25,726 INFO misc.py line 117 1419922] Train: [14/100][28/1462] Data 0.001 (0.002) Batch 2.810 (2.874) Remain 101:31:05 loss: 0.6282 Lr: 0.00200
[2025-09-06 17:14:28,714 INFO misc.py line 117 1419922] Train: [14/100][29/1462] Data 0.001 (0.002) Batch 2.988 (2.878) Remain 101:40:19 loss: 0.5939 Lr: 0.00200
[2025-09-06 17:14:31,337 INFO misc.py line 117 1419922] Train: [14/100][30/1462] Data 0.001 (0.002) Batch 2.623 (2.869) Remain 101:20:16 loss: 0.7621 Lr: 0.00200
[2025-09-06 17:14:34,250 INFO misc.py line 117 1419922] Train: [14/100][31/1462] Data 0.002 (0.002) Batch 2.913 (2.870) Remain 101:23:31 loss: 0.4903 Lr: 0.00200
[2025-09-06 17:14:36,982 INFO misc.py line 117 1419922] Train: [14/100][32/1462] Data 0.002 (0.002) Batch 2.732 (2.866) Remain 101:13:23 loss: 0.5755 Lr: 0.00200
[2025-09-06 17:14:40,028 INFO misc.py line 117 1419922] Train: [14/100][33/1462] Data 0.002 (0.002) Batch 3.046 (2.872) Remain 101:26:04 loss: 0.3110 Lr: 0.00200
[2025-09-06 17:14:42,792 INFO misc.py line 117 1419922] Train: [14/100][34/1462] Data 0.002 (0.002) Batch 2.764 (2.868) Remain 101:18:38 loss: 0.3312 Lr: 0.00200
[2025-09-06 17:14:46,046 INFO misc.py line 117 1419922] Train: [14/100][35/1462] Data 0.002 (0.002) Batch 3.255 (2.880) Remain 101:44:11 loss: 0.3178 Lr: 0.00200
[2025-09-06 17:14:48,981 INFO misc.py line 117 1419922] Train: [14/100][36/1462] Data 0.002 (0.002) Batch 2.935 (2.882) Remain 101:47:37 loss: 0.3678 Lr: 0.00200
[2025-09-06 17:14:52,039 INFO misc.py line 117 1419922] Train: [14/100][37/1462] Data 0.002 (0.002) Batch 3.058 (2.887) Remain 101:58:32 loss: 0.6043 Lr: 0.00200
[2025-09-06 17:14:55,198 INFO misc.py line 117 1419922] Train: [14/100][38/1462] Data 0.002 (0.002) Batch 3.160 (2.895) Remain 102:14:59 loss: 0.3548 Lr: 0.00200
[2025-09-06 17:14:58,291 INFO misc.py line 117 1419922] Train: [14/100][39/1462] Data 0.002 (0.002) Batch 3.093 (2.900) Remain 102:26:37 loss: 0.3662 Lr: 0.00200
[2025-09-06 17:15:01,175 INFO misc.py line 117 1419922] Train: [14/100][40/1462] Data 0.002 (0.002) Batch 2.883 (2.900) Remain 102:25:35 loss: 0.5984 Lr: 0.00200
[2025-09-06 17:15:04,032 INFO misc.py line 117 1419922] Train: [14/100][41/1462] Data 0.002 (0.002) Batch 2.858 (2.899) Remain 102:23:10 loss: 0.2131 Lr: 0.00200
[2025-09-06 17:15:06,901 INFO misc.py line 117 1419922] Train: [14/100][42/1462] Data 0.002 (0.002) Batch 2.869 (2.898) Remain 102:21:30 loss: 0.2328 Lr: 0.00200
[2025-09-06 17:15:09,739 INFO misc.py line 117 1419922] Train: [14/100][43/1462] Data 0.001 (0.002) Batch 2.838 (2.897) Remain 102:18:17 loss: 0.2901 Lr: 0.00200
[2025-09-06 17:15:12,499 INFO misc.py line 117 1419922] Train: [14/100][44/1462] Data 0.002 (0.002) Batch 2.760 (2.893) Remain 102:11:10 loss: 0.5460 Lr: 0.00200
[2025-09-06 17:15:15,466 INFO misc.py line 117 1419922] Train: [14/100][45/1462] Data 0.002 (0.002) Batch 2.967 (2.895) Remain 102:14:49 loss: 0.3931 Lr: 0.00200
[2025-09-06 17:15:18,488 INFO misc.py line 117 1419922] Train: [14/100][46/1462] Data 0.002 (0.002) Batch 3.023 (2.898) Remain 102:21:04 loss: 0.4379 Lr: 0.00200
[2025-09-06 17:15:21,254 INFO misc.py line 117 1419922] Train: [14/100][47/1462] Data 0.001 (0.002) Batch 2.765 (2.895) Remain 102:14:38 loss: 0.3612 Lr: 0.00200
[2025-09-06 17:15:24,072 INFO misc.py line 117 1419922] Train: [14/100][48/1462] Data 0.002 (0.002) Batch 2.819 (2.893) Remain 102:11:00 loss: 0.4049 Lr: 0.00200
[2025-09-06 17:15:27,001 INFO misc.py line 117 1419922] Train: [14/100][49/1462] Data 0.002 (0.002) Batch 2.928 (2.894) Remain 102:12:33 loss: 0.4247 Lr: 0.00200
[2025-09-06 17:15:29,947 INFO misc.py line 117 1419922] Train: [14/100][50/1462] Data 0.002 (0.002) Batch 2.946 (2.895) Remain 102:14:51 loss: 0.7000 Lr: 0.00200
[2025-09-06 17:15:32,542 INFO misc.py line 117 1419922] Train: [14/100][51/1462] Data 0.002 (0.002) Batch 2.595 (2.889) Remain 102:01:34 loss: 0.5204 Lr: 0.00200
[2025-09-06 17:15:35,258 INFO misc.py line 117 1419922] Train: [14/100][52/1462] Data 0.001 (0.002) Batch 2.717 (2.885) Remain 101:54:05 loss: 0.3152 Lr: 0.00200
[2025-09-06 17:15:38,164 INFO misc.py line 117 1419922] Train: [14/100][53/1462] Data 0.001 (0.002) Batch 2.905 (2.886) Remain 101:54:52 loss: 0.5952 Lr: 0.00200
[2025-09-06 17:15:41,304 INFO misc.py line 117 1419922] Train: [14/100][54/1462] Data 0.001 (0.002) Batch 3.140 (2.891) Remain 102:05:24 loss: 0.2296 Lr: 0.00200
[2025-09-06 17:15:43,847 INFO misc.py line 117 1419922] Train: [14/100][55/1462] Data 0.002 (0.002) Batch 2.543 (2.884) Remain 101:51:11 loss: 0.8914 Lr: 0.00200
[2025-09-06 17:15:46,451 INFO misc.py line 117 1419922] Train: [14/100][56/1462] Data 0.001 (0.002) Batch 2.604 (2.879) Remain 101:39:56 loss: 0.9913 Lr: 0.00200
[2025-09-06 17:15:49,276 INFO misc.py line 117 1419922] Train: [14/100][57/1462] Data 0.001 (0.002) Batch 2.825 (2.878) Remain 101:37:47 loss: 0.3684 Lr: 0.00200
[2025-09-06 17:15:52,160 INFO misc.py line 117 1419922] Train: [14/100][58/1462] Data 0.001 (0.002) Batch 2.884 (2.878) Remain 101:38:00 loss: 0.3908 Lr: 0.00200
[2025-09-06 17:15:55,266 INFO misc.py line 117 1419922] Train: [14/100][59/1462] Data 0.001 (0.002) Batch 3.106 (2.882) Remain 101:46:35 loss: 0.2867 Lr: 0.00200
[2025-09-06 17:15:57,791 INFO misc.py line 117 1419922] Train: [14/100][60/1462] Data 0.002 (0.002) Batch 2.525 (2.876) Remain 101:33:16 loss: 0.2634 Lr: 0.00200
[2025-09-06 17:16:00,352 INFO misc.py line 117 1419922] Train: [14/100][61/1462] Data 0.002 (0.002) Batch 2.561 (2.870) Remain 101:21:43 loss: 0.6207 Lr: 0.00200
[2025-09-06 17:16:03,504 INFO misc.py line 117 1419922] Train: [14/100][62/1462] Data 0.001 (0.002) Batch 3.152 (2.875) Remain 101:31:47 loss: 0.4767 Lr: 0.00200
[2025-09-06 17:16:06,163 INFO misc.py line 117 1419922] Train: [14/100][63/1462] Data 0.001 (0.002) Batch 2.658 (2.871) Remain 101:24:05 loss: 0.3400 Lr: 0.00200
[2025-09-06 17:16:08,828 INFO misc.py line 117 1419922] Train: [14/100][64/1462] Data 0.001 (0.002) Batch 2.666 (2.868) Remain 101:16:53 loss: 0.9026 Lr: 0.00200
[2025-09-06 17:16:11,406 INFO misc.py line 117 1419922] Train: [14/100][65/1462] Data 0.001 (0.002) Batch 2.578 (2.863) Remain 101:06:55 loss: 0.3493 Lr: 0.00200
[2025-09-06 17:16:14,058 INFO misc.py line 117 1419922] Train: [14/100][66/1462] Data 0.001 (0.002) Batch 2.652 (2.860) Remain 100:59:46 loss: 0.3458 Lr: 0.00200
[2025-09-06 17:16:16,743 INFO misc.py line 117 1419922] Train: [14/100][67/1462] Data 0.001 (0.002) Batch 2.685 (2.857) Remain 100:53:55 loss: 0.5726 Lr: 0.00200
[2025-09-06 17:16:19,673 INFO misc.py line 117 1419922] Train: [14/100][68/1462] Data 0.002 (0.002) Batch 2.930 (2.858) Remain 100:56:15 loss: 0.4623 Lr: 0.00200
[2025-09-06 17:16:22,504 INFO misc.py line 117 1419922] Train: [14/100][69/1462] Data 0.001 (0.002) Batch 2.830 (2.858) Remain 100:55:18 loss: 0.6361 Lr: 0.00200
[2025-09-06 17:16:25,108 INFO misc.py line 117 1419922] Train: [14/100][70/1462] Data 0.001 (0.002) Batch 2.605 (2.854) Remain 100:47:15 loss: 0.5465 Lr: 0.00200
[2025-09-06 17:16:27,932 INFO misc.py line 117 1419922] Train: [14/100][71/1462] Data 0.001 (0.002) Batch 2.823 (2.854) Remain 100:46:15 loss: 0.3564 Lr: 0.00200
[2025-09-06 17:16:30,707 INFO misc.py line 117 1419922] Train: [14/100][72/1462] Data 0.001 (0.002) Batch 2.775 (2.853) Remain 100:43:48 loss: 0.4636 Lr: 0.00200
[2025-09-06 17:16:33,397 INFO misc.py line 117 1419922] Train: [14/100][73/1462] Data 0.001 (0.002) Batch 2.690 (2.850) Remain 100:38:49 loss: 0.2378 Lr: 0.00200
[2025-09-06 17:16:36,215 INFO misc.py line 117 1419922] Train: [14/100][74/1462] Data 0.002 (0.002) Batch 2.818 (2.850) Remain 100:37:49 loss: 0.3298 Lr: 0.00200
[2025-09-06 17:16:38,919 INFO misc.py line 117 1419922] Train: [14/100][75/1462] Data 0.002 (0.002) Batch 2.704 (2.848) Remain 100:33:29 loss: 0.5682 Lr: 0.00200
[2025-09-06 17:16:41,640 INFO misc.py line 117 1419922] Train: [14/100][76/1462] Data 0.002 (0.002) Batch 2.721 (2.846) Remain 100:29:45 loss: 0.7360 Lr: 0.00200
[2025-09-06 17:16:44,493 INFO misc.py line 117 1419922] Train: [14/100][77/1462] Data 0.001 (0.002) Batch 2.853 (2.846) Remain 100:29:54 loss: 0.3560 Lr: 0.00200
[2025-09-06 17:16:47,308 INFO misc.py line 117 1419922] Train: [14/100][78/1462] Data 0.001 (0.002) Batch 2.814 (2.846) Remain 100:28:57 loss: 0.2654 Lr: 0.00200
[2025-09-06 17:16:50,725 INFO misc.py line 117 1419922] Train: [14/100][79/1462] Data 0.001 (0.002) Batch 3.418 (2.853) Remain 100:44:51 loss: 0.4198 Lr: 0.00200
[2025-09-06 17:16:53,405 INFO misc.py line 117 1419922] Train: [14/100][80/1462] Data 0.001 (0.002) Batch 2.679 (2.851) Remain 100:40:01 loss: 0.7159 Lr: 0.00200
[2025-09-06 17:16:56,432 INFO misc.py line 117 1419922] Train: [14/100][81/1462] Data 0.002 (0.002) Batch 3.027 (2.853) Remain 100:44:45 loss: 0.6940 Lr: 0.00200
[2025-09-06 17:16:59,166 INFO misc.py line 117 1419922] Train: [14/100][82/1462] Data 0.002 (0.002) Batch 2.734 (2.852) Remain 100:41:30 loss: 0.3905 Lr: 0.00200
[2025-09-06 17:17:02,063 INFO misc.py line 117 1419922] Train: [14/100][83/1462] Data 0.001 (0.002) Batch 2.898 (2.852) Remain 100:42:41 loss: 0.4334 Lr: 0.00200
[2025-09-06 17:17:05,048 INFO misc.py line 117 1419922] Train: [14/100][84/1462] Data 0.001 (0.002) Batch 2.984 (2.854) Remain 100:46:05 loss: 0.5182 Lr: 0.00200
[2025-09-06 17:17:07,965 INFO misc.py line 117 1419922] Train: [14/100][85/1462] Data 0.001 (0.002) Batch 2.917 (2.855) Remain 100:47:40 loss: 0.3576 Lr: 0.00200
[2025-09-06 17:17:10,733 INFO misc.py line 117 1419922] Train: [14/100][86/1462] Data 0.001 (0.002) Batch 2.768 (2.854) Remain 100:45:25 loss: 0.2319 Lr: 0.00200
[2025-09-06 17:17:13,424 INFO misc.py line 117 1419922] Train: [14/100][87/1462] Data 0.001 (0.002) Batch 2.691 (2.852) Remain 100:41:15 loss: 0.5961 Lr: 0.00200
[2025-09-06 17:17:16,086 INFO misc.py line 117 1419922] Train: [14/100][88/1462] Data 0.002 (0.002) Batch 2.662 (2.850) Remain 100:36:29 loss: 0.4026 Lr: 0.00200
[2025-09-06 17:17:19,042 INFO misc.py line 117 1419922] Train: [14/100][89/1462] Data 0.001 (0.002) Batch 2.956 (2.851) Remain 100:39:04 loss: 0.3600 Lr: 0.00200
[2025-09-06 17:17:22,084 INFO misc.py line 117 1419922] Train: [14/100][90/1462] Data 0.001 (0.002) Batch 3.042 (2.853) Remain 100:43:40 loss: 0.4630 Lr: 0.00200
[2025-09-06 17:17:25,080 INFO misc.py line 117 1419922] Train: [14/100][91/1462] Data 0.001 (0.002) Batch 2.996 (2.855) Remain 100:47:04 loss: 0.5893 Lr: 0.00200
[2025-09-06 17:17:28,294 INFO misc.py line 117 1419922] Train: [14/100][92/1462] Data 0.001 (0.002) Batch 3.214 (2.859) Remain 100:55:35 loss: 0.5100 Lr: 0.00200
[2025-09-06 17:17:31,358 INFO misc.py line 117 1419922] Train: [14/100][93/1462] Data 0.002 (0.002) Batch 3.063 (2.861) Remain 101:00:21 loss: 0.3637 Lr: 0.00200
[2025-09-06 17:17:34,067 INFO misc.py line 117 1419922] Train: [14/100][94/1462] Data 0.002 (0.002) Batch 2.709 (2.859) Remain 100:56:46 loss: 0.8141 Lr: 0.00200
[2025-09-06 17:17:36,974 INFO misc.py line 117 1419922] Train: [14/100][95/1462] Data 0.001 (0.002) Batch 2.908 (2.860) Remain 100:57:50 loss: 0.6914 Lr: 0.00200
[2025-09-06 17:17:39,926 INFO misc.py line 117 1419922] Train: [14/100][96/1462] Data 0.001 (0.002) Batch 2.951 (2.861) Remain 100:59:52 loss: 0.5767 Lr: 0.00200
[2025-09-06 17:17:42,646 INFO misc.py line 117 1419922] Train: [14/100][97/1462] Data 0.001 (0.002) Batch 2.720 (2.859) Remain 100:56:39 loss: 0.3902 Lr: 0.00200
[2025-09-06 17:17:45,457 INFO misc.py line 117 1419922] Train: [14/100][98/1462] Data 0.002 (0.002) Batch 2.811 (2.859) Remain 100:55:32 loss: 0.3743 Lr: 0.00200
[2025-09-06 17:17:48,288 INFO misc.py line 117 1419922] Train: [14/100][99/1462] Data 0.001 (0.002) Batch 2.832 (2.858) Remain 100:54:53 loss: 0.2842 Lr: 0.00200
[2025-09-06 17:17:50,891 INFO misc.py line 117 1419922] Train: [14/100][100/1462] Data 0.001 (0.002) Batch 2.603 (2.856) Remain 100:49:15 loss: 0.3643 Lr: 0.00200
[2025-09-06 17:17:53,748 INFO misc.py line 117 1419922] Train: [14/100][101/1462] Data 0.002 (0.002) Batch 2.857 (2.856) Remain 100:49:14 loss: 0.6801 Lr: 0.00200
[2025-09-06 17:17:56,898 INFO misc.py line 117 1419922] Train: [14/100][102/1462] Data 0.002 (0.002) Batch 3.150 (2.859) Remain 100:55:28 loss: 0.5542 Lr: 0.00200
[2025-09-06 17:18:00,141 INFO misc.py line 117 1419922] Train: [14/100][103/1462] Data 0.001 (0.002) Batch 3.243 (2.863) Remain 101:03:34 loss: 0.5186 Lr: 0.00200
[2025-09-06 17:18:02,892 INFO misc.py line 117 1419922] Train: [14/100][104/1462] Data 0.002 (0.002) Batch 2.752 (2.862) Remain 101:01:11 loss: 0.6351 Lr: 0.00200
[2025-09-06 17:18:05,453 INFO misc.py line 117 1419922] Train: [14/100][105/1462] Data 0.002 (0.002) Batch 2.560 (2.859) Remain 100:54:53 loss: 0.5037 Lr: 0.00200
[2025-09-06 17:18:08,002 INFO misc.py line 117 1419922] Train: [14/100][106/1462] Data 0.002 (0.002) Batch 2.550 (2.856) Remain 100:48:28 loss: 0.5311 Lr: 0.00200
[2025-09-06 17:18:11,221 INFO misc.py line 117 1419922] Train: [14/100][107/1462] Data 0.002 (0.002) Batch 3.219 (2.859) Remain 100:55:49 loss: 0.3803 Lr: 0.00200
[2025-09-06 17:18:14,218 INFO misc.py line 117 1419922] Train: [14/100][108/1462] Data 0.002 (0.002) Batch 2.997 (2.860) Remain 100:58:34 loss: 0.5379 Lr: 0.00200
[2025-09-06 17:18:17,316 INFO misc.py line 117 1419922] Train: [14/100][109/1462] Data 0.002 (0.002) Batch 3.098 (2.863) Remain 101:03:16 loss: 0.2218 Lr: 0.00200
[2025-09-06 17:18:20,197 INFO misc.py line 117 1419922] Train: [14/100][110/1462] Data 0.001 (0.002) Batch 2.881 (2.863) Remain 101:03:35 loss: 0.5661 Lr: 0.00200
[2025-09-06 17:18:22,954 INFO misc.py line 117 1419922] Train: [14/100][111/1462] Data 0.001 (0.002) Batch 2.757 (2.862) Remain 101:01:27 loss: 0.3777 Lr: 0.00200
[2025-09-06 17:18:26,319 INFO misc.py line 117 1419922] Train: [14/100][112/1462] Data 0.001 (0.002) Batch 3.365 (2.866) Remain 101:11:12 loss: 0.3172 Lr: 0.00200
[2025-09-06 17:18:29,379 INFO misc.py line 117 1419922] Train: [14/100][113/1462] Data 0.002 (0.002) Batch 3.059 (2.868) Remain 101:14:52 loss: 2.0973 Lr: 0.00200
[2025-09-06 17:18:32,266 INFO misc.py line 117 1419922] Train: [14/100][114/1462] Data 0.002 (0.002) Batch 2.887 (2.868) Remain 101:15:10 loss: 0.3754 Lr: 0.00200
[2025-09-06 17:18:35,255 INFO misc.py line 117 1419922] Train: [14/100][115/1462] Data 0.002 (0.002) Batch 2.989 (2.869) Remain 101:17:25 loss: 0.6177 Lr: 0.00200
[2025-09-06 17:18:38,180 INFO misc.py line 117 1419922] Train: [14/100][116/1462] Data 0.001 (0.002) Batch 2.925 (2.870) Remain 101:18:24 loss: 0.6897 Lr: 0.00200
[2025-09-06 17:18:41,511 INFO misc.py line 117 1419922] Train: [14/100][117/1462] Data 0.002 (0.002) Batch 3.331 (2.874) Remain 101:26:56 loss: 0.4941 Lr: 0.00200
[2025-09-06 17:18:44,568 INFO misc.py line 117 1419922] Train: [14/100][118/1462] Data 0.002 (0.002) Batch 3.057 (2.876) Remain 101:30:15 loss: 0.4423 Lr: 0.00200
[2025-09-06 17:18:47,398 INFO misc.py line 117 1419922] Train: [14/100][119/1462] Data 0.002 (0.002) Batch 2.831 (2.875) Remain 101:29:22 loss: 0.5832 Lr: 0.00200
[2025-09-06 17:18:50,216 INFO misc.py line 117 1419922] Train: [14/100][120/1462] Data 0.002 (0.002) Batch 2.818 (2.875) Remain 101:28:18 loss: 0.6316 Lr: 0.00200
[2025-09-06 17:18:53,072 INFO misc.py line 117 1419922] Train: [14/100][121/1462] Data 0.002 (0.002) Batch 2.855 (2.875) Remain 101:27:54 loss: 0.4269 Lr: 0.00200
[2025-09-06 17:18:55,558 INFO misc.py line 117 1419922] Train: [14/100][122/1462] Data 0.002 (0.002) Batch 2.486 (2.871) Remain 101:20:57 loss: 0.4872 Lr: 0.00200
[2025-09-06 17:18:58,322 INFO misc.py line 117 1419922] Train: [14/100][123/1462] Data 0.001 (0.002) Batch 2.764 (2.870) Remain 101:19:00 loss: 0.6186 Lr: 0.00200
[2025-09-06 17:19:01,302 INFO misc.py line 117 1419922] Train: [14/100][124/1462] Data 0.001 (0.002) Batch 2.980 (2.871) Remain 101:20:52 loss: 0.3356 Lr: 0.00200
[2025-09-06 17:19:04,276 INFO misc.py line 117 1419922] Train: [14/100][125/1462] Data 0.002 (0.002) Batch 2.975 (2.872) Remain 101:22:37 loss: 0.4490 Lr: 0.00200
[2025-09-06 17:19:06,879 INFO misc.py line 117 1419922] Train: [14/100][126/1462] Data 0.001 (0.002) Batch 2.602 (2.870) Remain 101:17:55 loss: 0.5693 Lr: 0.00200
[2025-09-06 17:19:09,776 INFO misc.py line 117 1419922] Train: [14/100][127/1462] Data 0.001 (0.002) Batch 2.897 (2.870) Remain 101:18:20 loss: 0.4114 Lr: 0.00200
[2025-09-06 17:19:12,789 INFO misc.py line 117 1419922] Train: [14/100][128/1462] Data 0.002 (0.002) Batch 3.014 (2.871) Remain 101:20:43 loss: 0.4051 Lr: 0.00200
[2025-09-06 17:19:15,309 INFO misc.py line 117 1419922] Train: [14/100][129/1462] Data 0.001 (0.002) Batch 2.520 (2.868) Remain 101:14:45 loss: 0.2826 Lr: 0.00200
[2025-09-06 17:19:18,063 INFO misc.py line 117 1419922] Train: [14/100][130/1462] Data 0.001 (0.002) Batch 2.754 (2.868) Remain 101:12:48 loss: 0.2982 Lr: 0.00200
[2025-09-06 17:19:21,297 INFO misc.py line 117 1419922] Train: [14/100][131/1462] Data 0.002 (0.002) Batch 3.234 (2.870) Remain 101:18:48 loss: 0.5143 Lr: 0.00200
[2025-09-06 17:19:24,288 INFO misc.py line 117 1419922] Train: [14/100][132/1462] Data 0.002 (0.002) Batch 2.991 (2.871) Remain 101:20:45 loss: 0.3736 Lr: 0.00200
[2025-09-06 17:19:27,330 INFO misc.py line 117 1419922] Train: [14/100][133/1462] Data 0.002 (0.002) Batch 3.042 (2.873) Remain 101:23:28 loss: 0.7286 Lr: 0.00200
[2025-09-06 17:19:30,414 INFO misc.py line 117 1419922] Train: [14/100][134/1462] Data 0.002 (0.002) Batch 3.084 (2.874) Remain 101:26:51 loss: 0.6353 Lr: 0.00200
[2025-09-06 17:19:33,695 INFO misc.py line 117 1419922] Train: [14/100][135/1462] Data 0.002 (0.002) Batch 3.281 (2.877) Remain 101:33:19 loss: 0.3426 Lr: 0.00200
[2025-09-06 17:19:36,573 INFO misc.py line 117 1419922] Train: [14/100][136/1462] Data 0.002 (0.002) Batch 2.878 (2.877) Remain 101:33:17 loss: 0.5958 Lr: 0.00200
[2025-09-06 17:19:39,146 INFO misc.py line 117 1419922] Train: [14/100][137/1462] Data 0.002 (0.002) Batch 2.573 (2.875) Remain 101:28:26 loss: 0.5411 Lr: 0.00200
[2025-09-06 17:19:42,137 INFO misc.py line 117 1419922] Train: [14/100][138/1462] Data 0.001 (0.002) Batch 2.990 (2.876) Remain 101:30:11 loss: 0.3847 Lr: 0.00200
[2025-09-06 17:19:44,991 INFO misc.py line 117 1419922] Train: [14/100][139/1462] Data 0.002 (0.002) Batch 2.854 (2.876) Remain 101:29:48 loss: 0.4764 Lr: 0.00200
[2025-09-06 17:19:47,710 INFO misc.py line 117 1419922] Train: [14/100][140/1462] Data 0.002 (0.002) Batch 2.720 (2.875) Remain 101:27:20 loss: 0.3573 Lr: 0.00200
[2025-09-06 17:19:50,629 INFO misc.py line 117 1419922] Train: [14/100][141/1462] Data 0.002 (0.002) Batch 2.919 (2.875) Remain 101:27:58 loss: 0.3307 Lr: 0.00200
[2025-09-06 17:19:53,425 INFO misc.py line 117 1419922] Train: [14/100][142/1462] Data 0.002 (0.002) Batch 2.796 (2.874) Remain 101:26:43 loss: 0.5821 Lr: 0.00200
[2025-09-06 17:19:56,079 INFO misc.py line 117 1419922] Train: [14/100][143/1462] Data 0.002 (0.002) Batch 2.654 (2.873) Remain 101:23:20 loss: 0.4744 Lr: 0.00200
[2025-09-06 17:19:59,043 INFO misc.py line 117 1419922] Train: [14/100][144/1462] Data 0.002 (0.002) Batch 2.964 (2.874) Remain 101:24:39 loss: 0.2863 Lr: 0.00200
[2025-09-06 17:20:01,756 INFO misc.py line 117 1419922] Train: [14/100][145/1462] Data 0.001 (0.002) Batch 2.714 (2.872) Remain 101:22:13 loss: 0.4108 Lr: 0.00200
[2025-09-06 17:20:04,948 INFO misc.py line 117 1419922] Train: [14/100][146/1462] Data 0.001 (0.002) Batch 3.191 (2.875) Remain 101:26:53 loss: 0.3281 Lr: 0.00200
[2025-09-06 17:20:07,691 INFO misc.py line 117 1419922] Train: [14/100][147/1462] Data 0.002 (0.002) Batch 2.743 (2.874) Remain 101:24:55 loss: 0.7737 Lr: 0.00200
[2025-09-06 17:20:10,402 INFO misc.py line 117 1419922] Train: [14/100][148/1462] Data 0.002 (0.002) Batch 2.711 (2.873) Remain 101:22:29 loss: 0.5481 Lr: 0.00200
[2025-09-06 17:20:13,224 INFO misc.py line 117 1419922] Train: [14/100][149/1462] Data 0.002 (0.002) Batch 2.823 (2.872) Remain 101:21:43 loss: 0.3331 Lr: 0.00200
[2025-09-06 17:20:16,014 INFO misc.py line 117 1419922] Train: [14/100][150/1462] Data 0.002 (0.002) Batch 2.790 (2.872) Remain 101:20:28 loss: 0.5461 Lr: 0.00200
[2025-09-06 17:20:18,671 INFO misc.py line 117 1419922] Train: [14/100][151/1462] Data 0.001 (0.002) Batch 2.657 (2.870) Remain 101:17:21 loss: 0.5094 Lr: 0.00200
[2025-09-06 17:20:21,475 INFO misc.py line 117 1419922] Train: [14/100][152/1462] Data 0.002 (0.002) Batch 2.804 (2.870) Remain 101:16:22 loss: 0.5773 Lr: 0.00200
[2025-09-06 17:20:24,147 INFO misc.py line 117 1419922] Train: [14/100][153/1462] Data 0.002 (0.002) Batch 2.672 (2.868) Remain 101:13:32 loss: 0.3768 Lr: 0.00200
[2025-09-06 17:20:26,960 INFO misc.py line 117 1419922] Train: [14/100][154/1462] Data 0.002 (0.002) Batch 2.813 (2.868) Remain 101:12:42 loss: 0.3186 Lr: 0.00200
[2025-09-06 17:20:29,771 INFO misc.py line 117 1419922] Train: [14/100][155/1462] Data 0.002 (0.002) Batch 2.811 (2.868) Remain 101:11:52 loss: 0.5805 Lr: 0.00200
[2025-09-06 17:20:32,540 INFO misc.py line 117 1419922] Train: [14/100][156/1462] Data 0.002 (0.002) Batch 2.769 (2.867) Remain 101:10:27 loss: 0.2785 Lr: 0.00200
[2025-09-06 17:20:35,265 INFO misc.py line 117 1419922] Train: [14/100][157/1462] Data 0.002 (0.002) Batch 2.725 (2.866) Remain 101:08:27 loss: 0.4345 Lr: 0.00200
[2025-09-06 17:20:38,226 INFO misc.py line 117 1419922] Train: [14/100][158/1462] Data 0.002 (0.002) Batch 2.960 (2.867) Remain 101:09:41 loss: 0.3498 Lr: 0.00200
[2025-09-06 17:20:41,316 INFO misc.py line 117 1419922] Train: [14/100][159/1462] Data 0.002 (0.002) Batch 3.090 (2.868) Remain 101:12:40 loss: 0.5483 Lr: 0.00200
[2025-09-06 17:20:44,323 INFO misc.py line 117 1419922] Train: [14/100][160/1462] Data 0.002 (0.002) Batch 3.007 (2.869) Remain 101:14:30 loss: 0.2249 Lr: 0.00200
[2025-09-06 17:20:47,070 INFO misc.py line 117 1419922] Train: [14/100][161/1462] Data 0.002 (0.002) Batch 2.747 (2.868) Remain 101:12:49 loss: 0.3600 Lr: 0.00200
[2025-09-06 17:20:50,021 INFO misc.py line 117 1419922] Train: [14/100][162/1462] Data 0.001 (0.002) Batch 2.951 (2.869) Remain 101:13:52 loss: 0.4818 Lr: 0.00200
[2025-09-06 17:20:53,134 INFO misc.py line 117 1419922] Train: [14/100][163/1462] Data 0.001 (0.002) Batch 3.113 (2.870) Remain 101:17:03 loss: 0.3490 Lr: 0.00200
[2025-09-06 17:20:55,968 INFO misc.py line 117 1419922] Train: [14/100][164/1462] Data 0.002 (0.002) Batch 2.834 (2.870) Remain 101:16:31 loss: 0.5099 Lr: 0.00200
[2025-09-06 17:20:58,872 INFO misc.py line 117 1419922] Train: [14/100][165/1462] Data 0.001 (0.002) Batch 2.904 (2.870) Remain 101:16:55 loss: 0.4179 Lr: 0.00200
[2025-09-06 17:21:02,870 INFO misc.py line 117 1419922] Train: [14/100][166/1462] Data 0.001 (0.002) Batch 3.997 (2.877) Remain 101:31:31 loss: 0.2575 Lr: 0.00200
[2025-09-06 17:21:05,962 INFO misc.py line 117 1419922] Train: [14/100][167/1462] Data 0.002 (0.002) Batch 3.092 (2.879) Remain 101:34:14 loss: 0.3316 Lr: 0.00200
[2025-09-06 17:21:08,781 INFO misc.py line 117 1419922] Train: [14/100][168/1462] Data 0.002 (0.002) Batch 2.819 (2.878) Remain 101:33:25 loss: 0.3370 Lr: 0.00200
[2025-09-06 17:21:11,640 INFO misc.py line 117 1419922] Train: [14/100][169/1462] Data 0.001 (0.002) Batch 2.859 (2.878) Remain 101:33:08 loss: 0.3391 Lr: 0.00200
[2025-09-06 17:21:14,382 INFO misc.py line 117 1419922] Train: [14/100][170/1462] Data 0.002 (0.002) Batch 2.742 (2.877) Remain 101:31:22 loss: 0.3753 Lr: 0.00200
[2025-09-06 17:21:17,092 INFO misc.py line 117 1419922] Train: [14/100][171/1462] Data 0.002 (0.002) Batch 2.709 (2.876) Remain 101:29:12 loss: 0.3178 Lr: 0.00200
[2025-09-06 17:21:20,019 INFO misc.py line 117 1419922] Train: [14/100][172/1462] Data 0.003 (0.002) Batch 2.928 (2.877) Remain 101:29:48 loss: 0.4575 Lr: 0.00200
[2025-09-06 17:21:23,366 INFO misc.py line 117 1419922] Train: [14/100][173/1462] Data 0.002 (0.002) Batch 3.347 (2.879) Remain 101:35:36 loss: 0.5614 Lr: 0.00200
[2025-09-06 17:21:26,260 INFO misc.py line 117 1419922] Train: [14/100][174/1462] Data 0.002 (0.002) Batch 2.894 (2.879) Remain 101:35:44 loss: 0.2834 Lr: 0.00200
[2025-09-06 17:21:29,167 INFO misc.py line 117 1419922] Train: [14/100][175/1462] Data 0.002 (0.002) Batch 2.907 (2.880) Remain 101:36:02 loss: 0.3290 Lr: 0.00200
[2025-09-06 17:21:32,196 INFO misc.py line 117 1419922] Train: [14/100][176/1462] Data 0.001 (0.002) Batch 3.030 (2.880) Remain 101:37:49 loss: 0.3168 Lr: 0.00200
[2025-09-06 17:21:35,266 INFO misc.py line 117 1419922] Train: [14/100][177/1462] Data 0.002 (0.002) Batch 3.070 (2.882) Remain 101:40:04 loss: 0.3997 Lr: 0.00200
[2025-09-06 17:21:37,923 INFO misc.py line 117 1419922] Train: [14/100][178/1462] Data 0.002 (0.002) Batch 2.656 (2.880) Remain 101:37:18 loss: 0.4136 Lr: 0.00200
[2025-09-06 17:21:40,815 INFO misc.py line 117 1419922] Train: [14/100][179/1462] Data 0.002 (0.002) Batch 2.892 (2.880) Remain 101:37:24 loss: 0.6067 Lr: 0.00200
[2025-09-06 17:21:43,736 INFO misc.py line 117 1419922] Train: [14/100][180/1462] Data 0.002 (0.002) Batch 2.921 (2.881) Remain 101:37:50 loss: 0.2830 Lr: 0.00200
[2025-09-06 17:21:46,556 INFO misc.py line 117 1419922] Train: [14/100][181/1462] Data 0.001 (0.002) Batch 2.821 (2.880) Remain 101:37:04 loss: 0.3289 Lr: 0.00200
[2025-09-06 17:21:49,947 INFO misc.py line 117 1419922] Train: [14/100][182/1462] Data 0.001 (0.002) Batch 3.390 (2.883) Remain 101:43:04 loss: 0.3693 Lr: 0.00200
[2025-09-06 17:21:52,513 INFO misc.py line 117 1419922] Train: [14/100][183/1462] Data 0.001 (0.002) Batch 2.566 (2.881) Remain 101:39:17 loss: 0.3464 Lr: 0.00200
[2025-09-06 17:21:55,108 INFO misc.py line 117 1419922] Train: [14/100][184/1462] Data 0.002 (0.002) Batch 2.595 (2.880) Remain 101:35:53 loss: 0.3416 Lr: 0.00200
[2025-09-06 17:21:57,989 INFO misc.py line 117 1419922] Train: [14/100][185/1462] Data 0.001 (0.002) Batch 2.881 (2.880) Remain 101:35:51 loss: 0.2657 Lr: 0.00200
[2025-09-06 17:22:00,997 INFO misc.py line 117 1419922] Train: [14/100][186/1462] Data 0.002 (0.002) Batch 3.008 (2.880) Remain 101:37:17 loss: 0.3802 Lr: 0.00200
[2025-09-06 17:22:03,504 INFO misc.py line 117 1419922] Train: [14/100][187/1462] Data 0.001 (0.002) Batch 2.507 (2.878) Remain 101:32:57 loss: 0.4513 Lr: 0.00200
[2025-09-06 17:22:06,393 INFO misc.py line 117 1419922] Train: [14/100][188/1462] Data 0.002 (0.002) Batch 2.888 (2.878) Remain 101:33:01 loss: 0.3757 Lr: 0.00200
[2025-09-06 17:22:09,345 INFO misc.py line 117 1419922] Train: [14/100][189/1462] Data 0.001 (0.002) Batch 2.952 (2.879) Remain 101:33:48 loss: 0.3288 Lr: 0.00200
[2025-09-06 17:22:11,975 INFO misc.py line 117 1419922] Train: [14/100][190/1462] Data 0.001 (0.002) Batch 2.630 (2.878) Remain 101:30:56 loss: 0.5855 Lr: 0.00200
[2025-09-06 17:22:14,654 INFO misc.py line 117 1419922] Train: [14/100][191/1462] Data 0.001 (0.002) Batch 2.678 (2.876) Remain 101:28:39 loss: 0.3049 Lr: 0.00200
[2025-09-06 17:22:17,417 INFO misc.py line 117 1419922] Train: [14/100][192/1462] Data 0.001 (0.002) Batch 2.764 (2.876) Remain 101:27:21 loss: 0.3394 Lr: 0.00200
[2025-09-06 17:22:20,245 INFO misc.py line 117 1419922] Train: [14/100][193/1462] Data 0.002 (0.002) Batch 2.828 (2.876) Remain 101:26:46 loss: 0.5607 Lr: 0.00200
[2025-09-06 17:22:23,176 INFO misc.py line 117 1419922] Train: [14/100][194/1462] Data 0.001 (0.002) Batch 2.930 (2.876) Remain 101:27:19 loss: 0.5684 Lr: 0.00200
[2025-09-06 17:22:25,945 INFO misc.py line 117 1419922] Train: [14/100][195/1462] Data 0.002 (0.002) Batch 2.769 (2.875) Remain 101:26:06 loss: 0.4664 Lr: 0.00200
[2025-09-06 17:22:28,672 INFO misc.py line 117 1419922] Train: [14/100][196/1462] Data 0.001 (0.002) Batch 2.727 (2.875) Remain 101:24:25 loss: 0.7406 Lr: 0.00200
[2025-09-06 17:22:31,622 INFO misc.py line 117 1419922] Train: [14/100][197/1462] Data 0.001 (0.002) Batch 2.950 (2.875) Remain 101:25:12 loss: 0.3391 Lr: 0.00200
[2025-09-06 17:22:34,596 INFO misc.py line 117 1419922] Train: [14/100][198/1462] Data 0.001 (0.002) Batch 2.973 (2.875) Remain 101:26:13 loss: 0.4154 Lr: 0.00200
[2025-09-06 17:22:37,548 INFO misc.py line 117 1419922] Train: [14/100][199/1462] Data 0.002 (0.002) Batch 2.952 (2.876) Remain 101:27:00 loss: 0.4569 Lr: 0.00200
[2025-09-06 17:22:40,604 INFO misc.py line 117 1419922] Train: [14/100][200/1462] Data 0.002 (0.002) Batch 3.056 (2.877) Remain 101:28:53 loss: 0.5008 Lr: 0.00200
[2025-09-06 17:22:43,245 INFO misc.py line 117 1419922] Train: [14/100][201/1462] Data 0.002 (0.002) Batch 2.641 (2.876) Remain 101:26:19 loss: 0.7791 Lr: 0.00200
[2025-09-06 17:22:46,270 INFO misc.py line 117 1419922] Train: [14/100][202/1462] Data 0.001 (0.002) Batch 3.025 (2.876) Remain 101:27:52 loss: 0.4561 Lr: 0.00200
[2025-09-06 17:22:49,073 INFO misc.py line 117 1419922] Train: [14/100][203/1462] Data 0.001 (0.002) Batch 2.804 (2.876) Remain 101:27:03 loss: 0.4370 Lr: 0.00200
[2025-09-06 17:22:51,944 INFO misc.py line 117 1419922] Train: [14/100][204/1462] Data 0.001 (0.002) Batch 2.870 (2.876) Remain 101:26:56 loss: 0.3859 Lr: 0.00200
[2025-09-06 17:22:55,269 INFO misc.py line 117 1419922] Train: [14/100][205/1462] Data 0.002 (0.002) Batch 3.325 (2.878) Remain 101:31:35 loss: 0.5455 Lr: 0.00200
[2025-09-06 17:22:58,232 INFO misc.py line 117 1419922] Train: [14/100][206/1462] Data 0.001 (0.002) Batch 2.963 (2.879) Remain 101:32:26 loss: 0.5005 Lr: 0.00200
[2025-09-06 17:23:01,083 INFO misc.py line 117 1419922] Train: [14/100][207/1462] Data 0.002 (0.002) Batch 2.851 (2.878) Remain 101:32:06 loss: 0.5782 Lr: 0.00200
[2025-09-06 17:23:04,036 INFO misc.py line 117 1419922] Train: [14/100][208/1462] Data 0.001 (0.002) Batch 2.953 (2.879) Remain 101:32:49 loss: 0.2449 Lr: 0.00200
[2025-09-06 17:23:06,617 INFO misc.py line 117 1419922] Train: [14/100][209/1462] Data 0.001 (0.002) Batch 2.581 (2.877) Remain 101:29:43 loss: 0.4984 Lr: 0.00200
[2025-09-06 17:23:09,353 INFO misc.py line 117 1419922] Train: [14/100][210/1462] Data 0.001 (0.002) Batch 2.736 (2.877) Remain 101:28:13 loss: 0.4670 Lr: 0.00200
[2025-09-06 17:23:12,038 INFO misc.py line 117 1419922] Train: [14/100][211/1462] Data 0.002 (0.002) Batch 2.685 (2.876) Remain 101:26:13 loss: 0.4085 Lr: 0.00200
[2025-09-06 17:23:14,836 INFO misc.py line 117 1419922] Train: [14/100][212/1462] Data 0.001 (0.002) Batch 2.798 (2.875) Remain 101:25:23 loss: 0.3917 Lr: 0.00200
[2025-09-06 17:23:17,543 INFO misc.py line 117 1419922] Train: [14/100][213/1462] Data 0.001 (0.002) Batch 2.706 (2.875) Remain 101:23:38 loss: 0.5308 Lr: 0.00200
[2025-09-06 17:23:20,435 INFO misc.py line 117 1419922] Train: [14/100][214/1462] Data 0.002 (0.002) Batch 2.892 (2.875) Remain 101:23:46 loss: 0.2985 Lr: 0.00200
[2025-09-06 17:23:22,924 INFO misc.py line 117 1419922] Train: [14/100][215/1462] Data 0.001 (0.002) Batch 2.489 (2.873) Remain 101:19:52 loss: 0.3189 Lr: 0.00200
[2025-09-06 17:23:25,766 INFO misc.py line 117 1419922] Train: [14/100][216/1462] Data 0.001 (0.002) Batch 2.842 (2.873) Remain 101:19:31 loss: 0.3689 Lr: 0.00200
[2025-09-06 17:23:28,364 INFO misc.py line 117 1419922] Train: [14/100][217/1462] Data 0.002 (0.002) Batch 2.598 (2.871) Remain 101:16:45 loss: 0.2465 Lr: 0.00200
[2025-09-06 17:23:31,073 INFO misc.py line 117 1419922] Train: [14/100][218/1462] Data 0.002 (0.002) Batch 2.709 (2.871) Remain 101:15:06 loss: 0.4758 Lr: 0.00200
[2025-09-06 17:23:33,890 INFO misc.py line 117 1419922] Train: [14/100][219/1462] Data 0.001 (0.002) Batch 2.817 (2.870) Remain 101:14:32 loss: 0.4699 Lr: 0.00200
[2025-09-06 17:23:36,835 INFO misc.py line 117 1419922] Train: [14/100][220/1462] Data 0.002 (0.002) Batch 2.945 (2.871) Remain 101:15:13 loss: 0.2911 Lr: 0.00200
[2025-09-06 17:23:39,642 INFO misc.py line 117 1419922] Train: [14/100][221/1462] Data 0.001 (0.002) Batch 2.807 (2.870) Remain 101:14:33 loss: 0.3845 Lr: 0.00200
[2025-09-06 17:23:42,880 INFO misc.py line 117 1419922] Train: [14/100][222/1462] Data 0.001 (0.002) Batch 3.238 (2.872) Remain 101:18:03 loss: 0.3625 Lr: 0.00200
[2025-09-06 17:23:45,713 INFO misc.py line 117 1419922] Train: [14/100][223/1462] Data 0.001 (0.002) Batch 2.833 (2.872) Remain 101:17:37 loss: 0.6422 Lr: 0.00200
[2025-09-06 17:23:48,240 INFO misc.py line 117 1419922] Train: [14/100][224/1462] Data 0.002 (0.002) Batch 2.527 (2.870) Remain 101:14:16 loss: 0.3472 Lr: 0.00200
[2025-09-06 17:23:51,142 INFO misc.py line 117 1419922] Train: [14/100][225/1462] Data 0.001 (0.002) Batch 2.902 (2.871) Remain 101:14:31 loss: 0.3298 Lr: 0.00200
[2025-09-06 17:23:53,996 INFO misc.py line 117 1419922] Train: [14/100][226/1462] Data 0.002 (0.002) Batch 2.855 (2.870) Remain 101:14:19 loss: 0.3484 Lr: 0.00200
[2025-09-06 17:23:56,822 INFO misc.py line 117 1419922] Train: [14/100][227/1462] Data 0.002 (0.002) Batch 2.826 (2.870) Remain 101:13:51 loss: 0.2441 Lr: 0.00200
[2025-09-06 17:23:59,805 INFO misc.py line 117 1419922] Train: [14/100][228/1462] Data 0.003 (0.002) Batch 2.984 (2.871) Remain 101:14:52 loss: 0.5733 Lr: 0.00200
[2025-09-06 17:24:02,916 INFO misc.py line 117 1419922] Train: [14/100][229/1462] Data 0.001 (0.002) Batch 3.111 (2.872) Remain 101:17:04 loss: 0.2912 Lr: 0.00200
[2025-09-06 17:24:05,754 INFO misc.py line 117 1419922] Train: [14/100][230/1462] Data 0.002 (0.002) Batch 2.837 (2.872) Remain 101:16:42 loss: 0.3092 Lr: 0.00200
[2025-09-06 17:24:08,775 INFO misc.py line 117 1419922] Train: [14/100][231/1462] Data 0.001 (0.002) Batch 3.021 (2.872) Remain 101:18:02 loss: 0.3723 Lr: 0.00200
[2025-09-06 17:24:11,677 INFO misc.py line 117 1419922] Train: [14/100][232/1462] Data 0.002 (0.002) Batch 2.902 (2.872) Remain 101:18:16 loss: 0.3266 Lr: 0.00200
[2025-09-06 17:24:14,521 INFO misc.py line 117 1419922] Train: [14/100][233/1462] Data 0.001 (0.002) Batch 2.844 (2.872) Remain 101:17:57 loss: 0.2931 Lr: 0.00200
[2025-09-06 17:24:17,744 INFO misc.py line 117 1419922] Train: [14/100][234/1462] Data 0.002 (0.002) Batch 3.224 (2.874) Remain 101:21:07 loss: 0.6616 Lr: 0.00200
[2025-09-06 17:24:21,034 INFO misc.py line 117 1419922] Train: [14/100][235/1462] Data 0.002 (0.002) Batch 3.290 (2.876) Remain 101:24:52 loss: 0.2971 Lr: 0.00200
[2025-09-06 17:24:24,110 INFO misc.py line 117 1419922] Train: [14/100][236/1462] Data 0.002 (0.002) Batch 3.076 (2.877) Remain 101:26:38 loss: 0.3795 Lr: 0.00200
[2025-09-06 17:24:27,353 INFO misc.py line 117 1419922] Train: [14/100][237/1462] Data 0.002 (0.002) Batch 3.243 (2.878) Remain 101:29:54 loss: 0.3500 Lr: 0.00200
[2025-09-06 17:24:30,551 INFO misc.py line 117 1419922] Train: [14/100][238/1462] Data 0.001 (0.002) Batch 3.198 (2.879) Remain 101:32:44 loss: 0.6212 Lr: 0.00200
[2025-09-06 17:24:33,246 INFO misc.py line 117 1419922] Train: [14/100][239/1462] Data 0.002 (0.002) Batch 2.695 (2.879) Remain 101:31:02 loss: 0.5221 Lr: 0.00200
[2025-09-06 17:24:36,142 INFO misc.py line 117 1419922] Train: [14/100][240/1462] Data 0.002 (0.002) Batch 2.896 (2.879) Remain 101:31:08 loss: 0.3092 Lr: 0.00200
[2025-09-06 17:24:38,946 INFO misc.py line 117 1419922] Train: [14/100][241/1462] Data 0.001 (0.002) Batch 2.804 (2.878) Remain 101:30:26 loss: 0.3486 Lr: 0.00200
[2025-09-06 17:24:41,924 INFO misc.py line 117 1419922] Train: [14/100][242/1462] Data 0.002 (0.002) Batch 2.978 (2.879) Remain 101:31:16 loss: 0.3817 Lr: 0.00200
[2025-09-06 17:24:44,521 INFO misc.py line 117 1419922] Train: [14/100][243/1462] Data 0.002 (0.002) Batch 2.597 (2.878) Remain 101:28:44 loss: 1.7567 Lr: 0.00200
[2025-09-06 17:24:48,174 INFO misc.py line 117 1419922] Train: [14/100][244/1462] Data 0.002 (0.002) Batch 3.653 (2.881) Remain 101:35:29 loss: 0.5856 Lr: 0.00200
[2025-09-06 17:24:51,111 INFO misc.py line 117 1419922] Train: [14/100][245/1462] Data 0.002 (0.002) Batch 2.937 (2.881) Remain 101:35:56 loss: 0.3033 Lr: 0.00200
[2025-09-06 17:24:53,830 INFO misc.py line 117 1419922] Train: [14/100][246/1462] Data 0.002 (0.002) Batch 2.719 (2.880) Remain 101:34:28 loss: 0.2989 Lr: 0.00200
[2025-09-06 17:24:56,484 INFO misc.py line 117 1419922] Train: [14/100][247/1462] Data 0.002 (0.002) Batch 2.653 (2.880) Remain 101:32:27 loss: 0.4504 Lr: 0.00200
[2025-09-06 17:24:59,511 INFO misc.py line 117 1419922] Train: [14/100][248/1462] Data 0.001 (0.002) Batch 3.027 (2.880) Remain 101:33:41 loss: 0.3376 Lr: 0.00200
[2025-09-06 17:25:03,024 INFO misc.py line 117 1419922] Train: [14/100][249/1462] Data 0.001 (0.002) Batch 3.514 (2.883) Remain 101:39:05 loss: 0.5995 Lr: 0.00200
[2025-09-06 17:25:05,951 INFO misc.py line 117 1419922] Train: [14/100][250/1462] Data 0.001 (0.002) Batch 2.926 (2.883) Remain 101:39:24 loss: 0.5621 Lr: 0.00200
[2025-09-06 17:25:08,748 INFO misc.py line 117 1419922] Train: [14/100][251/1462] Data 0.001 (0.002) Batch 2.797 (2.883) Remain 101:38:38 loss: 0.6212 Lr: 0.00200
[2025-09-06 17:25:11,747 INFO misc.py line 117 1419922] Train: [14/100][252/1462] Data 0.001 (0.002) Batch 2.999 (2.883) Remain 101:39:34 loss: 0.5101 Lr: 0.00200
[2025-09-06 17:25:14,396 INFO misc.py line 117 1419922] Train: [14/100][253/1462] Data 0.002 (0.002) Batch 2.649 (2.882) Remain 101:37:32 loss: 0.4212 Lr: 0.00200
[2025-09-06 17:25:17,462 INFO misc.py line 117 1419922] Train: [14/100][254/1462] Data 0.001 (0.002) Batch 3.066 (2.883) Remain 101:39:03 loss: 0.2968 Lr: 0.00200
[2025-09-06 17:25:20,241 INFO misc.py line 117 1419922] Train: [14/100][255/1462] Data 0.001 (0.002) Batch 2.779 (2.882) Remain 101:38:07 loss: 0.3855 Lr: 0.00200
[2025-09-06 17:25:23,029 INFO misc.py line 117 1419922] Train: [14/100][256/1462] Data 0.001 (0.002) Batch 2.788 (2.882) Remain 101:37:17 loss: 0.5524 Lr: 0.00200
[2025-09-06 17:25:25,986 INFO misc.py line 117 1419922] Train: [14/100][257/1462] Data 0.001 (0.002) Batch 2.957 (2.882) Remain 101:37:52 loss: 0.3147 Lr: 0.00200
[2025-09-06 17:25:28,733 INFO misc.py line 117 1419922] Train: [14/100][258/1462] Data 0.002 (0.002) Batch 2.747 (2.882) Remain 101:36:42 loss: 0.3254 Lr: 0.00200
[2025-09-06 17:25:31,912 INFO misc.py line 117 1419922] Train: [14/100][259/1462] Data 0.002 (0.002) Batch 3.179 (2.883) Remain 101:39:06 loss: 0.5076 Lr: 0.00200
[2025-09-06 17:25:34,735 INFO misc.py line 117 1419922] Train: [14/100][260/1462] Data 0.001 (0.002) Batch 2.823 (2.883) Remain 101:38:34 loss: 0.4137 Lr: 0.00200
[2025-09-06 17:25:37,429 INFO misc.py line 117 1419922] Train: [14/100][261/1462] Data 0.001 (0.002) Batch 2.693 (2.882) Remain 101:36:58 loss: 0.5658 Lr: 0.00200
[2025-09-06 17:25:39,887 INFO misc.py line 117 1419922] Train: [14/100][262/1462] Data 0.001 (0.002) Batch 2.459 (2.880) Remain 101:33:27 loss: 0.5208 Lr: 0.00200
[2025-09-06 17:25:42,828 INFO misc.py line 117 1419922] Train: [14/100][263/1462] Data 0.002 (0.002) Batch 2.940 (2.881) Remain 101:33:54 loss: 0.5699 Lr: 0.00200
[2025-09-06 17:25:45,634 INFO misc.py line 117 1419922] Train: [14/100][264/1462] Data 0.002 (0.002) Batch 2.807 (2.880) Remain 101:33:15 loss: 0.7399 Lr: 0.00200
[2025-09-06 17:25:48,395 INFO misc.py line 117 1419922] Train: [14/100][265/1462] Data 0.002 (0.002) Batch 2.761 (2.880) Remain 101:32:14 loss: 0.5825 Lr: 0.00200
[2025-09-06 17:25:51,497 INFO misc.py line 117 1419922] Train: [14/100][266/1462] Data 0.002 (0.002) Batch 3.102 (2.881) Remain 101:33:58 loss: 0.3105 Lr: 0.00200
[2025-09-06 17:25:54,313 INFO misc.py line 117 1419922] Train: [14/100][267/1462] Data 0.002 (0.002) Batch 2.816 (2.880) Remain 101:33:24 loss: 0.5965 Lr: 0.00200
[2025-09-06 17:25:57,228 INFO misc.py line 117 1419922] Train: [14/100][268/1462] Data 0.002 (0.002) Batch 2.915 (2.881) Remain 101:33:38 loss: 0.2795 Lr: 0.00200
[2025-09-06 17:26:00,169 INFO misc.py line 117 1419922] Train: [14/100][269/1462] Data 0.003 (0.002) Batch 2.941 (2.881) Remain 101:34:04 loss: 0.4199 Lr: 0.00200
[2025-09-06 17:26:03,501 INFO misc.py line 117 1419922] Train: [14/100][270/1462] Data 0.002 (0.002) Batch 3.332 (2.882) Remain 101:37:36 loss: 0.4228 Lr: 0.00200
[2025-09-06 17:26:06,578 INFO misc.py line 117 1419922] Train: [14/100][271/1462] Data 0.002 (0.002) Batch 3.077 (2.883) Remain 101:39:05 loss: 0.4119 Lr: 0.00200
[2025-09-06 17:26:09,322 INFO misc.py line 117 1419922] Train: [14/100][272/1462] Data 0.001 (0.002) Batch 2.743 (2.883) Remain 101:37:56 loss: 0.4509 Lr: 0.00200
[2025-09-06 17:26:12,363 INFO misc.py line 117 1419922] Train: [14/100][273/1462] Data 0.001 (0.002) Batch 3.042 (2.883) Remain 101:39:08 loss: 0.4602 Lr: 0.00200
[2025-09-06 17:26:15,186 INFO misc.py line 117 1419922] Train: [14/100][274/1462] Data 0.002 (0.002) Batch 2.823 (2.883) Remain 101:38:37 loss: 0.2293 Lr: 0.00200
[2025-09-06 17:26:17,794 INFO misc.py line 117 1419922] Train: [14/100][275/1462] Data 0.001 (0.002) Batch 2.608 (2.882) Remain 101:36:25 loss: 0.4009 Lr: 0.00200
[2025-09-06 17:26:20,410 INFO misc.py line 117 1419922] Train: [14/100][276/1462] Data 0.001 (0.002) Batch 2.616 (2.881) Remain 101:34:19 loss: 0.2790 Lr: 0.00200
[2025-09-06 17:26:23,308 INFO misc.py line 117 1419922] Train: [14/100][277/1462] Data 0.002 (0.002) Batch 2.899 (2.881) Remain 101:34:24 loss: 0.3642 Lr: 0.00200
[2025-09-06 17:26:26,143 INFO misc.py line 117 1419922] Train: [14/100][278/1462] Data 0.002 (0.002) Batch 2.835 (2.881) Remain 101:34:00 loss: 0.7062 Lr: 0.00200
[2025-09-06 17:26:28,865 INFO misc.py line 117 1419922] Train: [14/100][279/1462] Data 0.002 (0.002) Batch 2.722 (2.880) Remain 101:32:44 loss: 0.5292 Lr: 0.00200
[2025-09-06 17:26:32,107 INFO misc.py line 117 1419922] Train: [14/100][280/1462] Data 0.002 (0.002) Batch 3.242 (2.882) Remain 101:35:26 loss: 0.5638 Lr: 0.00200
[2025-09-06 17:26:34,837 INFO misc.py line 117 1419922] Train: [14/100][281/1462] Data 0.002 (0.002) Batch 2.730 (2.881) Remain 101:34:14 loss: 0.4602 Lr: 0.00200
[2025-09-06 17:26:37,643 INFO misc.py line 117 1419922] Train: [14/100][282/1462] Data 0.002 (0.002) Batch 2.806 (2.881) Remain 101:33:37 loss: 0.4932 Lr: 0.00200
[2025-09-06 17:26:40,478 INFO misc.py line 117 1419922] Train: [14/100][283/1462] Data 0.002 (0.002) Batch 2.836 (2.881) Remain 101:33:14 loss: 0.2648 Lr: 0.00200
[2025-09-06 17:26:43,366 INFO misc.py line 117 1419922] Train: [14/100][284/1462] Data 0.001 (0.002) Batch 2.887 (2.881) Remain 101:33:14 loss: 0.4247 Lr: 0.00200
[2025-09-06 17:26:46,126 INFO misc.py line 117 1419922] Train: [14/100][285/1462] Data 0.001 (0.002) Batch 2.760 (2.880) Remain 101:32:17 loss: 0.5040 Lr: 0.00200
[2025-09-06 17:26:48,945 INFO misc.py line 117 1419922] Train: [14/100][286/1462] Data 0.002 (0.002) Batch 2.819 (2.880) Remain 101:31:47 loss: 0.3933 Lr: 0.00200
[2025-09-06 17:26:51,861 INFO misc.py line 117 1419922] Train: [14/100][287/1462] Data 0.001 (0.002) Batch 2.916 (2.880) Remain 101:32:00 loss: 0.4018 Lr: 0.00200
[2025-09-06 17:26:54,651 INFO misc.py line 117 1419922] Train: [14/100][288/1462] Data 0.001 (0.002) Batch 2.790 (2.880) Remain 101:31:17 loss: 0.3525 Lr: 0.00200
[2025-09-06 17:26:57,264 INFO misc.py line 117 1419922] Train: [14/100][289/1462] Data 0.002 (0.002) Batch 2.613 (2.879) Remain 101:29:15 loss: 0.2341 Lr: 0.00200
[2025-09-06 17:26:59,959 INFO misc.py line 117 1419922] Train: [14/100][290/1462] Data 0.002 (0.002) Batch 2.694 (2.878) Remain 101:27:51 loss: 0.3853 Lr: 0.00200
[2025-09-06 17:27:02,639 INFO misc.py line 117 1419922] Train: [14/100][291/1462] Data 0.002 (0.002) Batch 2.680 (2.878) Remain 101:26:21 loss: 0.3211 Lr: 0.00200
[2025-09-06 17:27:05,238 INFO misc.py line 117 1419922] Train: [14/100][292/1462] Data 0.002 (0.002) Batch 2.599 (2.877) Remain 101:24:15 loss: 0.3743 Lr: 0.00200
[2025-09-06 17:27:07,865 INFO misc.py line 117 1419922] Train: [14/100][293/1462] Data 0.002 (0.002) Batch 2.628 (2.876) Remain 101:22:24 loss: 0.6210 Lr: 0.00200
[2025-09-06 17:27:11,033 INFO misc.py line 117 1419922] Train: [14/100][294/1462] Data 0.002 (0.002) Batch 3.168 (2.877) Remain 101:24:28 loss: 0.3810 Lr: 0.00200
[2025-09-06 17:27:13,837 INFO misc.py line 117 1419922] Train: [14/100][295/1462] Data 0.002 (0.002) Batch 2.804 (2.877) Remain 101:23:53 loss: 0.4513 Lr: 0.00200
[2025-09-06 17:27:16,411 INFO misc.py line 117 1419922] Train: [14/100][296/1462] Data 0.002 (0.002) Batch 2.574 (2.876) Remain 101:21:39 loss: 0.2798 Lr: 0.00200
[2025-09-06 17:27:19,042 INFO misc.py line 117 1419922] Train: [14/100][297/1462] Data 0.002 (0.002) Batch 2.631 (2.875) Remain 101:19:51 loss: 0.6869 Lr: 0.00200
[2025-09-06 17:27:22,140 INFO misc.py line 117 1419922] Train: [14/100][298/1462] Data 0.002 (0.002) Batch 3.097 (2.875) Remain 101:21:24 loss: 0.6656 Lr: 0.00200
[2025-09-06 17:27:25,607 INFO misc.py line 117 1419922] Train: [14/100][299/1462] Data 0.002 (0.002) Batch 3.467 (2.877) Remain 101:25:35 loss: 0.3767 Lr: 0.00200
[2025-09-06 17:27:28,671 INFO misc.py line 117 1419922] Train: [14/100][300/1462] Data 0.002 (0.002) Batch 3.065 (2.878) Remain 101:26:52 loss: 0.3218 Lr: 0.00200
[2025-09-06 17:27:31,493 INFO misc.py line 117 1419922] Train: [14/100][301/1462] Data 0.002 (0.002) Batch 2.822 (2.878) Remain 101:26:25 loss: 0.2581 Lr: 0.00200
[2025-09-06 17:27:34,615 INFO misc.py line 117 1419922] Train: [14/100][302/1462] Data 0.002 (0.002) Batch 3.122 (2.879) Remain 101:28:06 loss: 0.2283 Lr: 0.00200
[2025-09-06 17:27:37,076 INFO misc.py line 117 1419922] Train: [14/100][303/1462] Data 0.002 (0.002) Batch 2.462 (2.877) Remain 101:25:06 loss: 0.4178 Lr: 0.00200
[2025-09-06 17:27:39,904 INFO misc.py line 117 1419922] Train: [14/100][304/1462] Data 0.002 (0.002) Batch 2.828 (2.877) Remain 101:24:43 loss: 0.4508 Lr: 0.00200
[2025-09-06 17:27:42,784 INFO misc.py line 117 1419922] Train: [14/100][305/1462] Data 0.002 (0.002) Batch 2.880 (2.877) Remain 101:24:41 loss: 0.3204 Lr: 0.00200
[2025-09-06 17:27:45,795 INFO misc.py line 117 1419922] Train: [14/100][306/1462] Data 0.001 (0.002) Batch 3.011 (2.878) Remain 101:25:34 loss: 0.2736 Lr: 0.00200
[2025-09-06 17:27:48,475 INFO misc.py line 117 1419922] Train: [14/100][307/1462] Data 0.002 (0.002) Batch 2.680 (2.877) Remain 101:24:09 loss: 0.5401 Lr: 0.00200
[2025-09-06 17:27:51,623 INFO misc.py line 117 1419922] Train: [14/100][308/1462] Data 0.002 (0.002) Batch 3.147 (2.878) Remain 101:25:58 loss: 0.2487 Lr: 0.00200
[2025-09-06 17:27:54,528 INFO misc.py line 117 1419922] Train: [14/100][309/1462] Data 0.002 (0.002) Batch 2.906 (2.878) Remain 101:26:07 loss: 0.5986 Lr: 0.00200
[2025-09-06 17:27:57,267 INFO misc.py line 117 1419922] Train: [14/100][310/1462] Data 0.002 (0.002) Batch 2.739 (2.877) Remain 101:25:07 loss: 0.3929 Lr: 0.00200
[2025-09-06 17:27:59,896 INFO misc.py line 117 1419922] Train: [14/100][311/1462] Data 0.001 (0.002) Batch 2.629 (2.877) Remain 101:23:22 loss: 0.3734 Lr: 0.00200
[2025-09-06 17:28:03,047 INFO misc.py line 117 1419922] Train: [14/100][312/1462] Data 0.002 (0.002) Batch 3.151 (2.878) Remain 101:25:11 loss: 0.6874 Lr: 0.00200
[2025-09-06 17:28:06,010 INFO misc.py line 117 1419922] Train: [14/100][313/1462] Data 0.003 (0.002) Batch 2.963 (2.878) Remain 101:25:43 loss: 0.6048 Lr: 0.00200
[2025-09-06 17:28:08,563 INFO misc.py line 117 1419922] Train: [14/100][314/1462] Data 0.002 (0.002) Batch 2.553 (2.877) Remain 101:23:28 loss: 0.3520 Lr: 0.00200
[2025-09-06 17:28:11,737 INFO misc.py line 117 1419922] Train: [14/100][315/1462] Data 0.002 (0.002) Batch 3.174 (2.878) Remain 101:25:26 loss: 0.6127 Lr: 0.00200
[2025-09-06 17:28:14,639 INFO misc.py line 117 1419922] Train: [14/100][316/1462] Data 0.002 (0.002) Batch 2.902 (2.878) Remain 101:25:33 loss: 0.3784 Lr: 0.00200
[2025-09-06 17:28:17,685 INFO misc.py line 117 1419922] Train: [14/100][317/1462] Data 0.002 (0.002) Batch 3.045 (2.878) Remain 101:26:38 loss: 0.2538 Lr: 0.00200
[2025-09-06 17:28:20,326 INFO misc.py line 117 1419922] Train: [14/100][318/1462] Data 0.002 (0.002) Batch 2.642 (2.878) Remain 101:24:59 loss: 0.5728 Lr: 0.00200
[2025-09-06 17:28:23,189 INFO misc.py line 117 1419922] Train: [14/100][319/1462] Data 0.001 (0.002) Batch 2.862 (2.878) Remain 101:24:50 loss: 0.5479 Lr: 0.00200
[2025-09-06 17:28:25,941 INFO misc.py line 117 1419922] Train: [14/100][320/1462] Data 0.002 (0.002) Batch 2.752 (2.877) Remain 101:23:57 loss: 0.5340 Lr: 0.00200
[2025-09-06 17:28:28,820 INFO misc.py line 117 1419922] Train: [14/100][321/1462] Data 0.002 (0.002) Batch 2.880 (2.877) Remain 101:23:55 loss: 0.3812 Lr: 0.00200
[2025-09-06 17:28:31,589 INFO misc.py line 117 1419922] Train: [14/100][322/1462] Data 0.002 (0.002) Batch 2.768 (2.877) Remain 101:23:09 loss: 0.4533 Lr: 0.00200
[2025-09-06 17:28:34,723 INFO misc.py line 117 1419922] Train: [14/100][323/1462] Data 0.002 (0.002) Batch 3.134 (2.878) Remain 101:24:48 loss: 0.5004 Lr: 0.00200
[2025-09-06 17:28:37,360 INFO misc.py line 117 1419922] Train: [14/100][324/1462] Data 0.002 (0.002) Batch 2.637 (2.877) Remain 101:23:10 loss: 0.4183 Lr: 0.00200
[2025-09-06 17:28:39,980 INFO misc.py line 117 1419922] Train: [14/100][325/1462] Data 0.002 (0.002) Batch 2.620 (2.876) Remain 101:21:26 loss: 0.3856 Lr: 0.00200
[2025-09-06 17:28:42,863 INFO misc.py line 117 1419922] Train: [14/100][326/1462] Data 0.002 (0.002) Batch 2.883 (2.876) Remain 101:21:26 loss: 0.2315 Lr: 0.00200
[2025-09-06 17:28:46,156 INFO misc.py line 117 1419922] Train: [14/100][327/1462] Data 0.002 (0.002) Batch 3.293 (2.877) Remain 101:24:07 loss: 0.2716 Lr: 0.00200
[2025-09-06 17:28:49,092 INFO misc.py line 117 1419922] Train: [14/100][328/1462] Data 0.002 (0.002) Batch 2.936 (2.878) Remain 101:24:27 loss: 0.5838 Lr: 0.00200
[2025-09-06 17:28:51,762 INFO misc.py line 117 1419922] Train: [14/100][329/1462] Data 0.002 (0.002) Batch 2.670 (2.877) Remain 101:23:03 loss: 0.3196 Lr: 0.00200
[2025-09-06 17:28:54,440 INFO misc.py line 117 1419922] Train: [14/100][330/1462] Data 0.001 (0.002) Batch 2.678 (2.876) Remain 101:21:43 loss: 0.3116 Lr: 0.00200
[2025-09-06 17:28:57,348 INFO misc.py line 117 1419922] Train: [14/100][331/1462] Data 0.002 (0.002) Batch 2.908 (2.876) Remain 101:21:52 loss: 0.5910 Lr: 0.00200
[2025-09-06 17:29:00,365 INFO misc.py line 117 1419922] Train: [14/100][332/1462] Data 0.002 (0.002) Batch 3.017 (2.877) Remain 101:22:44 loss: 0.4493 Lr: 0.00200
[2025-09-06 17:29:03,319 INFO misc.py line 117 1419922] Train: [14/100][333/1462] Data 0.002 (0.002) Batch 2.954 (2.877) Remain 101:23:10 loss: 0.3098 Lr: 0.00200
[2025-09-06 17:29:05,968 INFO misc.py line 117 1419922] Train: [14/100][334/1462] Data 0.002 (0.002) Batch 2.649 (2.876) Remain 101:21:40 loss: 0.2237 Lr: 0.00200
[2025-09-06 17:29:08,809 INFO misc.py line 117 1419922] Train: [14/100][335/1462] Data 0.001 (0.002) Batch 2.841 (2.876) Remain 101:21:24 loss: 0.4566 Lr: 0.00200
[2025-09-06 17:29:12,073 INFO misc.py line 117 1419922] Train: [14/100][336/1462] Data 0.002 (0.002) Batch 3.264 (2.877) Remain 101:23:49 loss: 0.2915 Lr: 0.00200
[2025-09-06 17:29:15,039 INFO misc.py line 117 1419922] Train: [14/100][337/1462] Data 0.002 (0.002) Batch 2.965 (2.878) Remain 101:24:19 loss: 0.3554 Lr: 0.00200
[2025-09-06 17:29:17,802 INFO misc.py line 117 1419922] Train: [14/100][338/1462] Data 0.002 (0.002) Batch 2.763 (2.877) Remain 101:23:33 loss: 0.4264 Lr: 0.00200
[2025-09-06 17:29:20,903 INFO misc.py line 117 1419922] Train: [14/100][339/1462] Data 0.001 (0.002) Batch 3.101 (2.878) Remain 101:24:55 loss: 0.3765 Lr: 0.00200
[2025-09-06 17:29:23,572 INFO misc.py line 117 1419922] Train: [14/100][340/1462] Data 0.001 (0.002) Batch 2.669 (2.877) Remain 101:23:33 loss: 0.6125 Lr: 0.00200
[2025-09-06 17:29:26,375 INFO misc.py line 117 1419922] Train: [14/100][341/1462] Data 0.001 (0.002) Batch 2.803 (2.877) Remain 101:23:02 loss: 0.6468 Lr: 0.00200
[2025-09-06 17:29:29,174 INFO misc.py line 117 1419922] Train: [14/100][342/1462] Data 0.001 (0.002) Batch 2.799 (2.877) Remain 101:22:30 loss: 0.4902 Lr: 0.00200
[2025-09-06 17:29:31,802 INFO misc.py line 117 1419922] Train: [14/100][343/1462] Data 0.001 (0.002) Batch 2.628 (2.876) Remain 101:20:54 loss: 0.2830 Lr: 0.00200
[2025-09-06 17:29:34,547 INFO misc.py line 117 1419922] Train: [14/100][344/1462] Data 0.002 (0.002) Batch 2.744 (2.876) Remain 101:20:02 loss: 0.2396 Lr: 0.00200
[2025-09-06 17:29:37,373 INFO misc.py line 117 1419922] Train: [14/100][345/1462] Data 0.001 (0.002) Batch 2.826 (2.876) Remain 101:19:41 loss: 0.4373 Lr: 0.00200
[2025-09-06 17:29:40,490 INFO misc.py line 117 1419922] Train: [14/100][346/1462] Data 0.002 (0.002) Batch 3.117 (2.876) Remain 101:21:07 loss: 0.2846 Lr: 0.00200
[2025-09-06 17:29:43,431 INFO misc.py line 117 1419922] Train: [14/100][347/1462] Data 0.001 (0.002) Batch 2.941 (2.877) Remain 101:21:28 loss: 0.3278 Lr: 0.00200
[2025-09-06 17:29:46,236 INFO misc.py line 117 1419922] Train: [14/100][348/1462] Data 0.002 (0.002) Batch 2.805 (2.876) Remain 101:20:59 loss: 0.3792 Lr: 0.00200
[2025-09-06 17:29:48,964 INFO misc.py line 117 1419922] Train: [14/100][349/1462] Data 0.001 (0.002) Batch 2.728 (2.876) Remain 101:20:02 loss: 0.3708 Lr: 0.00200
[2025-09-06 17:29:51,869 INFO misc.py line 117 1419922] Train: [14/100][350/1462] Data 0.001 (0.002) Batch 2.905 (2.876) Remain 101:20:09 loss: 0.5083 Lr: 0.00200
[2025-09-06 17:29:54,697 INFO misc.py line 117 1419922] Train: [14/100][351/1462] Data 0.002 (0.002) Batch 2.829 (2.876) Remain 101:19:49 loss: 0.2993 Lr: 0.00200
[2025-09-06 17:29:57,290 INFO misc.py line 117 1419922] Train: [14/100][352/1462] Data 0.002 (0.002) Batch 2.593 (2.875) Remain 101:18:04 loss: 0.4746 Lr: 0.00200
[2025-09-06 17:30:00,402 INFO misc.py line 117 1419922] Train: [14/100][353/1462] Data 0.002 (0.002) Batch 3.111 (2.876) Remain 101:19:26 loss: 0.5219 Lr: 0.00200
[2025-09-06 17:30:03,192 INFO misc.py line 117 1419922] Train: [14/100][354/1462] Data 0.002 (0.002) Batch 2.790 (2.876) Remain 101:18:52 loss: 0.3766 Lr: 0.00200
[2025-09-06 17:30:06,120 INFO misc.py line 117 1419922] Train: [14/100][355/1462] Data 0.002 (0.002) Batch 2.928 (2.876) Remain 101:19:08 loss: 0.4137 Lr: 0.00200
[2025-09-06 17:30:09,232 INFO misc.py line 117 1419922] Train: [14/100][356/1462] Data 0.002 (0.002) Batch 3.112 (2.876) Remain 101:20:31 loss: 0.2566 Lr: 0.00200
[2025-09-06 17:30:12,086 INFO misc.py line 117 1419922] Train: [14/100][357/1462] Data 0.002 (0.002) Batch 2.855 (2.876) Remain 101:20:20 loss: 0.2745 Lr: 0.00200
[2025-09-06 17:30:15,109 INFO misc.py line 117 1419922] Train: [14/100][358/1462] Data 0.001 (0.002) Batch 3.023 (2.877) Remain 101:21:09 loss: 0.4444 Lr: 0.00200
[2025-09-06 17:30:18,066 INFO misc.py line 117 1419922] Train: [14/100][359/1462] Data 0.001 (0.002) Batch 2.957 (2.877) Remain 101:21:35 loss: 0.4037 Lr: 0.00200
[2025-09-06 17:30:21,013 INFO misc.py line 117 1419922] Train: [14/100][360/1462] Data 0.002 (0.002) Batch 2.947 (2.877) Remain 101:21:57 loss: 0.4870 Lr: 0.00200
[2025-09-06 17:30:23,834 INFO misc.py line 117 1419922] Train: [14/100][361/1462] Data 0.002 (0.002) Batch 2.821 (2.877) Remain 101:21:34 loss: 0.2630 Lr: 0.00200
[2025-09-06 17:30:26,423 INFO misc.py line 117 1419922] Train: [14/100][362/1462] Data 0.002 (0.002) Batch 2.589 (2.876) Remain 101:19:50 loss: 0.8480 Lr: 0.00200
[2025-09-06 17:30:29,483 INFO misc.py line 117 1419922] Train: [14/100][363/1462] Data 0.002 (0.002) Batch 3.059 (2.877) Remain 101:20:51 loss: 0.4464 Lr: 0.00200
[2025-09-06 17:30:32,174 INFO misc.py line 117 1419922] Train: [14/100][364/1462] Data 0.002 (0.002) Batch 2.691 (2.876) Remain 101:19:43 loss: 0.7077 Lr: 0.00200
[2025-09-06 17:30:34,927 INFO misc.py line 117 1419922] Train: [14/100][365/1462] Data 0.003 (0.002) Batch 2.754 (2.876) Remain 101:18:58 loss: 0.3314 Lr: 0.00200
[2025-09-06 17:30:37,829 INFO misc.py line 117 1419922] Train: [14/100][366/1462] Data 0.002 (0.002) Batch 2.901 (2.876) Remain 101:19:04 loss: 0.6432 Lr: 0.00200
[2025-09-06 17:30:40,597 INFO misc.py line 117 1419922] Train: [14/100][367/1462] Data 0.002 (0.002) Batch 2.769 (2.876) Remain 101:18:23 loss: 0.4662 Lr: 0.00200
[2025-09-06 17:30:43,590 INFO misc.py line 117 1419922] Train: [14/100][368/1462] Data 0.002 (0.002) Batch 2.993 (2.876) Remain 101:19:01 loss: 0.4788 Lr: 0.00200
[2025-09-06 17:30:46,687 INFO misc.py line 117 1419922] Train: [14/100][369/1462] Data 0.001 (0.002) Batch 3.096 (2.877) Remain 101:20:15 loss: 0.3738 Lr: 0.00200
[2025-09-06 17:30:49,594 INFO misc.py line 117 1419922] Train: [14/100][370/1462] Data 0.002 (0.002) Batch 2.907 (2.877) Remain 101:20:23 loss: 0.7420 Lr: 0.00200
[2025-09-06 17:30:52,687 INFO misc.py line 117 1419922] Train: [14/100][371/1462] Data 0.001 (0.002) Batch 3.093 (2.877) Remain 101:21:34 loss: 0.4302 Lr: 0.00200
[2025-09-06 17:30:55,833 INFO misc.py line 117 1419922] Train: [14/100][372/1462] Data 0.002 (0.002) Batch 3.146 (2.878) Remain 101:23:04 loss: 0.4716 Lr: 0.00200
[2025-09-06 17:30:59,012 INFO misc.py line 117 1419922] Train: [14/100][373/1462] Data 0.001 (0.002) Batch 3.179 (2.879) Remain 101:24:44 loss: 0.5275 Lr: 0.00200
[2025-09-06 17:31:01,893 INFO misc.py line 117 1419922] Train: [14/100][374/1462] Data 0.002 (0.002) Batch 2.881 (2.879) Remain 101:24:42 loss: 0.3930 Lr: 0.00200
[2025-09-06 17:31:04,688 INFO misc.py line 117 1419922] Train: [14/100][375/1462] Data 0.001 (0.002) Batch 2.795 (2.879) Remain 101:24:11 loss: 0.4010 Lr: 0.00200
[2025-09-06 17:31:07,817 INFO misc.py line 117 1419922] Train: [14/100][376/1462] Data 0.002 (0.002) Batch 3.129 (2.879) Remain 101:25:33 loss: 0.5357 Lr: 0.00200
[2025-09-06 17:31:10,708 INFO misc.py line 117 1419922] Train: [14/100][377/1462] Data 0.002 (0.002) Batch 2.891 (2.879) Remain 101:25:34 loss: 0.5460 Lr: 0.00200
[2025-09-06 17:31:13,700 INFO misc.py line 117 1419922] Train: [14/100][378/1462] Data 0.002 (0.002) Batch 2.993 (2.880) Remain 101:26:09 loss: 0.2927 Lr: 0.00200
[2025-09-06 17:31:16,674 INFO misc.py line 117 1419922] Train: [14/100][379/1462] Data 0.002 (0.002) Batch 2.973 (2.880) Remain 101:26:38 loss: 0.6794 Lr: 0.00200
[2025-09-06 17:31:19,321 INFO misc.py line 117 1419922] Train: [14/100][380/1462] Data 0.002 (0.002) Batch 2.647 (2.879) Remain 101:25:17 loss: 0.5420 Lr: 0.00200
[2025-09-06 17:31:22,075 INFO misc.py line 117 1419922] Train: [14/100][381/1462] Data 0.001 (0.002) Batch 2.754 (2.879) Remain 101:24:32 loss: 0.6363 Lr: 0.00200
[2025-09-06 17:31:24,786 INFO misc.py line 117 1419922] Train: [14/100][382/1462] Data 0.002 (0.002) Batch 2.710 (2.878) Remain 101:23:33 loss: 0.4110 Lr: 0.00200
[2025-09-06 17:31:27,544 INFO misc.py line 117 1419922] Train: [14/100][383/1462] Data 0.002 (0.002) Batch 2.759 (2.878) Remain 101:22:50 loss: 0.5294 Lr: 0.00200
[2025-09-06 17:31:30,793 INFO misc.py line 117 1419922] Train: [14/100][384/1462] Data 0.002 (0.002) Batch 3.248 (2.879) Remain 101:24:50 loss: 0.3995 Lr: 0.00200
[2025-09-06 17:31:33,209 INFO misc.py line 117 1419922] Train: [14/100][385/1462] Data 0.002 (0.002) Batch 2.417 (2.878) Remain 101:22:14 loss: 0.6777 Lr: 0.00200
[2025-09-06 17:31:36,337 INFO misc.py line 117 1419922] Train: [14/100][386/1462] Data 0.002 (0.002) Batch 3.128 (2.878) Remain 101:23:34 loss: 0.6933 Lr: 0.00200
[2025-09-06 17:31:39,397 INFO misc.py line 117 1419922] Train: [14/100][387/1462] Data 0.002 (0.002) Batch 3.059 (2.879) Remain 101:24:31 loss: 0.2944 Lr: 0.00200
[2025-09-06 17:31:42,217 INFO misc.py line 117 1419922] Train: [14/100][388/1462] Data 0.002 (0.002) Batch 2.821 (2.879) Remain 101:24:09 loss: 0.6706 Lr: 0.00200
[2025-09-06 17:31:45,030 INFO misc.py line 117 1419922] Train: [14/100][389/1462] Data 0.001 (0.002) Batch 2.813 (2.879) Remain 101:23:44 loss: 0.7936 Lr: 0.00200
[2025-09-06 17:31:48,398 INFO misc.py line 117 1419922] Train: [14/100][390/1462] Data 0.001 (0.002) Batch 3.368 (2.880) Remain 101:26:22 loss: 0.3370 Lr: 0.00200
[2025-09-06 17:31:50,962 INFO misc.py line 117 1419922] Train: [14/100][391/1462] Data 0.002 (0.002) Batch 2.564 (2.879) Remain 101:24:36 loss: 0.6113 Lr: 0.00200
[2025-09-06 17:31:53,892 INFO misc.py line 117 1419922] Train: [14/100][392/1462] Data 0.002 (0.002) Batch 2.930 (2.879) Remain 101:24:49 loss: 0.3943 Lr: 0.00200
[2025-09-06 17:31:56,610 INFO misc.py line 117 1419922] Train: [14/100][393/1462] Data 0.002 (0.002) Batch 2.718 (2.879) Remain 101:23:54 loss: 0.6719 Lr: 0.00200
[2025-09-06 17:31:59,568 INFO misc.py line 117 1419922] Train: [14/100][394/1462] Data 0.002 (0.002) Batch 2.958 (2.879) Remain 101:24:17 loss: 0.4467 Lr: 0.00200
[2025-09-06 17:32:02,680 INFO misc.py line 117 1419922] Train: [14/100][395/1462] Data 0.001 (0.002) Batch 3.112 (2.880) Remain 101:25:29 loss: 0.4171 Lr: 0.00200
[2025-09-06 17:32:05,468 INFO misc.py line 117 1419922] Train: [14/100][396/1462] Data 0.002 (0.002) Batch 2.788 (2.879) Remain 101:24:57 loss: 0.5998 Lr: 0.00200
[2025-09-06 17:32:08,567 INFO misc.py line 117 1419922] Train: [14/100][397/1462] Data 0.001 (0.002) Batch 3.099 (2.880) Remain 101:26:05 loss: 0.7498 Lr: 0.00200
[2025-09-06 17:32:11,237 INFO misc.py line 117 1419922] Train: [14/100][398/1462] Data 0.002 (0.002) Batch 2.669 (2.879) Remain 101:24:54 loss: 0.3161 Lr: 0.00200
[2025-09-06 17:32:14,339 INFO misc.py line 117 1419922] Train: [14/100][399/1462] Data 0.001 (0.002) Batch 3.102 (2.880) Remain 101:26:03 loss: 0.4294 Lr: 0.00200
[2025-09-06 17:32:17,506 INFO misc.py line 117 1419922] Train: [14/100][400/1462] Data 0.002 (0.002) Batch 3.166 (2.881) Remain 101:27:31 loss: 0.3080 Lr: 0.00200
[2025-09-06 17:32:20,536 INFO misc.py line 117 1419922] Train: [14/100][401/1462] Data 0.001 (0.002) Batch 3.031 (2.881) Remain 101:28:16 loss: 0.2933 Lr: 0.00200
[2025-09-06 17:32:23,492 INFO misc.py line 117 1419922] Train: [14/100][402/1462] Data 0.001 (0.002) Batch 2.956 (2.881) Remain 101:28:37 loss: 0.4461 Lr: 0.00200
[2025-09-06 17:32:26,160 INFO misc.py line 117 1419922] Train: [14/100][403/1462] Data 0.002 (0.002) Batch 2.668 (2.881) Remain 101:27:27 loss: 0.7139 Lr: 0.00200
[2025-09-06 17:32:28,856 INFO misc.py line 117 1419922] Train: [14/100][404/1462] Data 0.001 (0.002) Batch 2.696 (2.880) Remain 101:26:26 loss: 0.3468 Lr: 0.00200
[2025-09-06 17:32:31,967 INFO misc.py line 117 1419922] Train: [14/100][405/1462] Data 0.002 (0.002) Batch 3.111 (2.881) Remain 101:27:35 loss: 0.2728 Lr: 0.00200
[2025-09-06 17:32:34,882 INFO misc.py line 117 1419922] Train: [14/100][406/1462] Data 0.002 (0.002) Batch 2.915 (2.881) Remain 101:27:43 loss: 0.7782 Lr: 0.00200
[2025-09-06 17:32:37,662 INFO misc.py line 117 1419922] Train: [14/100][407/1462] Data 0.001 (0.002) Batch 2.780 (2.881) Remain 101:27:09 loss: 0.3106 Lr: 0.00200
[2025-09-06 17:32:40,437 INFO misc.py line 117 1419922] Train: [14/100][408/1462] Data 0.001 (0.002) Batch 2.775 (2.880) Remain 101:26:33 loss: 0.2758 Lr: 0.00200
[2025-09-06 17:32:43,453 INFO misc.py line 117 1419922] Train: [14/100][409/1462] Data 0.002 (0.002) Batch 3.016 (2.881) Remain 101:27:12 loss: 0.2953 Lr: 0.00200
[2025-09-06 17:32:46,543 INFO misc.py line 117 1419922] Train: [14/100][410/1462] Data 0.001 (0.002) Batch 3.090 (2.881) Remain 101:28:15 loss: 0.5206 Lr: 0.00200
[2025-09-06 17:32:49,419 INFO misc.py line 117 1419922] Train: [14/100][411/1462] Data 0.001 (0.002) Batch 2.876 (2.881) Remain 101:28:10 loss: 0.3715 Lr: 0.00200
[2025-09-06 17:32:52,250 INFO misc.py line 117 1419922] Train: [14/100][412/1462] Data 0.001 (0.002) Batch 2.830 (2.881) Remain 101:27:51 loss: 0.4867 Lr: 0.00200
[2025-09-06 17:32:55,300 INFO misc.py line 117 1419922] Train: [14/100][413/1462] Data 0.001 (0.002) Batch 3.051 (2.882) Remain 101:28:41 loss: 0.3510 Lr: 0.00200
[2025-09-06 17:32:58,263 INFO misc.py line 117 1419922] Train: [14/100][414/1462] Data 0.002 (0.002) Batch 2.963 (2.882) Remain 101:29:03 loss: 0.6213 Lr: 0.00200
[2025-09-06 17:33:01,153 INFO misc.py line 117 1419922] Train: [14/100][415/1462] Data 0.002 (0.002) Batch 2.890 (2.882) Remain 101:29:03 loss: 0.5011 Lr: 0.00200
[2025-09-06 17:33:04,118 INFO misc.py line 117 1419922] Train: [14/100][416/1462] Data 0.002 (0.002) Batch 2.964 (2.882) Remain 101:29:25 loss: 0.4413 Lr: 0.00200
[2025-09-06 17:33:07,201 INFO misc.py line 117 1419922] Train: [14/100][417/1462] Data 0.002 (0.002) Batch 3.083 (2.882) Remain 101:30:24 loss: 0.4845 Lr: 0.00200
[2025-09-06 17:33:10,133 INFO misc.py line 117 1419922] Train: [14/100][418/1462] Data 0.002 (0.002) Batch 2.932 (2.883) Remain 101:30:37 loss: 0.4239 Lr: 0.00200
[2025-09-06 17:33:13,127 INFO misc.py line 117 1419922] Train: [14/100][419/1462] Data 0.002 (0.002) Batch 2.994 (2.883) Remain 101:31:08 loss: 0.2650 Lr: 0.00200
[2025-09-06 17:33:16,107 INFO misc.py line 117 1419922] Train: [14/100][420/1462] Data 0.002 (0.002) Batch 2.980 (2.883) Remain 101:31:34 loss: 0.3853 Lr: 0.00200
[2025-09-06 17:33:18,740 INFO misc.py line 117 1419922] Train: [14/100][421/1462] Data 0.001 (0.002) Batch 2.632 (2.882) Remain 101:30:15 loss: 0.5789 Lr: 0.00200
[2025-09-06 17:33:21,741 INFO misc.py line 117 1419922] Train: [14/100][422/1462] Data 0.002 (0.002) Batch 3.001 (2.883) Remain 101:30:48 loss: 0.4095 Lr: 0.00200
[2025-09-06 17:33:24,628 INFO misc.py line 117 1419922] Train: [14/100][423/1462] Data 0.002 (0.002) Batch 2.888 (2.883) Remain 101:30:47 loss: 0.3960 Lr: 0.00200
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [64,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [65,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [66,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [67,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [68,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [69,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [70,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [71,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [72,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [73,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [74,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [75,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [76,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [77,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [78,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [79,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [80,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [81,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [82,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [83,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [84,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [85,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [86,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [87,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [88,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [89,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [90,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [91,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [92,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [93,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [94,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [222,0,0], thread: [95,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
[2025-09-06 17:33:26,512 ERROR events.py line 611 1419923] Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 175, in train
    self.run_step()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 202, in run_step
    self.scaler.scale(loss).backward()
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_tensor.py", line 581, in backward
    torch.autograd.backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/__init__.py", line 347, in backward
    _engine_run_backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/graph.py", line 825, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


[rank1]:[W906 17:33:26.008011115 CUDAGuardImpl.h:119] Warning: CUDA warning: device-side assert triggered (function destroyEvent)
terminate called after throwing an instance of 'c10::Error'
  what():  CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

Exception raised from c10_cuda_check_implementation at /opt/conda/conda-bld/pytorch_1728929546833/work/c10/cuda/CUDAException.cpp:43 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::string) + 0x96 (0x7f4fbb21b446 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: c10::detail::torchCheckFail(char const*, char const*, unsigned int, std::string const&) + 0x64 (0x7f4fbb1c56e4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #2: c10::cuda::c10_cuda_check_implementation(int, char const*, char const*, int, bool) + 0x118 (0x7f4fbb308a18 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #3: <unknown function> + 0x1f92e (0x7f4fbb2cf92e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #4: <unknown function> + 0x20a57 (0x7f4fbb2d0a57 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #5: <unknown function> + 0x20c5f (0x7f4fbb2d0c5f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #6: <unknown function> + 0x5faf70 (0x7f500c486f70 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #7: <unknown function> + 0x6f69f (0x7f4fbb1fc69f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #8: c10::TensorImpl::~TensorImpl() + 0x21b (0x7f4fbb1f537b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #9: c10::TensorImpl::~TensorImpl() + 0x9 (0x7f4fbb1f5529 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #10: c10d::Reducer::~Reducer() + 0x5c4 (0x7f50041273b4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #11: std::_Sp_counted_ptr<c10d::Reducer*, (__gnu_cxx::_Lock_policy)2>::_M_dispose() + 0x12 (0x7f500cc2ebf2 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #12: std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release() + 0x48 (0x7f500c34a078 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #13: <unknown function> + 0xdadc21 (0x7f500cc39c21 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #14: <unknown function> + 0x4c90f3 (0x7f500c3550f3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #15: <unknown function> + 0x4c9c71 (0x7f500c355c71 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #16: <unknown function> + 0x128b86 (0x5593d99c2b86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #17: <unknown function> + 0x14b2a0 (0x5593d99e52a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #18: <unknown function> + 0x128b86 (0x5593d99c2b86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #19: <unknown function> + 0x14b2a0 (0x5593d99e52a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #20: <unknown function> + 0x134be7 (0x5593d99cebe7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #21: <unknown function> + 0x134ccb (0x5593d99ceccb in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #22: <unknown function> + 0x144c3b (0x5593d99dec3b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #23: <unknown function> + 0x149f1c (0x5593d99e3f1c in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #24: <unknown function> + 0x121412 (0x5593d99bb412 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #25: <unknown function> + 0x203bb1 (0x5593d9a9dbb1 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #26: _PyEval_EvalFrameDefault + 0x46cf (0x5593d99ca77f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #27: _PyFunction_Vectorcall + 0x6c (0x5593d99d60ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #28: _PyEval_EvalFrameDefault + 0x700 (0x5593d99c67b0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #29: _PyFunction_Vectorcall + 0x6c (0x5593d99d60ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #30: _PyEval_EvalFrameDefault + 0x30c (0x5593d99c63bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #31: _PyFunction_Vectorcall + 0x6c (0x5593d99d60ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #32: _PyEval_EvalFrameDefault + 0x1340 (0x5593d99c73f0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #33: <unknown function> + 0x1cc3bc (0x5593d9a663bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #34: PyEval_EvalCode + 0x87 (0x5593d9a66307 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #35: <unknown function> + 0x1fc96a (0x5593d9a9696a in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #36: <unknown function> + 0x1f7df3 (0x5593d9a91df3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #37: PyRun_StringFlags + 0x7d (0x5593d9a8a4ad in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #38: PyRun_SimpleStringFlags + 0x3c (0x5593d9a8a3ac in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #39: Py_RunMain + 0x3ba (0x5593d9a895ba in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #40: Py_BytesMain + 0x37 (0x5593d9a59ef7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #41: <unknown function> + 0x29d90 (0x7f5015596d90 in /lib/x86_64-linux-gnu/libc.so.6)
frame #42: __libc_start_main + 0x80 (0x7f5015596e40 in /lib/x86_64-linux-gnu/libc.so.6)
frame #43: <unknown function> + 0x1bfe0e (0x5593d9a59e0e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)

W0906 17:33:27.239000 1419788 site-packages/torch/multiprocessing/spawn.py:160] Terminating process 1419922 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 38, in <module>
    main()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 27, in main
    launch(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 74, in launch
    mp.spawn(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 328, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 284, in start_processes
    while not context.join():
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 184, in join
    raise ProcessExitedException(
torch.multiprocessing.spawn.ProcessExitedException: process 1 terminated with signal SIGABRT
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 35 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
