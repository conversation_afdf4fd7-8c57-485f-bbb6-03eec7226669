Starting training at 2025年 09月 05日 星期五 23:50:07 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-05 23:50:28,533 INFO train.py line 136 1296420] => Loading config ...
[2025-09-05 23:50:28,533 INFO train.py line 138 1296420] Save path: exp/powscan/splitSections
[2025-09-05 23:50:28,670 INFO train.py line 139 1296420] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 12
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 6
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-05 23:50:28,670 INFO train.py line 140 1296420] => Building model ...
[2025-09-05 23:50:28,721 INFO train.py line 241 1296420] Num params: 3908496
[2025-09-05 23:50:28,768 INFO train.py line 142 1296420] => Building writer ...
[2025-09-05 23:50:28,770 INFO train.py line 251 1296420] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-05 23:50:28,770 INFO train.py line 144 1296420] => Building train dataset & dataloader ...
[2025-09-05 23:50:30,477 INFO defaults.py line 70 1296420] Totally 1846 x 30 samples in powscan train set.
[2025-09-05 23:50:30,478 INFO train.py line 146 1296420] => Building val dataset & dataloader ...
[2025-09-05 23:50:30,686 INFO defaults.py line 70 1296420] Totally 236 x 1 samples in powscan val set.
[2025-09-05 23:50:30,687 INFO train.py line 148 1296420] => Building optimize, scheduler, scaler(amp) ...
[2025-09-05 23:50:30,835 INFO train.py line 152 1296420] => Building hooks ...
[2025-09-05 23:50:30,836 INFO misc.py line 237 1296420] => Loading checkpoint & weight ...
[2025-09-05 23:50:30,836 INFO misc.py line 239 1296420] Loading weight at: exp/powscan/splitSections/model/model_last.pth
[2025-09-05 23:50:31,047 INFO misc.py line 245 1296420] Loading layer weights with keyword: , replace keyword with: 
[2025-09-05 23:50:31,075 INFO misc.py line 262 1296420] Missing keys: []
[2025-09-05 23:50:31,075 INFO misc.py line 264 1296420] Resuming train at eval epoch: 4
[2025-09-05 23:50:31,081 INFO train.py line 159 1296420] >>>>>>>>>>>>>>>> Start Training >>>>>>>>>>>>>>>>
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
[2025-09-05 23:51:59,483 INFO misc.py line 117 1296420] Train: [5/100][1/4615] Data 1.659 (1.659) Batch 5.487 (5.487) Remain 675:18:05 loss: 0.6979 Lr: 0.00200
[2025-09-05 23:52:01,828 INFO misc.py line 117 1296420] Train: [5/100][2/4615] Data 0.002 (0.002) Batch 2.345 (2.345) Remain 288:33:09 loss: 0.4251 Lr: 0.00200
[2025-09-05 23:52:03,992 INFO misc.py line 117 1296420] Train: [5/100][3/4615] Data 0.001 (0.001) Batch 2.164 (2.164) Remain 266:17:40 loss: 0.6303 Lr: 0.00200
[2025-09-05 23:52:06,502 INFO misc.py line 117 1296420] Train: [5/100][4/4615] Data 0.002 (0.002) Batch 2.510 (2.510) Remain 308:54:07 loss: 0.5419 Lr: 0.00200
[2025-09-05 23:52:08,989 INFO misc.py line 117 1296420] Train: [5/100][5/4615] Data 0.001 (0.001) Batch 2.487 (2.499) Remain 307:30:34 loss: 0.7479 Lr: 0.00200
[2025-09-05 23:52:11,083 INFO misc.py line 117 1296420] Train: [5/100][6/4615] Data 0.002 (0.001) Batch 2.093 (2.364) Remain 290:52:15 loss: 0.6768 Lr: 0.00200
[2025-09-05 23:52:13,260 INFO misc.py line 117 1296420] Train: [5/100][7/4615] Data 0.002 (0.002) Batch 2.177 (2.317) Remain 285:08:11 loss: 0.5873 Lr: 0.00200
[2025-09-05 23:52:15,613 INFO misc.py line 117 1296420] Train: [5/100][8/4615] Data 0.001 (0.001) Batch 2.353 (2.324) Remain 286:01:13 loss: 0.5798 Lr: 0.00200
[2025-09-05 23:52:17,962 INFO misc.py line 117 1296420] Train: [5/100][9/4615] Data 0.001 (0.001) Batch 2.349 (2.328) Remain 286:31:48 loss: 0.7766 Lr: 0.00200
[2025-09-05 23:52:20,685 INFO misc.py line 117 1296420] Train: [5/100][10/4615] Data 0.002 (0.001) Batch 2.723 (2.385) Remain 293:28:04 loss: 0.5323 Lr: 0.00200
[2025-09-05 23:52:23,382 INFO misc.py line 117 1296420] Train: [5/100][11/4615] Data 0.001 (0.001) Batch 2.697 (2.424) Remain 298:16:40 loss: 0.3488 Lr: 0.00200
[2025-09-05 23:52:25,802 INFO misc.py line 117 1296420] Train: [5/100][12/4615] Data 0.001 (0.001) Batch 2.420 (2.423) Remain 298:13:46 loss: 0.5011 Lr: 0.00200
[2025-09-05 23:52:28,624 INFO misc.py line 117 1296420] Train: [5/100][13/4615] Data 0.001 (0.001) Batch 2.822 (2.463) Remain 303:07:42 loss: 0.8058 Lr: 0.00200
[2025-09-05 23:52:31,025 INFO misc.py line 117 1296420] Train: [5/100][14/4615] Data 0.001 (0.001) Batch 2.401 (2.458) Remain 302:25:56 loss: 0.8558 Lr: 0.00200
[2025-09-05 23:52:33,563 INFO misc.py line 117 1296420] Train: [5/100][15/4615] Data 0.001 (0.001) Batch 2.538 (2.464) Remain 303:15:28 loss: 0.4964 Lr: 0.00200
[2025-09-05 23:52:36,483 INFO misc.py line 117 1296420] Train: [5/100][16/4615] Data 0.002 (0.001) Batch 2.919 (2.499) Remain 307:33:59 loss: 0.5847 Lr: 0.00200
[2025-09-05 23:52:39,488 INFO misc.py line 117 1296420] Train: [5/100][17/4615] Data 0.001 (0.001) Batch 3.006 (2.535) Remain 312:01:01 loss: 0.3667 Lr: 0.00200
[2025-09-05 23:52:42,142 INFO misc.py line 117 1296420] Train: [5/100][18/4615] Data 0.001 (0.001) Batch 2.654 (2.543) Remain 312:59:20 loss: 0.3563 Lr: 0.00200
[2025-09-05 23:52:44,776 INFO misc.py line 117 1296420] Train: [5/100][19/4615] Data 0.001 (0.001) Batch 2.634 (2.549) Remain 313:41:12 loss: 0.5828 Lr: 0.00200
[2025-09-05 23:52:47,537 INFO misc.py line 117 1296420] Train: [5/100][20/4615] Data 0.001 (0.001) Batch 2.761 (2.561) Remain 315:13:09 loss: 0.5771 Lr: 0.00200
[2025-09-05 23:52:50,155 INFO misc.py line 117 1296420] Train: [5/100][21/4615] Data 0.002 (0.001) Batch 2.618 (2.565) Remain 315:36:16 loss: 0.3751 Lr: 0.00200
[2025-09-05 23:52:52,921 INFO misc.py line 117 1296420] Train: [5/100][22/4615] Data 0.001 (0.001) Batch 2.766 (2.575) Remain 316:54:29 loss: 0.5550 Lr: 0.00200
[2025-09-05 23:52:55,857 INFO misc.py line 117 1296420] Train: [5/100][23/4615] Data 0.002 (0.001) Batch 2.936 (2.593) Remain 319:07:34 loss: 0.3471 Lr: 0.00200
[2025-09-05 23:52:58,532 INFO misc.py line 117 1296420] Train: [5/100][24/4615] Data 0.001 (0.001) Batch 2.675 (2.597) Remain 319:36:23 loss: 0.4498 Lr: 0.00200
[2025-09-05 23:53:01,598 INFO misc.py line 117 1296420] Train: [5/100][25/4615] Data 0.001 (0.001) Batch 3.066 (2.618) Remain 322:13:39 loss: 0.3756 Lr: 0.00200
[2025-09-05 23:53:04,107 INFO misc.py line 117 1296420] Train: [5/100][26/4615] Data 0.001 (0.001) Batch 2.508 (2.614) Remain 321:38:16 loss: 0.3855 Lr: 0.00200
[2025-09-05 23:53:06,579 INFO misc.py line 117 1296420] Train: [5/100][27/4615] Data 0.001 (0.001) Batch 2.472 (2.608) Remain 320:54:44 loss: 0.7480 Lr: 0.00200
[2025-09-05 23:53:09,149 INFO misc.py line 117 1296420] Train: [5/100][28/4615] Data 0.002 (0.001) Batch 2.570 (2.606) Remain 320:43:31 loss: 0.6767 Lr: 0.00200
[2025-09-05 23:53:11,847 INFO misc.py line 117 1296420] Train: [5/100][29/4615] Data 0.002 (0.001) Batch 2.698 (2.610) Remain 321:09:27 loss: 0.5389 Lr: 0.00200
[2025-09-05 23:53:14,249 INFO misc.py line 117 1296420] Train: [5/100][30/4615] Data 0.001 (0.001) Batch 2.402 (2.602) Remain 320:12:43 loss: 0.4061 Lr: 0.00200
[2025-09-05 23:53:16,874 INFO misc.py line 117 1296420] Train: [5/100][31/4615] Data 0.002 (0.001) Batch 2.625 (2.603) Remain 320:18:39 loss: 0.4712 Lr: 0.00200
[2025-09-05 23:53:19,961 INFO misc.py line 117 1296420] Train: [5/100][32/4615] Data 0.001 (0.001) Batch 3.087 (2.620) Remain 322:21:59 loss: 0.5771 Lr: 0.00200
[2025-09-05 23:53:22,064 INFO misc.py line 117 1296420] Train: [5/100][33/4615] Data 0.001 (0.001) Batch 2.103 (2.602) Remain 320:14:44 loss: 0.4733 Lr: 0.00200
[2025-09-05 23:53:24,999 INFO misc.py line 117 1296420] Train: [5/100][34/4615] Data 0.001 (0.001) Batch 2.935 (2.613) Remain 321:33:53 loss: 0.5717 Lr: 0.00200
[2025-09-05 23:53:27,560 INFO misc.py line 117 1296420] Train: [5/100][35/4615] Data 0.001 (0.001) Batch 2.561 (2.612) Remain 321:21:48 loss: 0.5218 Lr: 0.00200
[2025-09-05 23:53:29,901 INFO misc.py line 117 1296420] Train: [5/100][36/4615] Data 0.001 (0.001) Batch 2.340 (2.603) Remain 320:21:06 loss: 0.5731 Lr: 0.00200
[2025-09-05 23:53:32,605 INFO misc.py line 117 1296420] Train: [5/100][37/4615] Data 0.001 (0.001) Batch 2.705 (2.606) Remain 320:43:04 loss: 0.5994 Lr: 0.00200
[2025-09-05 23:53:34,881 INFO misc.py line 117 1296420] Train: [5/100][38/4615] Data 0.001 (0.001) Batch 2.276 (2.597) Remain 319:33:15 loss: 0.4763 Lr: 0.00200
[2025-09-05 23:53:37,790 INFO misc.py line 117 1296420] Train: [5/100][39/4615] Data 0.001 (0.001) Batch 2.909 (2.606) Remain 320:37:19 loss: 0.6842 Lr: 0.00200
[2025-09-05 23:53:40,162 INFO misc.py line 117 1296420] Train: [5/100][40/4615] Data 0.002 (0.001) Batch 2.372 (2.599) Remain 319:50:43 loss: 0.5349 Lr: 0.00200
[2025-09-05 23:53:42,821 INFO misc.py line 117 1296420] Train: [5/100][41/4615] Data 0.001 (0.001) Batch 2.658 (2.601) Remain 320:02:10 loss: 0.9530 Lr: 0.00200
[2025-09-05 23:53:45,798 INFO misc.py line 117 1296420] Train: [5/100][42/4615] Data 0.002 (0.001) Batch 2.977 (2.610) Remain 321:13:21 loss: 0.3892 Lr: 0.00200
[2025-09-05 23:53:48,303 INFO misc.py line 117 1296420] Train: [5/100][43/4615] Data 0.002 (0.001) Batch 2.505 (2.608) Remain 320:53:50 loss: 0.3552 Lr: 0.00200
[2025-09-05 23:53:51,170 INFO misc.py line 117 1296420] Train: [5/100][44/4615] Data 0.002 (0.001) Batch 2.868 (2.614) Remain 321:40:39 loss: 0.4480 Lr: 0.00200
[2025-09-05 23:53:53,616 INFO misc.py line 117 1296420] Train: [5/100][45/4615] Data 0.002 (0.001) Batch 2.445 (2.610) Remain 321:10:54 loss: 0.2864 Lr: 0.00200
[2025-09-05 23:53:56,327 INFO misc.py line 117 1296420] Train: [5/100][46/4615] Data 0.001 (0.001) Batch 2.712 (2.612) Remain 321:28:17 loss: 1.1774 Lr: 0.00200
[2025-09-05 23:53:59,138 INFO misc.py line 117 1296420] Train: [5/100][47/4615] Data 0.002 (0.001) Batch 2.810 (2.617) Remain 322:01:26 loss: 0.7270 Lr: 0.00200
[2025-09-05 23:54:01,484 INFO misc.py line 117 1296420] Train: [5/100][48/4615] Data 0.001 (0.001) Batch 2.347 (2.611) Remain 321:17:02 loss: 0.8182 Lr: 0.00200
[2025-09-05 23:54:04,024 INFO misc.py line 117 1296420] Train: [5/100][49/4615] Data 0.002 (0.001) Batch 2.540 (2.609) Remain 321:05:37 loss: 0.3977 Lr: 0.00200
