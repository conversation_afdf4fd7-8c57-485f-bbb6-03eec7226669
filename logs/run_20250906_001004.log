Starting training at 2025年 09月 06日 星期六 00:10:04 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-06 00:10:25,864 INFO train.py line 136 1307829] => Loading config ...
[2025-09-06 00:10:25,864 INFO train.py line 138 1307829] Save path: exp/powscan/splitSections
[2025-09-06 00:10:26,028 INFO train.py line 139 1307829] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 12
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 6
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-06 00:10:26,028 INFO train.py line 140 1307829] => Building model ...
[2025-09-06 00:10:26,080 INFO train.py line 241 1307829] Num params: 3908496
[2025-09-06 00:10:28,967 INFO train.py line 142 1307829] => Building writer ...
[2025-09-06 00:10:28,969 INFO train.py line 251 1307829] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-06 00:10:28,969 INFO train.py line 144 1307829] => Building train dataset & dataloader ...
[2025-09-06 00:10:30,660 INFO defaults.py line 70 1307829] Totally 755 x 30 samples in powscan train set.
[2025-09-06 00:10:30,661 INFO train.py line 146 1307829] => Building val dataset & dataloader ...
[2025-09-06 00:10:30,998 INFO defaults.py line 70 1307829] Totally 100 x 1 samples in powscan val set.
[2025-09-06 00:10:30,999 INFO train.py line 148 1307829] => Building optimize, scheduler, scaler(amp) ...
[2025-09-06 00:10:31,154 INFO train.py line 152 1307829] => Building hooks ...
[2025-09-06 00:10:31,155 INFO misc.py line 237 1307829] => Loading checkpoint & weight ...
[2025-09-06 00:10:31,155 INFO misc.py line 239 1307829] Loading weight at: exp/powscan/splitSections/model/model_last.pth
[2025-09-06 00:10:36,057 INFO misc.py line 245 1307829] Loading layer weights with keyword: , replace keyword with: 
[2025-09-06 00:10:37,935 INFO misc.py line 262 1307829] Missing keys: []
[2025-09-06 00:10:37,935 INFO misc.py line 264 1307829] Resuming train at eval epoch: 4
[2025-09-06 00:10:37,941 INFO train.py line 159 1307829] >>>>>>>>>>>>>>>> Start Training >>>>>>>>>>>>>>>>
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
[2025-09-06 00:10:48,428 ERROR events.py line 611 1307829] Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 168, in train
    for (
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 701, in __next__
    data = self._next_data()
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 1465, in _next_data
    return self._process_data(data)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 1491, in _process_data
    data.reraise()
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_utils.py", line 715, in reraise
    raise exception
AttributeError: Caught AttributeError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/data/_utils/worker.py", line 351, in _worker_loop
    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 52, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 52, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/defaults.py", line 189, in __getitem__
    return self.prepare_train_data(idx)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/defaults.py", line 149, in prepare_train_data
    data_dict = self.transform(data_dict)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/transform.py", line 1193, in __call__
    data_dict = t(data_dict)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/transform.py", line 175, in __call__
    if "coord" in data_dict.keys():
AttributeError: 'NoneType' object has no attribute 'keys'


[rank0]:[W906 00:10:48.265722219 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())
W0906 00:10:50.123000 1307676 site-packages/torch/multiprocessing/spawn.py:160] Terminating process 1307830 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 38, in <module>
    main()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 27, in main
    launch(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 74, in launch
    mp.spawn(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 328, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 284, in start_processes
    while not context.join():
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 192, in join
    raise ProcessExitedException(
torch.multiprocessing.spawn.ProcessExitedException: process 0 terminated with exit code 1
####Warning: Grid data/powscan/processed/数据3/25-26(25_26)_grid_2_1_0 has only 4168 points (less than 60000)
!!!!Warning: Empty grid extracted from data/powscan/processed/数据6/20-21(20_21)_grid_2_2_0
!!!!Warning: Empty grid extracted from data/powscan/processed/数据9/1-2(1_2)_grid_3_2_0
####Warning: Grid data/powscan/processed/数据9/15-16(15_16)_grid_2_4_0 has only 27107 points (less than 60000)
!!!!Warning: Empty grid extracted from data/powscan/processed/数据9/23-24(23_24)_grid_2_1_1
!!!!Warning: Empty grid extracted from data/powscan/processed/数据2/9-10(9_10)_grid_3_0_0
####Warning: Grid data/powscan/processed/数据9/23-24(23_24)_grid_0_3_1 has only 35158 points (less than 60000)
####Warning: Grid data/powscan/processed/数据6/16-17(16_17)_grid_0_0_0 has only 2697 points (less than 60000)
!!!!Warning: Empty grid extracted from data/powscan/processed/数据3/21-22(21_22)_grid_0_0_0
####Warning: Grid data/powscan/processed/数据9/23-24(23_24)_grid_0_3_1 has only 35158 points (less than 60000)
####Warning: Grid data/powscan/processed/数据9/34-35(34_35)_grid_1_2_0 has only 140 points (less than 60000)
####Warning: Grid data/powscan/processed/数据3/11-12(11_12)_grid_2_1_0 has only 1497 points (less than 60000)
####Warning: Grid data/powscan/processed/数据2/5-6(5_6)_grid_2_1_0 has only 39370 points (less than 60000)
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 32 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
