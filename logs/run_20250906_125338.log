Starting training at 2025年 09月 06日 星期六 12:53:38 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-06 12:53:59,685 INFO train.py line 136 1395871] => Loading config ...
[2025-09-06 12:53:59,686 INFO train.py line 138 1395871] Save path: exp/powscan/splitSections
[2025-09-06 12:53:59,823 INFO train.py line 139 1395871] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 12
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 6
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-06 12:53:59,823 INFO train.py line 140 1395871] => Building model ...
[2025-09-06 12:53:59,876 INFO train.py line 241 1395871] Num params: 3908496
[2025-09-06 12:53:59,922 INFO train.py line 142 1395871] => Building writer ...
[2025-09-06 12:53:59,924 INFO train.py line 251 1395871] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-06 12:53:59,924 INFO train.py line 144 1395871] => Building train dataset & dataloader ...
[2025-09-06 12:54:18,836 INFO defaults.py line 70 1395871] Totally 585 x 30 samples in powscan train set.
[2025-09-06 12:54:18,836 INFO train.py line 146 1395871] => Building val dataset & dataloader ...
[2025-09-06 12:54:20,212 INFO defaults.py line 70 1395871] Totally 80 x 1 samples in powscan val set.
[2025-09-06 12:54:20,213 INFO train.py line 148 1395871] => Building optimize, scheduler, scaler(amp) ...
[2025-09-06 12:54:20,363 INFO train.py line 152 1395871] => Building hooks ...
[2025-09-06 12:54:20,364 INFO misc.py line 237 1395871] => Loading checkpoint & weight ...
[2025-09-06 12:54:20,364 INFO misc.py line 239 1395871] Loading weight at: exp/powscan/splitSections/model/model_last.pth
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
[2025-09-06 12:54:20,592 INFO misc.py line 245 1395871] Loading layer weights with keyword: , replace keyword with: 
[2025-09-06 12:54:20,622 INFO misc.py line 262 1395871] Missing keys: []
[2025-09-06 12:54:20,622 INFO misc.py line 264 1395871] Resuming train at eval epoch: 12
[2025-09-06 12:54:20,628 INFO train.py line 159 1395871] >>>>>>>>>>>>>>>> Start Training >>>>>>>>>>>>>>>>
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
[2025-09-06 12:54:37,583 INFO misc.py line 117 1395871] Train: [13/100][1/1462] Data 12.558 (12.558) Batch 16.914 (16.914) Remain 604:27:35 loss: 0.3139 Lr: 0.00200
[2025-09-06 12:54:40,348 INFO misc.py line 117 1395871] Train: [13/100][2/1462] Data 0.002 (0.002) Batch 2.765 (2.765) Remain 98:49:17 loss: 0.5060 Lr: 0.00200
[2025-09-06 12:54:43,392 INFO misc.py line 117 1395871] Train: [13/100][3/1462] Data 0.001 (0.001) Batch 3.044 (3.044) Remain 108:46:54 loss: 0.3467 Lr: 0.00200
[2025-09-06 12:54:46,499 INFO misc.py line 117 1395871] Train: [13/100][4/1462] Data 0.001 (0.001) Batch 3.107 (3.107) Remain 111:01:00 loss: 0.5299 Lr: 0.00200
[2025-09-06 12:54:49,592 INFO misc.py line 117 1395871] Train: [13/100][5/1462] Data 0.001 (0.001) Batch 3.094 (3.100) Remain 110:47:26 loss: 0.7278 Lr: 0.00200
[2025-09-06 12:54:52,271 INFO misc.py line 117 1395871] Train: [13/100][6/1462] Data 0.002 (0.001) Batch 2.679 (2.960) Remain 105:46:08 loss: 0.6891 Lr: 0.00200
[2025-09-06 12:54:55,333 INFO misc.py line 117 1395871] Train: [13/100][7/1462] Data 0.001 (0.001) Batch 3.062 (2.985) Remain 106:41:02 loss: 0.3695 Lr: 0.00200
[2025-09-06 12:54:58,434 INFO misc.py line 117 1395871] Train: [13/100][8/1462] Data 0.001 (0.001) Batch 3.100 (3.008) Remain 107:30:21 loss: 0.4590 Lr: 0.00200
[2025-09-06 12:55:01,830 INFO misc.py line 117 1395871] Train: [13/100][9/1462] Data 0.001 (0.001) Batch 3.396 (3.073) Remain 109:48:52 loss: 0.3422 Lr: 0.00200
[2025-09-06 12:55:04,670 INFO misc.py line 117 1395871] Train: [13/100][10/1462] Data 0.001 (0.001) Batch 2.840 (3.040) Remain 108:37:19 loss: 0.6448 Lr: 0.00200
[2025-09-06 12:55:07,561 INFO misc.py line 117 1395871] Train: [13/100][11/1462] Data 0.001 (0.001) Batch 2.891 (3.021) Remain 107:57:25 loss: 0.3998 Lr: 0.00200
[2025-09-06 12:55:10,257 INFO misc.py line 117 1395871] Train: [13/100][12/1462] Data 0.002 (0.001) Batch 2.697 (2.985) Remain 106:40:06 loss: 0.4794 Lr: 0.00200
[2025-09-06 12:55:13,070 INFO misc.py line 117 1395871] Train: [13/100][13/1462] Data 0.001 (0.001) Batch 2.813 (2.968) Remain 106:03:10 loss: 0.4772 Lr: 0.00200
[2025-09-06 12:55:15,782 INFO misc.py line 117 1395871] Train: [13/100][14/1462] Data 0.001 (0.001) Batch 2.712 (2.945) Remain 105:13:11 loss: 0.5508 Lr: 0.00200
[2025-09-06 12:55:18,948 INFO misc.py line 117 1395871] Train: [13/100][15/1462] Data 0.001 (0.001) Batch 3.166 (2.963) Remain 105:52:43 loss: 0.6422 Lr: 0.00200
[2025-09-06 12:55:21,820 INFO misc.py line 117 1395871] Train: [13/100][16/1462] Data 0.001 (0.001) Batch 2.871 (2.956) Remain 105:37:34 loss: 0.2217 Lr: 0.00200
[2025-09-06 12:55:24,896 INFO misc.py line 117 1395871] Train: [13/100][17/1462] Data 0.001 (0.001) Batch 3.077 (2.965) Remain 105:56:00 loss: 0.4870 Lr: 0.00200
[2025-09-06 12:55:27,746 INFO misc.py line 117 1395871] Train: [13/100][18/1462] Data 0.001 (0.001) Batch 2.850 (2.957) Remain 105:39:35 loss: 0.4024 Lr: 0.00200
[2025-09-06 12:55:30,464 INFO misc.py line 117 1395871] Train: [13/100][19/1462] Data 0.001 (0.001) Batch 2.718 (2.942) Remain 105:07:31 loss: 0.3851 Lr: 0.00200
[2025-09-06 12:55:33,595 INFO misc.py line 117 1395871] Train: [13/100][20/1462] Data 0.001 (0.001) Batch 3.131 (2.953) Remain 105:31:15 loss: 0.5888 Lr: 0.00200
[2025-09-06 12:55:36,504 INFO misc.py line 117 1395871] Train: [13/100][21/1462] Data 0.002 (0.001) Batch 2.909 (2.951) Remain 105:25:56 loss: 0.5331 Lr: 0.00200
[2025-09-06 12:55:39,441 INFO misc.py line 117 1395871] Train: [13/100][22/1462] Data 0.001 (0.001) Batch 2.937 (2.950) Remain 105:24:24 loss: 0.3096 Lr: 0.00200
[2025-09-06 12:55:42,296 INFO misc.py line 117 1395871] Train: [13/100][23/1462] Data 0.001 (0.001) Batch 2.855 (2.945) Remain 105:14:08 loss: 0.3287 Lr: 0.00200
[2025-09-06 12:55:45,169 INFO misc.py line 117 1395871] Train: [13/100][24/1462] Data 0.001 (0.001) Batch 2.873 (2.942) Remain 105:06:45 loss: 0.3085 Lr: 0.00200
[2025-09-06 12:55:48,002 INFO misc.py line 117 1395871] Train: [13/100][25/1462] Data 0.002 (0.001) Batch 2.833 (2.937) Remain 104:56:06 loss: 0.3749 Lr: 0.00200
[2025-09-06 12:55:51,058 INFO misc.py line 117 1395871] Train: [13/100][26/1462] Data 0.001 (0.001) Batch 3.055 (2.942) Remain 105:07:06 loss: 0.4166 Lr: 0.00200
[2025-09-06 12:55:54,113 INFO misc.py line 117 1395871] Train: [13/100][27/1462] Data 0.002 (0.001) Batch 3.055 (2.947) Remain 105:17:09 loss: 0.5022 Lr: 0.00200
[2025-09-06 12:55:56,984 INFO misc.py line 117 1395871] Train: [13/100][28/1462] Data 0.002 (0.001) Batch 2.871 (2.944) Remain 105:10:39 loss: 0.4228 Lr: 0.00200
[2025-09-06 12:55:59,619 INFO misc.py line 117 1395871] Train: [13/100][29/1462] Data 0.002 (0.001) Batch 2.635 (2.932) Remain 104:45:08 loss: 0.6356 Lr: 0.00200
[2025-09-06 12:56:02,642 INFO misc.py line 117 1395871] Train: [13/100][30/1462] Data 0.002 (0.001) Batch 3.024 (2.935) Remain 104:52:22 loss: 0.5628 Lr: 0.00200
[2025-09-06 12:56:05,460 INFO misc.py line 117 1395871] Train: [13/100][31/1462] Data 0.002 (0.001) Batch 2.817 (2.931) Remain 104:43:18 loss: 0.4487 Lr: 0.00200
[2025-09-06 12:56:08,528 INFO misc.py line 117 1395871] Train: [13/100][32/1462] Data 0.001 (0.001) Batch 3.068 (2.936) Remain 104:53:25 loss: 0.3054 Lr: 0.00200
[2025-09-06 12:56:11,304 INFO misc.py line 117 1395871] Train: [13/100][33/1462] Data 0.002 (0.001) Batch 2.776 (2.930) Remain 104:41:56 loss: 0.2917 Lr: 0.00200
[2025-09-06 12:56:14,856 INFO misc.py line 117 1395871] Train: [13/100][34/1462] Data 0.002 (0.001) Batch 3.552 (2.950) Remain 105:24:50 loss: 0.4771 Lr: 0.00200
[2025-09-06 12:56:17,685 INFO misc.py line 117 1395871] Train: [13/100][35/1462] Data 0.001 (0.001) Batch 2.829 (2.947) Remain 105:16:40 loss: 0.5751 Lr: 0.00200
[2025-09-06 12:56:20,768 INFO misc.py line 117 1395871] Train: [13/100][36/1462] Data 0.001 (0.001) Batch 3.083 (2.951) Remain 105:25:28 loss: 0.2599 Lr: 0.00200
[2025-09-06 12:56:23,761 INFO misc.py line 117 1395871] Train: [13/100][37/1462] Data 0.002 (0.001) Batch 2.993 (2.952) Remain 105:28:06 loss: 0.5582 Lr: 0.00200
[2025-09-06 12:56:26,334 INFO misc.py line 117 1395871] Train: [13/100][38/1462] Data 0.002 (0.001) Batch 2.573 (2.941) Remain 105:04:49 loss: 0.9891 Lr: 0.00200
[2025-09-06 12:56:28,924 INFO misc.py line 117 1395871] Train: [13/100][39/1462] Data 0.002 (0.001) Batch 2.590 (2.931) Remain 104:43:51 loss: 0.3784 Lr: 0.00200
[2025-09-06 12:56:32,196 INFO misc.py line 117 1395871] Train: [13/100][40/1462] Data 0.002 (0.001) Batch 3.273 (2.941) Remain 105:03:35 loss: 0.3701 Lr: 0.00200
[2025-09-06 12:56:34,972 INFO misc.py line 117 1395871] Train: [13/100][41/1462] Data 0.001 (0.001) Batch 2.775 (2.936) Remain 104:54:12 loss: 0.4969 Lr: 0.00200
[2025-09-06 12:56:38,030 INFO misc.py line 117 1395871] Train: [13/100][42/1462] Data 0.002 (0.001) Batch 3.058 (2.939) Remain 105:00:50 loss: 0.6166 Lr: 0.00200
[2025-09-06 12:56:40,488 INFO misc.py line 117 1395871] Train: [13/100][43/1462] Data 0.002 (0.001) Batch 2.459 (2.927) Remain 104:35:01 loss: 0.4224 Lr: 0.00200
[2025-09-06 12:56:43,077 INFO misc.py line 117 1395871] Train: [13/100][44/1462] Data 0.001 (0.001) Batch 2.589 (2.919) Remain 104:17:16 loss: 0.4471 Lr: 0.00200
[2025-09-06 12:56:46,170 INFO misc.py line 117 1395871] Train: [13/100][45/1462] Data 0.002 (0.001) Batch 3.093 (2.923) Remain 104:26:06 loss: 0.4433 Lr: 0.00200
[2025-09-06 12:56:48,747 INFO misc.py line 117 1395871] Train: [13/100][46/1462] Data 0.001 (0.001) Batch 2.576 (2.915) Remain 104:08:46 loss: 0.3555 Lr: 0.00200
[2025-09-06 12:56:51,927 INFO misc.py line 117 1395871] Train: [13/100][47/1462] Data 0.002 (0.001) Batch 3.181 (2.921) Remain 104:21:38 loss: 0.6089 Lr: 0.00200
[2025-09-06 12:56:54,856 INFO misc.py line 117 1395871] Train: [13/100][48/1462] Data 0.002 (0.001) Batch 2.929 (2.921) Remain 104:21:58 loss: 0.2829 Lr: 0.00200
[2025-09-06 12:56:57,319 INFO misc.py line 117 1395871] Train: [13/100][49/1462] Data 0.002 (0.001) Batch 2.462 (2.911) Remain 104:00:32 loss: 0.1763 Lr: 0.00200
[2025-09-06 12:57:00,095 INFO misc.py line 117 1395871] Train: [13/100][50/1462] Data 0.001 (0.001) Batch 2.776 (2.909) Remain 103:54:18 loss: 0.5276 Lr: 0.00200
[2025-09-06 12:57:03,106 INFO misc.py line 117 1395871] Train: [13/100][51/1462] Data 0.002 (0.001) Batch 3.011 (2.911) Remain 103:58:50 loss: 0.3663 Lr: 0.00200
[2025-09-06 12:57:06,079 INFO misc.py line 117 1395871] Train: [13/100][52/1462] Data 0.001 (0.001) Batch 2.974 (2.912) Remain 104:01:33 loss: 0.3060 Lr: 0.00200
[2025-09-06 12:57:08,795 INFO misc.py line 117 1395871] Train: [13/100][53/1462] Data 0.002 (0.001) Batch 2.716 (2.908) Remain 103:53:05 loss: 0.3975 Lr: 0.00200
[2025-09-06 12:57:11,967 INFO misc.py line 117 1395871] Train: [13/100][54/1462] Data 0.001 (0.001) Batch 3.171 (2.913) Remain 104:04:06 loss: 0.2377 Lr: 0.00200
[2025-09-06 12:57:14,808 INFO misc.py line 117 1395871] Train: [13/100][55/1462] Data 0.001 (0.001) Batch 2.841 (2.912) Remain 104:01:05 loss: 0.3464 Lr: 0.00200
[2025-09-06 12:57:17,546 INFO misc.py line 117 1395871] Train: [13/100][56/1462] Data 0.001 (0.001) Batch 2.739 (2.909) Remain 103:54:02 loss: 0.4962 Lr: 0.00200
[2025-09-06 12:57:20,274 INFO misc.py line 117 1395871] Train: [13/100][57/1462] Data 0.001 (0.001) Batch 2.727 (2.905) Remain 103:46:47 loss: 0.5035 Lr: 0.00200
[2025-09-06 12:57:23,393 INFO misc.py line 117 1395871] Train: [13/100][58/1462] Data 0.001 (0.001) Batch 3.120 (2.909) Remain 103:55:05 loss: 0.5179 Lr: 0.00200
[2025-09-06 12:57:26,304 INFO misc.py line 117 1395871] Train: [13/100][59/1462] Data 0.001 (0.001) Batch 2.911 (2.909) Remain 103:55:06 loss: 0.2906 Lr: 0.00200
[2025-09-06 12:57:29,213 INFO misc.py line 117 1395871] Train: [13/100][60/1462] Data 0.001 (0.001) Batch 2.909 (2.909) Remain 103:55:03 loss: 0.6161 Lr: 0.00200
[2025-09-06 12:57:32,058 INFO misc.py line 117 1395871] Train: [13/100][61/1462] Data 0.001 (0.001) Batch 2.845 (2.908) Remain 103:52:38 loss: 0.3277 Lr: 0.00200
[2025-09-06 12:57:35,276 INFO misc.py line 117 1395871] Train: [13/100][62/1462] Data 0.002 (0.001) Batch 3.217 (2.913) Remain 104:03:50 loss: 0.4091 Lr: 0.00200
[2025-09-06 12:57:38,589 INFO misc.py line 117 1395871] Train: [13/100][63/1462] Data 0.002 (0.001) Batch 3.313 (2.920) Remain 104:18:04 loss: 0.5767 Lr: 0.00200
[2025-09-06 12:57:41,197 INFO misc.py line 117 1395871] Train: [13/100][64/1462] Data 0.002 (0.001) Batch 2.607 (2.915) Remain 104:07:03 loss: 0.4545 Lr: 0.00200
[2025-09-06 12:57:44,214 INFO misc.py line 117 1395871] Train: [13/100][65/1462] Data 0.002 (0.001) Batch 3.017 (2.916) Remain 104:10:32 loss: 0.4992 Lr: 0.00200
[2025-09-06 12:57:46,785 INFO misc.py line 117 1395871] Train: [13/100][66/1462] Data 0.001 (0.001) Batch 2.571 (2.911) Remain 103:58:44 loss: 0.6250 Lr: 0.00200
[2025-09-06 12:57:49,538 INFO misc.py line 117 1395871] Train: [13/100][67/1462] Data 0.001 (0.001) Batch 2.753 (2.909) Remain 103:53:23 loss: 0.7574 Lr: 0.00200
[2025-09-06 12:57:52,238 INFO misc.py line 117 1395871] Train: [13/100][68/1462] Data 0.001 (0.001) Batch 2.701 (2.905) Remain 103:46:30 loss: 0.5754 Lr: 0.00200
[2025-09-06 12:57:55,304 INFO misc.py line 117 1395871] Train: [13/100][69/1462] Data 0.001 (0.001) Batch 3.066 (2.908) Remain 103:51:39 loss: 0.3611 Lr: 0.00200
[2025-09-06 12:57:58,124 INFO misc.py line 117 1395871] Train: [13/100][70/1462] Data 0.002 (0.001) Batch 2.820 (2.906) Remain 103:48:47 loss: 0.4986 Lr: 0.00200
[2025-09-06 12:58:01,023 INFO misc.py line 117 1395871] Train: [13/100][71/1462] Data 0.002 (0.001) Batch 2.899 (2.906) Remain 103:48:30 loss: 0.2597 Lr: 0.00200
[2025-09-06 12:58:03,896 INFO misc.py line 117 1395871] Train: [13/100][72/1462] Data 0.002 (0.001) Batch 2.873 (2.906) Remain 103:47:25 loss: 0.4871 Lr: 0.00200
[2025-09-06 12:58:06,512 INFO misc.py line 117 1395871] Train: [13/100][73/1462] Data 0.002 (0.001) Batch 2.617 (2.902) Remain 103:38:31 loss: 0.4429 Lr: 0.00200
[2025-09-06 12:58:09,334 INFO misc.py line 117 1395871] Train: [13/100][74/1462] Data 0.002 (0.001) Batch 2.822 (2.901) Remain 103:36:03 loss: 0.5048 Lr: 0.00200
[2025-09-06 12:58:12,134 INFO misc.py line 117 1395871] Train: [13/100][75/1462] Data 0.002 (0.001) Batch 2.800 (2.899) Remain 103:33:01 loss: 0.2349 Lr: 0.00200
[2025-09-06 12:58:14,952 INFO misc.py line 117 1395871] Train: [13/100][76/1462] Data 0.002 (0.001) Batch 2.817 (2.898) Remain 103:30:34 loss: 0.3383 Lr: 0.00200
[2025-09-06 12:58:17,933 INFO misc.py line 117 1395871] Train: [13/100][77/1462] Data 0.002 (0.001) Batch 2.981 (2.899) Remain 103:32:55 loss: 0.3558 Lr: 0.00200
[2025-09-06 12:58:21,253 INFO misc.py line 117 1395871] Train: [13/100][78/1462] Data 0.002 (0.001) Batch 3.320 (2.905) Remain 103:44:53 loss: 0.4349 Lr: 0.00200
[2025-09-06 12:58:24,042 INFO misc.py line 117 1395871] Train: [13/100][79/1462] Data 0.002 (0.001) Batch 2.790 (2.903) Remain 103:41:35 loss: 0.3858 Lr: 0.00200
[2025-09-06 12:58:26,783 INFO misc.py line 117 1395871] Train: [13/100][80/1462] Data 0.002 (0.001) Batch 2.741 (2.901) Remain 103:37:01 loss: 0.2549 Lr: 0.00200
[2025-09-06 12:58:29,688 INFO misc.py line 117 1395871] Train: [13/100][81/1462] Data 0.002 (0.002) Batch 2.906 (2.901) Remain 103:37:05 loss: 0.3819 Lr: 0.00200
[2025-09-06 12:58:32,550 INFO misc.py line 117 1395871] Train: [13/100][82/1462] Data 0.002 (0.002) Batch 2.862 (2.901) Remain 103:35:58 loss: 0.3158 Lr: 0.00200
[2025-09-06 12:58:35,419 INFO misc.py line 117 1395871] Train: [13/100][83/1462] Data 0.002 (0.002) Batch 2.869 (2.900) Remain 103:35:04 loss: 0.7845 Lr: 0.00200
[2025-09-06 12:58:38,189 INFO misc.py line 117 1395871] Train: [13/100][84/1462] Data 0.001 (0.002) Batch 2.770 (2.899) Remain 103:31:34 loss: 0.3469 Lr: 0.00200
[2025-09-06 12:58:40,954 INFO misc.py line 117 1395871] Train: [13/100][85/1462] Data 0.002 (0.002) Batch 2.766 (2.897) Remain 103:28:02 loss: 0.6097 Lr: 0.00200
[2025-09-06 12:58:43,759 INFO misc.py line 117 1395871] Train: [13/100][86/1462] Data 0.002 (0.002) Batch 2.805 (2.896) Remain 103:25:36 loss: 0.3373 Lr: 0.00200
[2025-09-06 12:58:46,692 INFO misc.py line 117 1395871] Train: [13/100][87/1462] Data 0.002 (0.002) Batch 2.933 (2.896) Remain 103:26:30 loss: 0.2839 Lr: 0.00200
[2025-09-06 12:58:49,986 INFO misc.py line 117 1395871] Train: [13/100][88/1462] Data 0.002 (0.002) Batch 3.294 (2.901) Remain 103:36:29 loss: 0.5068 Lr: 0.00200
[2025-09-06 12:58:52,928 INFO misc.py line 117 1395871] Train: [13/100][89/1462] Data 0.002 (0.002) Batch 2.942 (2.902) Remain 103:37:27 loss: 0.5662 Lr: 0.00200
[2025-09-06 12:58:55,882 INFO misc.py line 117 1395871] Train: [13/100][90/1462] Data 0.001 (0.002) Batch 2.954 (2.902) Remain 103:38:41 loss: 0.2436 Lr: 0.00200
[2025-09-06 12:58:58,675 INFO misc.py line 117 1395871] Train: [13/100][91/1462] Data 0.001 (0.002) Batch 2.793 (2.901) Remain 103:35:58 loss: 0.7963 Lr: 0.00200
[2025-09-06 12:59:01,744 INFO misc.py line 117 1395871] Train: [13/100][92/1462] Data 0.001 (0.002) Batch 3.070 (2.903) Remain 103:39:59 loss: 0.8848 Lr: 0.00200
[2025-09-06 12:59:04,641 INFO misc.py line 117 1395871] Train: [13/100][93/1462] Data 0.001 (0.002) Batch 2.897 (2.903) Remain 103:39:47 loss: 0.3342 Lr: 0.00200
[2025-09-06 12:59:07,609 INFO misc.py line 117 1395871] Train: [13/100][94/1462] Data 0.002 (0.002) Batch 2.968 (2.903) Remain 103:41:17 loss: 0.4597 Lr: 0.00200
[2025-09-06 12:59:10,398 INFO misc.py line 117 1395871] Train: [13/100][95/1462] Data 0.001 (0.002) Batch 2.789 (2.902) Remain 103:38:34 loss: 0.3746 Lr: 0.00200
[2025-09-06 12:59:13,400 INFO misc.py line 117 1395871] Train: [13/100][96/1462] Data 0.002 (0.002) Batch 3.002 (2.903) Remain 103:40:49 loss: 0.6438 Lr: 0.00200
[2025-09-06 12:59:16,628 INFO misc.py line 117 1395871] Train: [13/100][97/1462] Data 0.002 (0.002) Batch 3.228 (2.907) Remain 103:48:09 loss: 0.5277 Lr: 0.00200
[2025-09-06 12:59:19,312 INFO misc.py line 117 1395871] Train: [13/100][98/1462] Data 0.001 (0.002) Batch 2.684 (2.904) Remain 103:43:05 loss: 0.3183 Lr: 0.00200
[2025-09-06 12:59:22,270 INFO misc.py line 117 1395871] Train: [13/100][99/1462] Data 0.001 (0.002) Batch 2.958 (2.905) Remain 103:44:14 loss: 0.4061 Lr: 0.00200
[2025-09-06 12:59:25,344 INFO misc.py line 117 1395871] Train: [13/100][100/1462] Data 0.001 (0.002) Batch 3.075 (2.907) Remain 103:47:56 loss: 0.3791 Lr: 0.00200
[2025-09-06 12:59:28,421 INFO misc.py line 117 1395871] Train: [13/100][101/1462] Data 0.001 (0.002) Batch 3.076 (2.908) Remain 103:51:36 loss: 0.5623 Lr: 0.00200
[2025-09-06 12:59:31,501 INFO misc.py line 117 1395871] Train: [13/100][102/1462] Data 0.001 (0.002) Batch 3.080 (2.910) Remain 103:55:16 loss: 0.4890 Lr: 0.00200
[2025-09-06 12:59:34,348 INFO misc.py line 117 1395871] Train: [13/100][103/1462] Data 0.001 (0.002) Batch 2.847 (2.910) Remain 103:53:52 loss: 0.3518 Lr: 0.00200
[2025-09-06 12:59:37,336 INFO misc.py line 117 1395871] Train: [13/100][104/1462] Data 0.001 (0.002) Batch 2.988 (2.910) Remain 103:55:29 loss: 0.6059 Lr: 0.00200
[2025-09-06 12:59:40,346 INFO misc.py line 117 1395871] Train: [13/100][105/1462] Data 0.001 (0.002) Batch 3.009 (2.911) Remain 103:57:31 loss: 0.3050 Lr: 0.00200
[2025-09-06 12:59:43,143 INFO misc.py line 117 1395871] Train: [13/100][106/1462] Data 0.001 (0.002) Batch 2.797 (2.910) Remain 103:55:06 loss: 1.0950 Lr: 0.00200
[2025-09-06 12:59:46,352 INFO misc.py line 117 1395871] Train: [13/100][107/1462] Data 0.001 (0.002) Batch 3.209 (2.913) Remain 104:01:12 loss: 0.5668 Lr: 0.00200
[2025-09-06 12:59:49,162 INFO misc.py line 117 1395871] Train: [13/100][108/1462] Data 0.001 (0.002) Batch 2.811 (2.912) Remain 103:59:03 loss: 0.3240 Lr: 0.00200
[2025-09-06 12:59:52,075 INFO misc.py line 117 1395871] Train: [13/100][109/1462] Data 0.001 (0.002) Batch 2.913 (2.912) Remain 103:59:02 loss: 0.6127 Lr: 0.00200
[2025-09-06 12:59:55,090 INFO misc.py line 117 1395871] Train: [13/100][110/1462] Data 0.001 (0.002) Batch 3.015 (2.913) Remain 104:01:03 loss: 0.4878 Lr: 0.00200
[2025-09-06 12:59:58,164 INFO misc.py line 117 1395871] Train: [13/100][111/1462] Data 0.001 (0.002) Batch 3.074 (2.915) Remain 104:04:11 loss: 0.6031 Lr: 0.00200
[2025-09-06 13:00:01,018 INFO misc.py line 117 1395871] Train: [13/100][112/1462] Data 0.001 (0.001) Batch 2.854 (2.914) Remain 104:02:56 loss: 0.4047 Lr: 0.00200
[2025-09-06 13:00:04,007 INFO misc.py line 117 1395871] Train: [13/100][113/1462] Data 0.002 (0.001) Batch 2.989 (2.915) Remain 104:04:21 loss: 0.7508 Lr: 0.00200
[2025-09-06 13:00:06,899 INFO misc.py line 117 1395871] Train: [13/100][114/1462] Data 0.002 (0.002) Batch 2.892 (2.914) Remain 104:03:52 loss: 0.3631 Lr: 0.00200
[2025-09-06 13:00:09,757 INFO misc.py line 117 1395871] Train: [13/100][115/1462] Data 0.001 (0.001) Batch 2.858 (2.914) Remain 104:02:44 loss: 0.5390 Lr: 0.00200
[2025-09-06 13:00:12,664 INFO misc.py line 117 1395871] Train: [13/100][116/1462] Data 0.002 (0.002) Batch 2.908 (2.914) Remain 104:02:34 loss: 0.4518 Lr: 0.00200
[2025-09-06 13:00:15,573 INFO misc.py line 117 1395871] Train: [13/100][117/1462] Data 0.002 (0.002) Batch 2.909 (2.914) Remain 104:02:25 loss: 0.7748 Lr: 0.00200
[2025-09-06 13:00:18,703 INFO misc.py line 117 1395871] Train: [13/100][118/1462] Data 0.002 (0.002) Batch 3.129 (2.916) Remain 104:06:23 loss: 0.4012 Lr: 0.00200
[2025-09-06 13:00:21,449 INFO misc.py line 117 1395871] Train: [13/100][119/1462] Data 0.001 (0.002) Batch 2.747 (2.914) Remain 104:03:13 loss: 0.6003 Lr: 0.00200
[2025-09-06 13:00:24,618 INFO misc.py line 117 1395871] Train: [13/100][120/1462] Data 0.001 (0.002) Batch 3.169 (2.916) Remain 104:07:50 loss: 0.4159 Lr: 0.00200
[2025-09-06 13:00:27,432 INFO misc.py line 117 1395871] Train: [13/100][121/1462] Data 0.002 (0.002) Batch 2.813 (2.916) Remain 104:05:54 loss: 0.6220 Lr: 0.00200
[2025-09-06 13:00:30,286 INFO misc.py line 117 1395871] Train: [13/100][122/1462] Data 0.001 (0.002) Batch 2.854 (2.915) Remain 104:04:45 loss: 0.4169 Lr: 0.00200
[2025-09-06 13:00:33,669 INFO misc.py line 117 1395871] Train: [13/100][123/1462] Data 0.001 (0.001) Batch 3.383 (2.919) Remain 104:13:04 loss: 0.3751 Lr: 0.00200
[2025-09-06 13:00:36,325 INFO misc.py line 117 1395871] Train: [13/100][124/1462] Data 0.001 (0.001) Batch 2.656 (2.917) Remain 104:08:21 loss: 0.6313 Lr: 0.00200
[2025-09-06 13:00:39,608 INFO misc.py line 117 1395871] Train: [13/100][125/1462] Data 0.002 (0.001) Batch 3.283 (2.920) Remain 104:14:44 loss: 0.5450 Lr: 0.00200
[2025-09-06 13:00:42,297 INFO misc.py line 117 1395871] Train: [13/100][126/1462] Data 0.001 (0.001) Batch 2.689 (2.918) Remain 104:10:40 loss: 0.3323 Lr: 0.00200
[2025-09-06 13:00:45,345 INFO misc.py line 117 1395871] Train: [13/100][127/1462] Data 0.001 (0.001) Batch 3.048 (2.919) Remain 104:12:52 loss: 0.3292 Lr: 0.00200
[2025-09-06 13:00:48,318 INFO misc.py line 117 1395871] Train: [13/100][128/1462] Data 0.001 (0.001) Batch 2.973 (2.919) Remain 104:13:44 loss: 0.5811 Lr: 0.00200
[2025-09-06 13:00:51,011 INFO misc.py line 117 1395871] Train: [13/100][129/1462] Data 0.002 (0.001) Batch 2.693 (2.918) Remain 104:09:51 loss: 0.4289 Lr: 0.00200
[2025-09-06 13:00:53,928 INFO misc.py line 117 1395871] Train: [13/100][130/1462] Data 0.001 (0.001) Batch 2.917 (2.918) Remain 104:09:48 loss: 0.2953 Lr: 0.00200
[2025-09-06 13:00:56,653 INFO misc.py line 117 1395871] Train: [13/100][131/1462] Data 0.001 (0.001) Batch 2.725 (2.916) Remain 104:06:31 loss: 0.2827 Lr: 0.00200
[2025-09-06 13:00:59,594 INFO misc.py line 117 1395871] Train: [13/100][132/1462] Data 0.001 (0.001) Batch 2.940 (2.916) Remain 104:06:53 loss: 0.3100 Lr: 0.00200
[2025-09-06 13:01:02,815 INFO misc.py line 117 1395871] Train: [13/100][133/1462] Data 0.002 (0.001) Batch 3.222 (2.919) Remain 104:11:52 loss: 0.5647 Lr: 0.00200
[2025-09-06 13:01:06,041 INFO misc.py line 117 1395871] Train: [13/100][134/1462] Data 0.002 (0.001) Batch 3.226 (2.921) Remain 104:16:50 loss: 0.3248 Lr: 0.00200
[2025-09-06 13:01:08,834 INFO misc.py line 117 1395871] Train: [13/100][135/1462] Data 0.001 (0.001) Batch 2.793 (2.920) Remain 104:14:43 loss: 0.4571 Lr: 0.00200
[2025-09-06 13:01:11,661 INFO misc.py line 117 1395871] Train: [13/100][136/1462] Data 0.001 (0.001) Batch 2.827 (2.919) Remain 104:13:09 loss: 0.7524 Lr: 0.00200
[2025-09-06 13:01:14,292 INFO misc.py line 117 1395871] Train: [13/100][137/1462] Data 0.001 (0.001) Batch 2.632 (2.917) Remain 104:08:30 loss: 0.2756 Lr: 0.00200
[2025-09-06 13:01:16,998 INFO misc.py line 117 1395871] Train: [13/100][138/1462] Data 0.001 (0.001) Batch 2.706 (2.916) Remain 104:05:06 loss: 0.3166 Lr: 0.00200
[2025-09-06 13:01:19,712 INFO misc.py line 117 1395871] Train: [13/100][139/1462] Data 0.001 (0.001) Batch 2.714 (2.914) Remain 104:01:53 loss: 0.2730 Lr: 0.00200
[2025-09-06 13:01:22,329 INFO misc.py line 117 1395871] Train: [13/100][140/1462] Data 0.001 (0.001) Batch 2.617 (2.912) Remain 103:57:11 loss: 0.3318 Lr: 0.00200
[2025-09-06 13:01:25,510 INFO misc.py line 117 1395871] Train: [13/100][141/1462] Data 0.001 (0.001) Batch 3.181 (2.914) Remain 104:01:19 loss: 0.4791 Lr: 0.00200
[2025-09-06 13:01:28,304 INFO misc.py line 117 1395871] Train: [13/100][142/1462] Data 0.001 (0.001) Batch 2.794 (2.913) Remain 103:59:25 loss: 0.1911 Lr: 0.00200
[2025-09-06 13:01:30,865 INFO misc.py line 117 1395871] Train: [13/100][143/1462] Data 0.001 (0.001) Batch 2.562 (2.911) Remain 103:53:59 loss: 0.3030 Lr: 0.00200
[2025-09-06 13:01:33,756 INFO misc.py line 117 1395871] Train: [13/100][144/1462] Data 0.001 (0.001) Batch 2.890 (2.910) Remain 103:53:38 loss: 0.6573 Lr: 0.00200
[2025-09-06 13:01:36,528 INFO misc.py line 117 1395871] Train: [13/100][145/1462] Data 0.001 (0.001) Batch 2.772 (2.909) Remain 103:51:30 loss: 0.6393 Lr: 0.00200
[2025-09-06 13:01:39,622 INFO misc.py line 117 1395871] Train: [13/100][146/1462] Data 0.002 (0.001) Batch 3.094 (2.911) Remain 103:54:14 loss: 0.6431 Lr: 0.00200
[2025-09-06 13:01:42,640 INFO misc.py line 117 1395871] Train: [13/100][147/1462] Data 0.001 (0.001) Batch 3.017 (2.911) Remain 103:55:46 loss: 0.2998 Lr: 0.00200
[2025-09-06 13:01:45,581 INFO misc.py line 117 1395871] Train: [13/100][148/1462] Data 0.001 (0.001) Batch 2.941 (2.912) Remain 103:56:10 loss: 0.3771 Lr: 0.00200
[2025-09-06 13:01:48,245 INFO misc.py line 117 1395871] Train: [13/100][149/1462] Data 0.001 (0.001) Batch 2.664 (2.910) Remain 103:52:29 loss: 0.4947 Lr: 0.00200
[2025-09-06 13:01:51,464 INFO misc.py line 117 1395871] Train: [13/100][150/1462] Data 0.001 (0.001) Batch 3.219 (2.912) Remain 103:56:56 loss: 0.4690 Lr: 0.00200
[2025-09-06 13:01:54,359 INFO misc.py line 117 1395871] Train: [13/100][151/1462] Data 0.002 (0.001) Batch 2.895 (2.912) Remain 103:56:38 loss: 0.4371 Lr: 0.00200
[2025-09-06 13:01:57,184 INFO misc.py line 117 1395871] Train: [13/100][152/1462] Data 0.001 (0.001) Batch 2.825 (2.911) Remain 103:55:20 loss: 0.2861 Lr: 0.00200
[2025-09-06 13:01:59,917 INFO misc.py line 117 1395871] Train: [13/100][153/1462] Data 0.002 (0.001) Batch 2.733 (2.910) Remain 103:52:44 loss: 0.4299 Lr: 0.00200
[2025-09-06 13:02:02,751 INFO misc.py line 117 1395871] Train: [13/100][154/1462] Data 0.001 (0.001) Batch 2.834 (2.910) Remain 103:51:36 loss: 0.3525 Lr: 0.00200
[2025-09-06 13:02:05,396 INFO misc.py line 117 1395871] Train: [13/100][155/1462] Data 0.002 (0.001) Batch 2.645 (2.908) Remain 103:47:50 loss: 0.3521 Lr: 0.00200
[2025-09-06 13:02:08,298 INFO misc.py line 117 1395871] Train: [13/100][156/1462] Data 0.001 (0.001) Batch 2.902 (2.908) Remain 103:47:42 loss: 0.4852 Lr: 0.00200
[2025-09-06 13:02:11,319 INFO misc.py line 117 1395871] Train: [13/100][157/1462] Data 0.001 (0.001) Batch 3.021 (2.909) Remain 103:49:13 loss: 0.5041 Lr: 0.00200
[2025-09-06 13:02:14,109 INFO misc.py line 117 1395871] Train: [13/100][158/1462] Data 0.001 (0.001) Batch 2.790 (2.908) Remain 103:47:32 loss: 0.5990 Lr: 0.00200
[2025-09-06 13:02:17,165 INFO misc.py line 117 1395871] Train: [13/100][159/1462] Data 0.002 (0.001) Batch 3.056 (2.909) Remain 103:49:31 loss: 0.2797 Lr: 0.00200
[2025-09-06 13:02:19,979 INFO misc.py line 117 1395871] Train: [13/100][160/1462] Data 0.001 (0.001) Batch 2.815 (2.908) Remain 103:48:11 loss: 0.4505 Lr: 0.00200
[2025-09-06 13:02:23,626 INFO misc.py line 117 1395871] Train: [13/100][161/1462] Data 0.001 (0.001) Batch 3.646 (2.913) Remain 103:58:08 loss: 0.7241 Lr: 0.00200
[2025-09-06 13:02:26,395 INFO misc.py line 117 1395871] Train: [13/100][162/1462] Data 0.002 (0.001) Batch 2.770 (2.912) Remain 103:56:10 loss: 0.6817 Lr: 0.00200
[2025-09-06 13:02:29,201 INFO misc.py line 117 1395871] Train: [13/100][163/1462] Data 0.002 (0.001) Batch 2.806 (2.911) Remain 103:54:42 loss: 0.4634 Lr: 0.00200
[2025-09-06 13:02:32,188 INFO misc.py line 117 1395871] Train: [13/100][164/1462] Data 0.001 (0.001) Batch 2.987 (2.912) Remain 103:55:39 loss: 0.4029 Lr: 0.00200
[2025-09-06 13:02:35,074 INFO misc.py line 117 1395871] Train: [13/100][165/1462] Data 0.002 (0.001) Batch 2.886 (2.912) Remain 103:55:16 loss: 0.7299 Lr: 0.00200
[2025-09-06 13:02:37,904 INFO misc.py line 117 1395871] Train: [13/100][166/1462] Data 0.001 (0.001) Batch 2.829 (2.911) Remain 103:54:08 loss: 0.3120 Lr: 0.00200
[2025-09-06 13:02:40,530 INFO misc.py line 117 1395871] Train: [13/100][167/1462] Data 0.002 (0.001) Batch 2.626 (2.909) Remain 103:50:22 loss: 0.5847 Lr: 0.00200
[2025-09-06 13:02:43,085 INFO misc.py line 117 1395871] Train: [13/100][168/1462] Data 0.001 (0.001) Batch 2.555 (2.907) Remain 103:45:43 loss: 0.4143 Lr: 0.00200
[2025-09-06 13:02:45,819 INFO misc.py line 117 1395871] Train: [13/100][169/1462] Data 0.002 (0.001) Batch 2.734 (2.906) Remain 103:43:26 loss: 0.2241 Lr: 0.00200
[2025-09-06 13:02:49,006 INFO misc.py line 117 1395871] Train: [13/100][170/1462] Data 0.002 (0.001) Batch 3.187 (2.908) Remain 103:46:59 loss: 0.2705 Lr: 0.00200
[2025-09-06 13:02:51,698 INFO misc.py line 117 1395871] Train: [13/100][171/1462] Data 0.001 (0.001) Batch 2.692 (2.907) Remain 103:44:11 loss: 0.3104 Lr: 0.00200
[2025-09-06 13:02:54,641 INFO misc.py line 117 1395871] Train: [13/100][172/1462] Data 0.001 (0.001) Batch 2.943 (2.907) Remain 103:44:36 loss: 0.4770 Lr: 0.00200
[2025-09-06 13:02:57,524 INFO misc.py line 117 1395871] Train: [13/100][173/1462] Data 0.002 (0.001) Batch 2.884 (2.907) Remain 103:44:16 loss: 0.4846 Lr: 0.00200
[2025-09-06 13:03:00,160 INFO misc.py line 117 1395871] Train: [13/100][174/1462] Data 0.002 (0.001) Batch 2.636 (2.905) Remain 103:40:49 loss: 0.6675 Lr: 0.00200
[2025-09-06 13:03:03,205 INFO misc.py line 117 1395871] Train: [13/100][175/1462] Data 0.002 (0.001) Batch 3.045 (2.906) Remain 103:42:30 loss: 0.5564 Lr: 0.00200
[2025-09-06 13:03:06,057 INFO misc.py line 117 1395871] Train: [13/100][176/1462] Data 0.001 (0.001) Batch 2.852 (2.906) Remain 103:41:48 loss: 0.6195 Lr: 0.00200
[2025-09-06 13:03:08,942 INFO misc.py line 117 1395871] Train: [13/100][177/1462] Data 0.001 (0.001) Batch 2.885 (2.905) Remain 103:41:30 loss: 0.4369 Lr: 0.00200
[2025-09-06 13:03:11,812 INFO misc.py line 117 1395871] Train: [13/100][178/1462] Data 0.002 (0.001) Batch 2.870 (2.905) Remain 103:41:01 loss: 0.4295 Lr: 0.00200
[2025-09-06 13:03:14,640 INFO misc.py line 117 1395871] Train: [13/100][179/1462] Data 0.002 (0.001) Batch 2.828 (2.905) Remain 103:40:01 loss: 0.4653 Lr: 0.00200
[2025-09-06 13:03:17,297 INFO misc.py line 117 1395871] Train: [13/100][180/1462] Data 0.001 (0.001) Batch 2.657 (2.903) Remain 103:36:58 loss: 0.3723 Lr: 0.00200
[2025-09-06 13:03:20,293 INFO misc.py line 117 1395871] Train: [13/100][181/1462] Data 0.002 (0.001) Batch 2.997 (2.904) Remain 103:38:03 loss: 0.3847 Lr: 0.00200
[2025-09-06 13:03:22,979 INFO misc.py line 117 1395871] Train: [13/100][182/1462] Data 0.002 (0.001) Batch 2.686 (2.903) Remain 103:35:23 loss: 0.5691 Lr: 0.00200
[2025-09-06 13:03:25,918 INFO misc.py line 117 1395871] Train: [13/100][183/1462] Data 0.002 (0.001) Batch 2.939 (2.903) Remain 103:35:46 loss: 0.4912 Lr: 0.00200
[2025-09-06 13:03:28,750 INFO misc.py line 117 1395871] Train: [13/100][184/1462] Data 0.001 (0.001) Batch 2.832 (2.903) Remain 103:34:53 loss: 0.5138 Lr: 0.00200
[2025-09-06 13:03:31,403 INFO misc.py line 117 1395871] Train: [13/100][185/1462] Data 0.002 (0.001) Batch 2.653 (2.901) Remain 103:31:54 loss: 0.3310 Lr: 0.00200
[2025-09-06 13:03:34,369 INFO misc.py line 117 1395871] Train: [13/100][186/1462] Data 0.001 (0.001) Batch 2.966 (2.902) Remain 103:32:37 loss: 0.6121 Lr: 0.00200
[2025-09-06 13:03:37,201 INFO misc.py line 117 1395871] Train: [13/100][187/1462] Data 0.002 (0.001) Batch 2.832 (2.901) Remain 103:31:45 loss: 0.3010 Lr: 0.00200
[2025-09-06 13:03:40,267 INFO misc.py line 117 1395871] Train: [13/100][188/1462] Data 0.001 (0.001) Batch 3.066 (2.902) Remain 103:33:37 loss: 0.1529 Lr: 0.00200
[2025-09-06 13:03:42,853 INFO misc.py line 117 1395871] Train: [13/100][189/1462] Data 0.001 (0.001) Batch 2.586 (2.900) Remain 103:29:55 loss: 0.4401 Lr: 0.00200
[2025-09-06 13:03:45,649 INFO misc.py line 117 1395871] Train: [13/100][190/1462] Data 0.001 (0.001) Batch 2.796 (2.900) Remain 103:28:41 loss: 0.2819 Lr: 0.00200
[2025-09-06 13:03:48,454 INFO misc.py line 117 1395871] Train: [13/100][191/1462] Data 0.002 (0.001) Batch 2.805 (2.899) Remain 103:27:33 loss: 0.3072 Lr: 0.00200
[2025-09-06 13:03:51,018 INFO misc.py line 117 1395871] Train: [13/100][192/1462] Data 0.002 (0.001) Batch 2.564 (2.897) Remain 103:23:42 loss: 0.3542 Lr: 0.00200
[2025-09-06 13:03:53,999 INFO misc.py line 117 1395871] Train: [13/100][193/1462] Data 0.002 (0.001) Batch 2.982 (2.898) Remain 103:24:36 loss: 0.2296 Lr: 0.00200
[2025-09-06 13:03:56,746 INFO misc.py line 117 1395871] Train: [13/100][194/1462] Data 0.001 (0.001) Batch 2.746 (2.897) Remain 103:22:52 loss: 0.3190 Lr: 0.00200
[2025-09-06 13:03:59,552 INFO misc.py line 117 1395871] Train: [13/100][195/1462] Data 0.001 (0.001) Batch 2.807 (2.897) Remain 103:21:48 loss: 0.3694 Lr: 0.00200
[2025-09-06 13:04:02,695 INFO misc.py line 117 1395871] Train: [13/100][196/1462] Data 0.001 (0.001) Batch 3.143 (2.898) Remain 103:24:29 loss: 0.3072 Lr: 0.00200
[2025-09-06 13:04:05,465 INFO misc.py line 117 1395871] Train: [13/100][197/1462] Data 0.002 (0.001) Batch 2.769 (2.897) Remain 103:23:01 loss: 0.2887 Lr: 0.00200
[2025-09-06 13:04:08,274 INFO misc.py line 117 1395871] Train: [13/100][198/1462] Data 0.001 (0.001) Batch 2.809 (2.897) Remain 103:22:00 loss: 0.4477 Lr: 0.00200
[2025-09-06 13:04:11,199 INFO misc.py line 117 1395871] Train: [13/100][199/1462] Data 0.001 (0.001) Batch 2.926 (2.897) Remain 103:22:16 loss: 0.2965 Lr: 0.00200
[2025-09-06 13:04:13,797 INFO misc.py line 117 1395871] Train: [13/100][200/1462] Data 0.001 (0.001) Batch 2.598 (2.895) Remain 103:18:58 loss: 0.7472 Lr: 0.00200
[2025-09-06 13:04:16,552 INFO misc.py line 117 1395871] Train: [13/100][201/1462] Data 0.002 (0.001) Batch 2.755 (2.895) Remain 103:17:24 loss: 0.5192 Lr: 0.00200
[2025-09-06 13:04:19,616 INFO misc.py line 117 1395871] Train: [13/100][202/1462] Data 0.002 (0.001) Batch 3.064 (2.896) Remain 103:19:10 loss: 0.4488 Lr: 0.00200
[2025-09-06 13:04:22,325 INFO misc.py line 117 1395871] Train: [13/100][203/1462] Data 0.002 (0.001) Batch 2.709 (2.895) Remain 103:17:08 loss: 0.4168 Lr: 0.00200
[2025-09-06 13:04:24,894 INFO misc.py line 117 1395871] Train: [13/100][204/1462] Data 0.001 (0.001) Batch 2.569 (2.893) Remain 103:13:37 loss: 0.2992 Lr: 0.00200
[2025-09-06 13:04:27,782 INFO misc.py line 117 1395871] Train: [13/100][205/1462] Data 0.001 (0.001) Batch 2.888 (2.893) Remain 103:13:31 loss: 0.2683 Lr: 0.00200
[2025-09-06 13:04:30,733 INFO misc.py line 117 1395871] Train: [13/100][206/1462] Data 0.001 (0.001) Batch 2.950 (2.893) Remain 103:14:04 loss: 0.4835 Lr: 0.00200
[2025-09-06 13:04:33,448 INFO misc.py line 117 1395871] Train: [13/100][207/1462] Data 0.001 (0.001) Batch 2.715 (2.892) Remain 103:12:09 loss: 0.8336 Lr: 0.00200
[2025-09-06 13:04:36,630 INFO misc.py line 117 1395871] Train: [13/100][208/1462] Data 0.001 (0.001) Batch 3.182 (2.894) Remain 103:15:08 loss: 0.2527 Lr: 0.00200
[2025-09-06 13:04:39,224 INFO misc.py line 117 1395871] Train: [13/100][209/1462] Data 0.001 (0.001) Batch 2.594 (2.892) Remain 103:11:58 loss: 0.2301 Lr: 0.00200
[2025-09-06 13:04:42,026 INFO misc.py line 117 1395871] Train: [13/100][210/1462] Data 0.001 (0.001) Batch 2.803 (2.892) Remain 103:10:59 loss: 0.3115 Lr: 0.00200
[2025-09-06 13:04:44,715 INFO misc.py line 117 1395871] Train: [13/100][211/1462] Data 0.001 (0.001) Batch 2.688 (2.891) Remain 103:08:50 loss: 0.3899 Lr: 0.00200
[2025-09-06 13:04:47,351 INFO misc.py line 117 1395871] Train: [13/100][212/1462] Data 0.002 (0.001) Batch 2.636 (2.890) Remain 103:06:11 loss: 0.5408 Lr: 0.00200
[2025-09-06 13:04:50,114 INFO misc.py line 117 1395871] Train: [13/100][213/1462] Data 0.001 (0.001) Batch 2.763 (2.889) Remain 103:04:51 loss: 0.4264 Lr: 0.00200
[2025-09-06 13:04:52,807 INFO misc.py line 117 1395871] Train: [13/100][214/1462] Data 0.001 (0.001) Batch 2.693 (2.888) Remain 103:02:48 loss: 0.7883 Lr: 0.00200
[2025-09-06 13:04:55,608 INFO misc.py line 117 1395871] Train: [13/100][215/1462] Data 0.001 (0.001) Batch 2.801 (2.888) Remain 103:01:52 loss: 0.5348 Lr: 0.00200
[2025-09-06 13:04:58,441 INFO misc.py line 117 1395871] Train: [13/100][216/1462] Data 0.002 (0.001) Batch 2.834 (2.888) Remain 103:01:17 loss: 0.5422 Lr: 0.00200
[2025-09-06 13:05:01,124 INFO misc.py line 117 1395871] Train: [13/100][217/1462] Data 0.001 (0.001) Batch 2.682 (2.887) Remain 102:59:11 loss: 0.2915 Lr: 0.00200
[2025-09-06 13:05:03,915 INFO misc.py line 117 1395871] Train: [13/100][218/1462] Data 0.002 (0.001) Batch 2.791 (2.886) Remain 102:58:11 loss: 0.5205 Lr: 0.00200
[2025-09-06 13:05:06,623 INFO misc.py line 117 1395871] Train: [13/100][219/1462] Data 0.001 (0.001) Batch 2.708 (2.885) Remain 102:56:22 loss: 0.6652 Lr: 0.00200
[2025-09-06 13:05:09,494 INFO misc.py line 117 1395871] Train: [13/100][220/1462] Data 0.001 (0.001) Batch 2.871 (2.885) Remain 102:56:11 loss: 0.4145 Lr: 0.00200
[2025-09-06 13:05:12,541 INFO misc.py line 117 1395871] Train: [13/100][221/1462] Data 0.001 (0.001) Batch 3.047 (2.886) Remain 102:57:43 loss: 0.5190 Lr: 0.00200
[2025-09-06 13:05:15,506 INFO misc.py line 117 1395871] Train: [13/100][222/1462] Data 0.001 (0.001) Batch 2.965 (2.886) Remain 102:58:27 loss: 0.5160 Lr: 0.00200
[2025-09-06 13:05:18,636 INFO misc.py line 117 1395871] Train: [13/100][223/1462] Data 0.001 (0.001) Batch 3.130 (2.887) Remain 103:00:46 loss: 0.4160 Lr: 0.00200
[2025-09-06 13:05:21,420 INFO misc.py line 117 1395871] Train: [13/100][224/1462] Data 0.001 (0.001) Batch 2.784 (2.887) Remain 102:59:43 loss: 0.3825 Lr: 0.00200
[2025-09-06 13:05:24,590 INFO misc.py line 117 1395871] Train: [13/100][225/1462] Data 0.002 (0.001) Batch 3.170 (2.888) Remain 103:02:24 loss: 0.3784 Lr: 0.00200
[2025-09-06 13:05:27,671 INFO misc.py line 117 1395871] Train: [13/100][226/1462] Data 0.001 (0.001) Batch 3.082 (2.889) Remain 103:04:12 loss: 0.5925 Lr: 0.00200
[2025-09-06 13:05:30,828 INFO misc.py line 117 1395871] Train: [13/100][227/1462] Data 0.002 (0.001) Batch 3.157 (2.890) Remain 103:06:43 loss: 0.3522 Lr: 0.00200
[2025-09-06 13:05:33,776 INFO misc.py line 117 1395871] Train: [13/100][228/1462] Data 0.001 (0.001) Batch 2.948 (2.891) Remain 103:07:13 loss: 0.5891 Lr: 0.00200
[2025-09-06 13:05:36,689 INFO misc.py line 117 1395871] Train: [13/100][229/1462] Data 0.001 (0.001) Batch 2.912 (2.891) Remain 103:07:22 loss: 0.4044 Lr: 0.00200
[2025-09-06 13:05:39,827 INFO misc.py line 117 1395871] Train: [13/100][230/1462] Data 0.001 (0.001) Batch 3.138 (2.892) Remain 103:09:39 loss: 0.3540 Lr: 0.00200
[2025-09-06 13:05:42,501 INFO misc.py line 117 1395871] Train: [13/100][231/1462] Data 0.001 (0.001) Batch 2.674 (2.891) Remain 103:07:34 loss: 1.4900 Lr: 0.00200
[2025-09-06 13:05:45,432 INFO misc.py line 117 1395871] Train: [13/100][232/1462] Data 0.002 (0.001) Batch 2.931 (2.891) Remain 103:07:53 loss: 0.3093 Lr: 0.00200
[2025-09-06 13:05:48,006 INFO misc.py line 117 1395871] Train: [13/100][233/1462] Data 0.001 (0.001) Batch 2.574 (2.890) Remain 103:04:53 loss: 0.5922 Lr: 0.00200
[2025-09-06 13:05:50,884 INFO misc.py line 117 1395871] Train: [13/100][234/1462] Data 0.001 (0.001) Batch 2.878 (2.890) Remain 103:04:44 loss: 0.4265 Lr: 0.00200
[2025-09-06 13:05:53,568 INFO misc.py line 117 1395871] Train: [13/100][235/1462] Data 0.001 (0.001) Batch 2.684 (2.889) Remain 103:02:47 loss: 0.5245 Lr: 0.00200
[2025-09-06 13:05:56,723 INFO misc.py line 117 1395871] Train: [13/100][236/1462] Data 0.001 (0.001) Batch 3.155 (2.890) Remain 103:05:11 loss: 0.7556 Lr: 0.00200
[2025-09-06 13:05:59,620 INFO misc.py line 117 1395871] Train: [13/100][237/1462] Data 0.002 (0.001) Batch 2.897 (2.890) Remain 103:05:12 loss: 0.3158 Lr: 0.00200
[2025-09-06 13:06:02,489 INFO misc.py line 117 1395871] Train: [13/100][238/1462] Data 0.002 (0.001) Batch 2.869 (2.890) Remain 103:04:58 loss: 0.3673 Lr: 0.00200
[2025-09-06 13:06:05,488 INFO misc.py line 117 1395871] Train: [13/100][239/1462] Data 0.002 (0.001) Batch 2.999 (2.890) Remain 103:05:55 loss: 0.5685 Lr: 0.00200
[2025-09-06 13:06:08,757 INFO misc.py line 117 1395871] Train: [13/100][240/1462] Data 0.001 (0.001) Batch 3.269 (2.892) Remain 103:09:17 loss: 0.5199 Lr: 0.00200
[2025-09-06 13:06:11,383 INFO misc.py line 117 1395871] Train: [13/100][241/1462] Data 0.002 (0.001) Batch 2.627 (2.891) Remain 103:06:51 loss: 0.3795 Lr: 0.00200
[2025-09-06 13:06:14,286 INFO misc.py line 117 1395871] Train: [13/100][242/1462] Data 0.002 (0.001) Batch 2.902 (2.891) Remain 103:06:54 loss: 0.3941 Lr: 0.00200
[2025-09-06 13:06:16,956 INFO misc.py line 117 1395871] Train: [13/100][243/1462] Data 0.002 (0.001) Batch 2.670 (2.890) Remain 103:04:53 loss: 0.3037 Lr: 0.00200
[2025-09-06 13:06:19,665 INFO misc.py line 117 1395871] Train: [13/100][244/1462] Data 0.001 (0.001) Batch 2.709 (2.889) Remain 103:03:14 loss: 0.5056 Lr: 0.00200
[2025-09-06 13:06:22,269 INFO misc.py line 117 1395871] Train: [13/100][245/1462] Data 0.002 (0.001) Batch 2.605 (2.888) Remain 103:00:40 loss: 0.5283 Lr: 0.00200
[2025-09-06 13:06:25,160 INFO misc.py line 117 1395871] Train: [13/100][246/1462] Data 0.002 (0.001) Batch 2.890 (2.888) Remain 103:00:39 loss: 0.4814 Lr: 0.00200
[2025-09-06 13:06:27,655 INFO misc.py line 117 1395871] Train: [13/100][247/1462] Data 0.002 (0.001) Batch 2.496 (2.886) Remain 102:57:09 loss: 0.5075 Lr: 0.00200
[2025-09-06 13:06:30,456 INFO misc.py line 117 1395871] Train: [13/100][248/1462] Data 0.001 (0.001) Batch 2.800 (2.886) Remain 102:56:21 loss: 0.2854 Lr: 0.00200
[2025-09-06 13:06:33,382 INFO misc.py line 117 1395871] Train: [13/100][249/1462] Data 0.002 (0.001) Batch 2.926 (2.886) Remain 102:56:40 loss: 0.3382 Lr: 0.00200
[2025-09-06 13:06:36,077 INFO misc.py line 117 1395871] Train: [13/100][250/1462] Data 0.002 (0.001) Batch 2.695 (2.885) Remain 102:54:57 loss: 0.3113 Lr: 0.00200
[2025-09-06 13:06:38,953 INFO misc.py line 117 1395871] Train: [13/100][251/1462] Data 0.002 (0.001) Batch 2.876 (2.885) Remain 102:54:50 loss: 0.7050 Lr: 0.00200
[2025-09-06 13:06:42,038 INFO misc.py line 117 1395871] Train: [13/100][252/1462] Data 0.002 (0.001) Batch 3.085 (2.886) Remain 102:56:30 loss: 0.3438 Lr: 0.00200
[2025-09-06 13:06:44,993 INFO misc.py line 117 1395871] Train: [13/100][253/1462] Data 0.002 (0.001) Batch 2.955 (2.886) Remain 102:57:02 loss: 0.4113 Lr: 0.00200
[2025-09-06 13:06:48,035 INFO misc.py line 117 1395871] Train: [13/100][254/1462] Data 0.002 (0.001) Batch 3.042 (2.887) Remain 102:58:19 loss: 0.2680 Lr: 0.00200
[2025-09-06 13:06:51,151 INFO misc.py line 117 1395871] Train: [13/100][255/1462] Data 0.002 (0.001) Batch 3.116 (2.888) Remain 103:00:13 loss: 0.2949 Lr: 0.00200
[2025-09-06 13:06:53,990 INFO misc.py line 117 1395871] Train: [13/100][256/1462] Data 0.002 (0.001) Batch 2.839 (2.888) Remain 102:59:45 loss: 0.6017 Lr: 0.00200
[2025-09-06 13:06:56,725 INFO misc.py line 117 1395871] Train: [13/100][257/1462] Data 0.001 (0.001) Batch 2.735 (2.887) Remain 102:58:25 loss: 0.4487 Lr: 0.00200
[2025-09-06 13:06:59,733 INFO misc.py line 117 1395871] Train: [13/100][258/1462] Data 0.002 (0.001) Batch 3.008 (2.888) Remain 102:59:23 loss: 0.4493 Lr: 0.00200
[2025-09-06 13:07:02,645 INFO misc.py line 117 1395871] Train: [13/100][259/1462] Data 0.002 (0.001) Batch 2.912 (2.888) Remain 102:59:32 loss: 0.5302 Lr: 0.00200
[2025-09-06 13:07:05,340 INFO misc.py line 117 1395871] Train: [13/100][260/1462] Data 0.001 (0.001) Batch 2.695 (2.887) Remain 102:57:53 loss: 0.4113 Lr: 0.00200
[2025-09-06 13:07:08,131 INFO misc.py line 117 1395871] Train: [13/100][261/1462] Data 0.001 (0.001) Batch 2.791 (2.887) Remain 102:57:02 loss: 0.3611 Lr: 0.00200
[2025-09-06 13:07:11,001 INFO misc.py line 117 1395871] Train: [13/100][262/1462] Data 0.001 (0.001) Batch 2.870 (2.887) Remain 102:56:51 loss: 0.3517 Lr: 0.00200
[2025-09-06 13:07:14,237 INFO misc.py line 117 1395871] Train: [13/100][263/1462] Data 0.001 (0.001) Batch 3.236 (2.888) Remain 102:59:41 loss: 0.6043 Lr: 0.00200
[2025-09-06 13:07:17,375 INFO misc.py line 117 1395871] Train: [13/100][264/1462] Data 0.002 (0.001) Batch 3.138 (2.889) Remain 103:01:41 loss: 0.5860 Lr: 0.00200
[2025-09-06 13:07:20,083 INFO misc.py line 117 1395871] Train: [13/100][265/1462] Data 0.001 (0.001) Batch 2.708 (2.888) Remain 103:00:10 loss: 0.3098 Lr: 0.00200
[2025-09-06 13:07:22,779 INFO misc.py line 117 1395871] Train: [13/100][266/1462] Data 0.001 (0.001) Batch 2.696 (2.887) Remain 102:58:33 loss: 0.4946 Lr: 0.00200
[2025-09-06 13:07:25,600 INFO misc.py line 117 1395871] Train: [13/100][267/1462] Data 0.001 (0.001) Batch 2.821 (2.887) Remain 102:57:58 loss: 0.8986 Lr: 0.00200
[2025-09-06 13:07:28,353 INFO misc.py line 117 1395871] Train: [13/100][268/1462] Data 0.001 (0.001) Batch 2.752 (2.887) Remain 102:56:50 loss: 0.4356 Lr: 0.00200
[2025-09-06 13:07:31,211 INFO misc.py line 117 1395871] Train: [13/100][269/1462] Data 0.001 (0.001) Batch 2.858 (2.887) Remain 102:56:33 loss: 0.4234 Lr: 0.00200
[2025-09-06 13:07:34,133 INFO misc.py line 117 1395871] Train: [13/100][270/1462] Data 0.001 (0.001) Batch 2.922 (2.887) Remain 102:56:47 loss: 0.3217 Lr: 0.00200
[2025-09-06 13:07:37,063 INFO misc.py line 117 1395871] Train: [13/100][271/1462] Data 0.002 (0.001) Batch 2.930 (2.887) Remain 102:57:05 loss: 0.4602 Lr: 0.00200
[2025-09-06 13:07:39,892 INFO misc.py line 117 1395871] Train: [13/100][272/1462] Data 0.001 (0.001) Batch 2.829 (2.887) Remain 102:56:35 loss: 0.3689 Lr: 0.00200
[2025-09-06 13:07:42,750 INFO misc.py line 117 1395871] Train: [13/100][273/1462] Data 0.001 (0.001) Batch 2.858 (2.887) Remain 102:56:18 loss: 0.7872 Lr: 0.00200
[2025-09-06 13:07:45,575 INFO misc.py line 117 1395871] Train: [13/100][274/1462] Data 0.002 (0.001) Batch 2.825 (2.886) Remain 102:55:46 loss: 0.6340 Lr: 0.00200
[2025-09-06 13:07:48,364 INFO misc.py line 117 1395871] Train: [13/100][275/1462] Data 0.001 (0.001) Batch 2.789 (2.886) Remain 102:54:57 loss: 0.6067 Lr: 0.00200
[2025-09-06 13:07:51,090 INFO misc.py line 117 1395871] Train: [13/100][276/1462] Data 0.002 (0.001) Batch 2.726 (2.885) Remain 102:53:39 loss: 0.6391 Lr: 0.00200
[2025-09-06 13:07:53,779 INFO misc.py line 117 1395871] Train: [13/100][277/1462] Data 0.002 (0.001) Batch 2.689 (2.885) Remain 102:52:05 loss: 0.4137 Lr: 0.00200
[2025-09-06 13:07:56,487 INFO misc.py line 117 1395871] Train: [13/100][278/1462] Data 0.001 (0.001) Batch 2.707 (2.884) Remain 102:50:39 loss: 1.0343 Lr: 0.00200
[2025-09-06 13:07:59,284 INFO misc.py line 117 1395871] Train: [13/100][279/1462] Data 0.001 (0.001) Batch 2.797 (2.884) Remain 102:49:56 loss: 0.4557 Lr: 0.00200
[2025-09-06 13:08:02,184 INFO misc.py line 117 1395871] Train: [13/100][280/1462] Data 0.002 (0.001) Batch 2.901 (2.884) Remain 102:50:00 loss: 0.6309 Lr: 0.00200
[2025-09-06 13:08:05,118 INFO misc.py line 117 1395871] Train: [13/100][281/1462] Data 0.002 (0.001) Batch 2.934 (2.884) Remain 102:50:21 loss: 0.4563 Lr: 0.00200
[2025-09-06 13:08:07,971 INFO misc.py line 117 1395871] Train: [13/100][282/1462] Data 0.002 (0.001) Batch 2.854 (2.884) Remain 102:50:04 loss: 0.3260 Lr: 0.00200
[2025-09-06 13:08:10,915 INFO misc.py line 117 1395871] Train: [13/100][283/1462] Data 0.002 (0.001) Batch 2.943 (2.884) Remain 102:50:28 loss: 0.4195 Lr: 0.00200
[2025-09-06 13:08:13,723 INFO misc.py line 117 1395871] Train: [13/100][284/1462] Data 0.003 (0.001) Batch 2.808 (2.884) Remain 102:49:51 loss: 0.6504 Lr: 0.00200
[2025-09-06 13:08:16,894 INFO misc.py line 117 1395871] Train: [13/100][285/1462] Data 0.001 (0.001) Batch 3.172 (2.885) Remain 102:51:59 loss: 0.3439 Lr: 0.00200
[2025-09-06 13:08:19,733 INFO misc.py line 117 1395871] Train: [13/100][286/1462] Data 0.001 (0.001) Batch 2.838 (2.885) Remain 102:51:35 loss: 0.3178 Lr: 0.00200
[2025-09-06 13:08:22,637 INFO misc.py line 117 1395871] Train: [13/100][287/1462] Data 0.002 (0.001) Batch 2.904 (2.885) Remain 102:51:41 loss: 0.8826 Lr: 0.00200
[2025-09-06 13:08:25,516 INFO misc.py line 117 1395871] Train: [13/100][288/1462] Data 0.002 (0.001) Batch 2.879 (2.885) Remain 102:51:35 loss: 0.6790 Lr: 0.00200
[2025-09-06 13:08:28,253 INFO misc.py line 117 1395871] Train: [13/100][289/1462] Data 0.001 (0.001) Batch 2.737 (2.884) Remain 102:50:26 loss: 0.5792 Lr: 0.00200
[2025-09-06 13:08:31,237 INFO misc.py line 117 1395871] Train: [13/100][290/1462] Data 0.001 (0.001) Batch 2.984 (2.884) Remain 102:51:08 loss: 0.5324 Lr: 0.00200
[2025-09-06 13:08:34,155 INFO misc.py line 117 1395871] Train: [13/100][291/1462] Data 0.001 (0.001) Batch 2.918 (2.885) Remain 102:51:20 loss: 0.3414 Lr: 0.00200
[2025-09-06 13:08:37,409 INFO misc.py line 117 1395871] Train: [13/100][292/1462] Data 0.001 (0.001) Batch 3.254 (2.886) Remain 102:54:02 loss: 0.6647 Lr: 0.00200
[2025-09-06 13:08:40,207 INFO misc.py line 117 1395871] Train: [13/100][293/1462] Data 0.001 (0.001) Batch 2.798 (2.886) Remain 102:53:20 loss: 0.4176 Lr: 0.00200
[2025-09-06 13:08:42,939 INFO misc.py line 117 1395871] Train: [13/100][294/1462] Data 0.001 (0.001) Batch 2.732 (2.885) Remain 102:52:09 loss: 0.4466 Lr: 0.00200
[2025-09-06 13:08:45,990 INFO misc.py line 117 1395871] Train: [13/100][295/1462] Data 0.001 (0.001) Batch 3.051 (2.886) Remain 102:53:19 loss: 0.3559 Lr: 0.00200
[2025-09-06 13:08:48,932 INFO misc.py line 117 1395871] Train: [13/100][296/1462] Data 0.001 (0.001) Batch 2.942 (2.886) Remain 102:53:41 loss: 0.2344 Lr: 0.00200
[2025-09-06 13:08:51,937 INFO misc.py line 117 1395871] Train: [13/100][297/1462] Data 0.002 (0.001) Batch 3.005 (2.886) Remain 102:54:30 loss: 0.2243 Lr: 0.00200
[2025-09-06 13:08:54,576 INFO misc.py line 117 1395871] Train: [13/100][298/1462] Data 0.002 (0.001) Batch 2.639 (2.885) Remain 102:52:40 loss: 0.4781 Lr: 0.00200
[2025-09-06 13:08:57,501 INFO misc.py line 117 1395871] Train: [13/100][299/1462] Data 0.001 (0.001) Batch 2.925 (2.886) Remain 102:52:54 loss: 0.5638 Lr: 0.00200
[2025-09-06 13:09:00,392 INFO misc.py line 117 1395871] Train: [13/100][300/1462] Data 0.001 (0.001) Batch 2.891 (2.886) Remain 102:52:53 loss: 0.5617 Lr: 0.00200
[2025-09-06 13:09:03,425 INFO misc.py line 117 1395871] Train: [13/100][301/1462] Data 0.001 (0.001) Batch 3.032 (2.886) Remain 102:53:54 loss: 0.2618 Lr: 0.00200
[2025-09-06 13:09:06,272 INFO misc.py line 117 1395871] Train: [13/100][302/1462] Data 0.002 (0.001) Batch 2.847 (2.886) Remain 102:53:34 loss: 0.1818 Lr: 0.00200
[2025-09-06 13:09:08,578 INFO misc.py line 117 1395871] Train: [13/100][303/1462] Data 0.002 (0.001) Batch 2.306 (2.884) Remain 102:49:23 loss: 0.4587 Lr: 0.00200
[2025-09-06 13:09:11,410 INFO misc.py line 117 1395871] Train: [13/100][304/1462] Data 0.002 (0.001) Batch 2.832 (2.884) Remain 102:48:58 loss: 0.2310 Lr: 0.00200
[2025-09-06 13:09:14,566 INFO misc.py line 117 1395871] Train: [13/100][305/1462] Data 0.001 (0.001) Batch 3.156 (2.885) Remain 102:50:51 loss: 0.1945 Lr: 0.00200
[2025-09-06 13:09:17,630 INFO misc.py line 117 1395871] Train: [13/100][306/1462] Data 0.001 (0.001) Batch 3.064 (2.885) Remain 102:52:04 loss: 0.4981 Lr: 0.00200
[2025-09-06 13:09:20,559 INFO misc.py line 117 1395871] Train: [13/100][307/1462] Data 0.001 (0.001) Batch 2.930 (2.885) Remain 102:52:20 loss: 0.4164 Lr: 0.00200
[2025-09-06 13:09:23,337 INFO misc.py line 117 1395871] Train: [13/100][308/1462] Data 0.002 (0.001) Batch 2.778 (2.885) Remain 102:51:32 loss: 0.4738 Lr: 0.00200
[2025-09-06 13:09:25,853 INFO misc.py line 117 1395871] Train: [13/100][309/1462] Data 0.001 (0.001) Batch 2.516 (2.884) Remain 102:48:54 loss: 0.4044 Lr: 0.00200
[2025-09-06 13:09:28,701 INFO misc.py line 117 1395871] Train: [13/100][310/1462] Data 0.001 (0.001) Batch 2.849 (2.884) Remain 102:48:36 loss: 0.3383 Lr: 0.00200
[2025-09-06 13:09:31,622 INFO misc.py line 117 1395871] Train: [13/100][311/1462] Data 0.001 (0.001) Batch 2.921 (2.884) Remain 102:48:49 loss: 0.2860 Lr: 0.00200
[2025-09-06 13:09:34,575 INFO misc.py line 117 1395871] Train: [13/100][312/1462] Data 0.002 (0.001) Batch 2.952 (2.884) Remain 102:49:14 loss: 0.3273 Lr: 0.00200
[2025-09-06 13:09:37,429 INFO misc.py line 117 1395871] Train: [13/100][313/1462] Data 0.001 (0.001) Batch 2.854 (2.884) Remain 102:48:59 loss: 0.3296 Lr: 0.00200
[2025-09-06 13:09:40,443 INFO misc.py line 117 1395871] Train: [13/100][314/1462] Data 0.002 (0.001) Batch 3.015 (2.884) Remain 102:49:50 loss: 0.4303 Lr: 0.00200
[2025-09-06 13:09:43,471 INFO misc.py line 117 1395871] Train: [13/100][315/1462] Data 0.002 (0.001) Batch 3.028 (2.885) Remain 102:50:46 loss: 0.2950 Lr: 0.00200
[2025-09-06 13:09:46,451 INFO misc.py line 117 1395871] Train: [13/100][316/1462] Data 0.002 (0.001) Batch 2.980 (2.885) Remain 102:51:22 loss: 0.3361 Lr: 0.00200
[2025-09-06 13:09:49,089 INFO misc.py line 117 1395871] Train: [13/100][317/1462] Data 0.001 (0.001) Batch 2.638 (2.884) Remain 102:49:38 loss: 0.3361 Lr: 0.00200
[2025-09-06 13:09:52,083 INFO misc.py line 117 1395871] Train: [13/100][318/1462] Data 0.001 (0.001) Batch 2.994 (2.885) Remain 102:50:20 loss: 0.5020 Lr: 0.00200
[2025-09-06 13:09:55,164 INFO misc.py line 117 1395871] Train: [13/100][319/1462] Data 0.001 (0.001) Batch 3.081 (2.885) Remain 102:51:37 loss: 0.2513 Lr: 0.00200
[2025-09-06 13:09:58,054 INFO misc.py line 117 1395871] Train: [13/100][320/1462] Data 0.001 (0.001) Batch 2.890 (2.885) Remain 102:51:36 loss: 0.3373 Lr: 0.00200
[2025-09-06 13:10:00,756 INFO misc.py line 117 1395871] Train: [13/100][321/1462] Data 0.002 (0.001) Batch 2.701 (2.885) Remain 102:50:19 loss: 0.4505 Lr: 0.00200
[2025-09-06 13:10:03,683 INFO misc.py line 117 1395871] Train: [13/100][322/1462] Data 0.001 (0.001) Batch 2.928 (2.885) Remain 102:50:33 loss: 0.3801 Lr: 0.00200
[2025-09-06 13:10:06,358 INFO misc.py line 117 1395871] Train: [13/100][323/1462] Data 0.002 (0.001) Batch 2.675 (2.884) Remain 102:49:06 loss: 0.2679 Lr: 0.00200
[2025-09-06 13:10:09,280 INFO misc.py line 117 1395871] Train: [13/100][324/1462] Data 0.002 (0.001) Batch 2.922 (2.884) Remain 102:49:18 loss: 0.6945 Lr: 0.00200
[2025-09-06 13:10:12,138 INFO misc.py line 117 1395871] Train: [13/100][325/1462] Data 0.002 (0.001) Batch 2.858 (2.884) Remain 102:49:05 loss: 0.2940 Lr: 0.00200
[2025-09-06 13:10:14,910 INFO misc.py line 117 1395871] Train: [13/100][326/1462] Data 0.001 (0.001) Batch 2.772 (2.884) Remain 102:48:17 loss: 0.4968 Lr: 0.00200
[2025-09-06 13:10:18,082 INFO misc.py line 117 1395871] Train: [13/100][327/1462] Data 0.001 (0.001) Batch 3.172 (2.885) Remain 102:50:08 loss: 0.3947 Lr: 0.00200
[2025-09-06 13:10:21,255 INFO misc.py line 117 1395871] Train: [13/100][328/1462] Data 0.002 (0.001) Batch 3.174 (2.886) Remain 102:52:00 loss: 0.3554 Lr: 0.00200
[2025-09-06 13:10:23,895 INFO misc.py line 117 1395871] Train: [13/100][329/1462] Data 0.001 (0.001) Batch 2.640 (2.885) Remain 102:50:20 loss: 0.4168 Lr: 0.00200
[2025-09-06 13:10:26,873 INFO misc.py line 117 1395871] Train: [13/100][330/1462] Data 0.002 (0.002) Batch 2.978 (2.885) Remain 102:50:53 loss: 0.1738 Lr: 0.00200
[2025-09-06 13:10:29,561 INFO misc.py line 117 1395871] Train: [13/100][331/1462] Data 0.002 (0.002) Batch 2.688 (2.885) Remain 102:49:33 loss: 0.3478 Lr: 0.00200
[2025-09-06 13:10:32,497 INFO misc.py line 117 1395871] Train: [13/100][332/1462] Data 0.002 (0.002) Batch 2.936 (2.885) Remain 102:49:50 loss: 0.3311 Lr: 0.00200
[2025-09-06 13:10:35,878 INFO misc.py line 117 1395871] Train: [13/100][333/1462] Data 0.002 (0.002) Batch 3.382 (2.886) Remain 102:53:01 loss: 0.7610 Lr: 0.00200
[2025-09-06 13:10:39,011 INFO misc.py line 117 1395871] Train: [13/100][334/1462] Data 0.002 (0.002) Batch 3.133 (2.887) Remain 102:54:33 loss: 0.3825 Lr: 0.00200
[2025-09-06 13:10:41,691 INFO misc.py line 117 1395871] Train: [13/100][335/1462] Data 0.002 (0.002) Batch 2.679 (2.886) Remain 102:53:10 loss: 0.5005 Lr: 0.00200
[2025-09-06 13:10:44,441 INFO misc.py line 117 1395871] Train: [13/100][336/1462] Data 0.001 (0.002) Batch 2.751 (2.886) Remain 102:52:15 loss: 0.4993 Lr: 0.00200
[2025-09-06 13:10:47,162 INFO misc.py line 117 1395871] Train: [13/100][337/1462] Data 0.002 (0.002) Batch 2.721 (2.886) Remain 102:51:09 loss: 0.5090 Lr: 0.00200
[2025-09-06 13:10:50,150 INFO misc.py line 117 1395871] Train: [13/100][338/1462] Data 0.001 (0.002) Batch 2.988 (2.886) Remain 102:51:45 loss: 0.2338 Lr: 0.00200
[2025-09-06 13:10:52,698 INFO misc.py line 117 1395871] Train: [13/100][339/1462] Data 0.002 (0.002) Batch 2.548 (2.885) Remain 102:49:33 loss: 0.6546 Lr: 0.00200
[2025-09-06 13:10:55,370 INFO misc.py line 117 1395871] Train: [13/100][340/1462] Data 0.001 (0.002) Batch 2.672 (2.884) Remain 102:48:09 loss: 0.3548 Lr: 0.00200
[2025-09-06 13:10:57,934 INFO misc.py line 117 1395871] Train: [13/100][341/1462] Data 0.001 (0.002) Batch 2.564 (2.883) Remain 102:46:05 loss: 0.3728 Lr: 0.00200
[2025-09-06 13:11:00,945 INFO misc.py line 117 1395871] Train: [13/100][342/1462] Data 0.002 (0.002) Batch 3.010 (2.884) Remain 102:46:50 loss: 0.3586 Lr: 0.00200
[2025-09-06 13:11:03,779 INFO misc.py line 117 1395871] Train: [13/100][343/1462] Data 0.001 (0.002) Batch 2.834 (2.883) Remain 102:46:29 loss: 0.3763 Lr: 0.00200
[2025-09-06 13:11:06,555 INFO misc.py line 117 1395871] Train: [13/100][344/1462] Data 0.002 (0.002) Batch 2.777 (2.883) Remain 102:45:45 loss: 0.3880 Lr: 0.00200
[2025-09-06 13:11:09,552 INFO misc.py line 117 1395871] Train: [13/100][345/1462] Data 0.002 (0.002) Batch 2.996 (2.884) Remain 102:46:25 loss: 0.4056 Lr: 0.00200
[2025-09-06 13:11:12,435 INFO misc.py line 117 1395871] Train: [13/100][346/1462] Data 0.002 (0.002) Batch 2.883 (2.884) Remain 102:46:22 loss: 0.2388 Lr: 0.00200
[2025-09-06 13:11:15,211 INFO misc.py line 117 1395871] Train: [13/100][347/1462] Data 0.001 (0.002) Batch 2.776 (2.883) Remain 102:45:39 loss: 0.4149 Lr: 0.00200
[2025-09-06 13:11:18,399 INFO misc.py line 117 1395871] Train: [13/100][348/1462] Data 0.002 (0.002) Batch 3.188 (2.884) Remain 102:47:30 loss: 0.3901 Lr: 0.00200
[2025-09-06 13:11:21,439 INFO misc.py line 117 1395871] Train: [13/100][349/1462] Data 0.001 (0.002) Batch 3.040 (2.885) Remain 102:48:24 loss: 0.4081 Lr: 0.00200
[2025-09-06 13:11:24,214 INFO misc.py line 117 1395871] Train: [13/100][350/1462] Data 0.001 (0.002) Batch 2.775 (2.884) Remain 102:47:41 loss: 0.6682 Lr: 0.00200
[2025-09-06 13:11:26,866 INFO misc.py line 117 1395871] Train: [13/100][351/1462] Data 0.001 (0.002) Batch 2.653 (2.884) Remain 102:46:13 loss: 0.3385 Lr: 0.00200
[2025-09-06 13:11:29,375 INFO misc.py line 117 1395871] Train: [13/100][352/1462] Data 0.002 (0.002) Batch 2.509 (2.882) Remain 102:43:52 loss: 0.1679 Lr: 0.00200
[2025-09-06 13:11:31,966 INFO misc.py line 117 1395871] Train: [13/100][353/1462] Data 0.001 (0.002) Batch 2.591 (2.882) Remain 102:42:02 loss: 0.4439 Lr: 0.00200
[2025-09-06 13:11:34,817 INFO misc.py line 117 1395871] Train: [13/100][354/1462] Data 0.002 (0.002) Batch 2.851 (2.882) Remain 102:41:48 loss: 0.3995 Lr: 0.00200
[2025-09-06 13:11:37,492 INFO misc.py line 117 1395871] Train: [13/100][355/1462] Data 0.002 (0.002) Batch 2.675 (2.881) Remain 102:40:30 loss: 0.8334 Lr: 0.00200
[2025-09-06 13:11:40,322 INFO misc.py line 117 1395871] Train: [13/100][356/1462] Data 0.002 (0.002) Batch 2.830 (2.881) Remain 102:40:09 loss: 0.2816 Lr: 0.00200
[2025-09-06 13:11:43,084 INFO misc.py line 117 1395871] Train: [13/100][357/1462] Data 0.002 (0.002) Batch 2.762 (2.880) Remain 102:39:23 loss: 0.3148 Lr: 0.00200
[2025-09-06 13:11:45,936 INFO misc.py line 117 1395871] Train: [13/100][358/1462] Data 0.002 (0.002) Batch 2.852 (2.880) Remain 102:39:10 loss: 0.3848 Lr: 0.00200
[2025-09-06 13:11:49,163 INFO misc.py line 117 1395871] Train: [13/100][359/1462] Data 0.002 (0.002) Batch 3.226 (2.881) Remain 102:41:11 loss: 0.3345 Lr: 0.00200
[2025-09-06 13:11:52,069 INFO misc.py line 117 1395871] Train: [13/100][360/1462] Data 0.001 (0.002) Batch 2.906 (2.881) Remain 102:41:17 loss: 0.5278 Lr: 0.00200
[2025-09-06 13:11:54,596 INFO misc.py line 117 1395871] Train: [13/100][361/1462] Data 0.001 (0.002) Batch 2.527 (2.880) Remain 102:39:07 loss: 0.4874 Lr: 0.00200
[2025-09-06 13:11:57,348 INFO misc.py line 117 1395871] Train: [13/100][362/1462] Data 0.002 (0.002) Batch 2.752 (2.880) Remain 102:38:19 loss: 0.5121 Lr: 0.00200
[2025-09-06 13:12:00,169 INFO misc.py line 117 1395871] Train: [13/100][363/1462] Data 0.001 (0.002) Batch 2.821 (2.880) Remain 102:37:55 loss: 0.4022 Lr: 0.00200
[2025-09-06 13:12:03,029 INFO misc.py line 117 1395871] Train: [13/100][364/1462] Data 0.001 (0.002) Batch 2.860 (2.880) Remain 102:37:45 loss: 0.3433 Lr: 0.00200
[2025-09-06 13:12:06,066 INFO misc.py line 117 1395871] Train: [13/100][365/1462] Data 0.001 (0.002) Batch 3.037 (2.880) Remain 102:38:38 loss: 0.8495 Lr: 0.00200
[2025-09-06 13:12:08,541 INFO misc.py line 117 1395871] Train: [13/100][366/1462] Data 0.001 (0.002) Batch 2.475 (2.879) Remain 102:36:12 loss: 0.5045 Lr: 0.00200
[2025-09-06 13:12:11,561 INFO misc.py line 117 1395871] Train: [13/100][367/1462] Data 0.002 (0.002) Batch 3.020 (2.880) Remain 102:36:58 loss: 0.4597 Lr: 0.00200
[2025-09-06 13:12:14,255 INFO misc.py line 117 1395871] Train: [13/100][368/1462] Data 0.002 (0.002) Batch 2.694 (2.879) Remain 102:35:50 loss: 0.4521 Lr: 0.00200
[2025-09-06 13:12:17,179 INFO misc.py line 117 1395871] Train: [13/100][369/1462] Data 0.001 (0.002) Batch 2.924 (2.879) Remain 102:36:03 loss: 0.5508 Lr: 0.00200
[2025-09-06 13:12:20,157 INFO misc.py line 117 1395871] Train: [13/100][370/1462] Data 0.001 (0.002) Batch 2.979 (2.879) Remain 102:36:35 loss: 0.4550 Lr: 0.00200
[2025-09-06 13:12:23,378 INFO misc.py line 117 1395871] Train: [13/100][371/1462] Data 0.001 (0.002) Batch 3.221 (2.880) Remain 102:38:31 loss: 0.2301 Lr: 0.00200
[2025-09-06 13:12:26,044 INFO misc.py line 117 1395871] Train: [13/100][372/1462] Data 0.001 (0.002) Batch 2.665 (2.880) Remain 102:37:13 loss: 0.4342 Lr: 0.00200
[2025-09-06 13:12:28,713 INFO misc.py line 117 1395871] Train: [13/100][373/1462] Data 0.001 (0.002) Batch 2.670 (2.879) Remain 102:35:58 loss: 0.4129 Lr: 0.00200
[2025-09-06 13:12:31,294 INFO misc.py line 117 1395871] Train: [13/100][374/1462] Data 0.001 (0.002) Batch 2.580 (2.878) Remain 102:34:11 loss: 0.4178 Lr: 0.00200
[2025-09-06 13:12:33,729 INFO misc.py line 117 1395871] Train: [13/100][375/1462] Data 0.001 (0.002) Batch 2.435 (2.877) Remain 102:31:36 loss: 0.2145 Lr: 0.00200
[2025-09-06 13:12:36,539 INFO misc.py line 117 1395871] Train: [13/100][376/1462] Data 0.001 (0.001) Batch 2.811 (2.877) Remain 102:31:10 loss: 0.3488 Lr: 0.00200
[2025-09-06 13:12:39,487 INFO misc.py line 117 1395871] Train: [13/100][377/1462] Data 0.002 (0.002) Batch 2.948 (2.877) Remain 102:31:31 loss: 0.7106 Lr: 0.00200
[2025-09-06 13:12:43,198 INFO misc.py line 117 1395871] Train: [13/100][378/1462] Data 0.001 (0.002) Batch 3.711 (2.879) Remain 102:36:14 loss: 0.7245 Lr: 0.00200
[2025-09-06 13:12:45,977 INFO misc.py line 117 1395871] Train: [13/100][379/1462] Data 0.001 (0.001) Batch 2.779 (2.879) Remain 102:35:37 loss: 0.3938 Lr: 0.00200
[2025-09-06 13:12:48,882 INFO misc.py line 117 1395871] Train: [13/100][380/1462] Data 0.001 (0.001) Batch 2.905 (2.879) Remain 102:35:42 loss: 0.4373 Lr: 0.00200
[2025-09-06 13:12:51,354 INFO misc.py line 117 1395871] Train: [13/100][381/1462] Data 0.001 (0.001) Batch 2.471 (2.878) Remain 102:33:21 loss: 0.6096 Lr: 0.00200
[2025-09-06 13:12:54,193 INFO misc.py line 117 1395871] Train: [13/100][382/1462] Data 0.001 (0.001) Batch 2.839 (2.878) Remain 102:33:05 loss: 0.2392 Lr: 0.00200
[2025-09-06 13:12:57,281 INFO misc.py line 117 1395871] Train: [13/100][383/1462] Data 0.001 (0.001) Batch 3.088 (2.879) Remain 102:34:13 loss: 0.3035 Lr: 0.00200
[2025-09-06 13:13:00,050 INFO misc.py line 117 1395871] Train: [13/100][384/1462] Data 0.001 (0.001) Batch 2.769 (2.878) Remain 102:33:33 loss: 0.6449 Lr: 0.00200
[2025-09-06 13:13:03,025 INFO misc.py line 117 1395871] Train: [13/100][385/1462] Data 0.001 (0.001) Batch 2.975 (2.879) Remain 102:34:03 loss: 0.6960 Lr: 0.00200
[2025-09-06 13:13:06,162 INFO misc.py line 117 1395871] Train: [13/100][386/1462] Data 0.002 (0.001) Batch 3.137 (2.879) Remain 102:35:26 loss: 0.3406 Lr: 0.00200
[2025-09-06 13:13:09,026 INFO misc.py line 117 1395871] Train: [13/100][387/1462] Data 0.001 (0.001) Batch 2.865 (2.879) Remain 102:35:19 loss: 0.3982 Lr: 0.00200
[2025-09-06 13:13:12,089 INFO misc.py line 117 1395871] Train: [13/100][388/1462] Data 0.002 (0.001) Batch 3.063 (2.880) Remain 102:36:17 loss: 0.3059 Lr: 0.00200
[2025-09-06 13:13:14,861 INFO misc.py line 117 1395871] Train: [13/100][389/1462] Data 0.001 (0.001) Batch 2.772 (2.879) Remain 102:35:38 loss: 0.2757 Lr: 0.00200
[2025-09-06 13:13:17,698 INFO misc.py line 117 1395871] Train: [13/100][390/1462] Data 0.001 (0.001) Batch 2.837 (2.879) Remain 102:35:21 loss: 0.5685 Lr: 0.00200
[2025-09-06 13:13:20,300 INFO misc.py line 117 1395871] Train: [13/100][391/1462] Data 0.002 (0.001) Batch 2.602 (2.879) Remain 102:33:47 loss: 0.9803 Lr: 0.00200
[2025-09-06 13:13:23,226 INFO misc.py line 117 1395871] Train: [13/100][392/1462] Data 0.002 (0.001) Batch 2.926 (2.879) Remain 102:33:59 loss: 0.2079 Lr: 0.00200
[2025-09-06 13:13:26,329 INFO misc.py line 117 1395871] Train: [13/100][393/1462] Data 0.001 (0.001) Batch 3.104 (2.879) Remain 102:35:10 loss: 0.5526 Lr: 0.00200
[2025-09-06 13:13:29,022 INFO misc.py line 117 1395871] Train: [13/100][394/1462] Data 0.001 (0.001) Batch 2.692 (2.879) Remain 102:34:06 loss: 0.4387 Lr: 0.00200
[2025-09-06 13:13:31,933 INFO misc.py line 117 1395871] Train: [13/100][395/1462] Data 0.001 (0.001) Batch 2.911 (2.879) Remain 102:34:14 loss: 1.1010 Lr: 0.00200
[2025-09-06 13:13:34,567 INFO misc.py line 117 1395871] Train: [13/100][396/1462] Data 0.001 (0.001) Batch 2.634 (2.878) Remain 102:32:51 loss: 0.3867 Lr: 0.00200
[2025-09-06 13:13:37,334 INFO misc.py line 117 1395871] Train: [13/100][397/1462] Data 0.001 (0.001) Batch 2.767 (2.878) Remain 102:32:12 loss: 0.3081 Lr: 0.00200
[2025-09-06 13:13:40,474 INFO misc.py line 117 1395871] Train: [13/100][398/1462] Data 0.001 (0.001) Batch 3.140 (2.879) Remain 102:33:34 loss: 0.4164 Lr: 0.00200
[2025-09-06 13:13:43,464 INFO misc.py line 117 1395871] Train: [13/100][399/1462] Data 0.001 (0.001) Batch 2.991 (2.879) Remain 102:34:07 loss: 0.4809 Lr: 0.00200
[2025-09-06 13:13:46,428 INFO misc.py line 117 1395871] Train: [13/100][400/1462] Data 0.002 (0.001) Batch 2.964 (2.879) Remain 102:34:32 loss: 0.5606 Lr: 0.00200
[2025-09-06 13:13:49,966 INFO misc.py line 117 1395871] Train: [13/100][401/1462] Data 0.002 (0.001) Batch 3.538 (2.881) Remain 102:38:01 loss: 0.5609 Lr: 0.00200
[2025-09-06 13:13:53,045 INFO misc.py line 117 1395871] Train: [13/100][402/1462] Data 0.002 (0.001) Batch 3.079 (2.881) Remain 102:39:02 loss: 0.7006 Lr: 0.00200
[2025-09-06 13:13:55,998 INFO misc.py line 117 1395871] Train: [13/100][403/1462] Data 0.001 (0.001) Batch 2.953 (2.882) Remain 102:39:22 loss: 0.3466 Lr: 0.00200
[2025-09-06 13:13:58,489 INFO misc.py line 117 1395871] Train: [13/100][404/1462] Data 0.001 (0.001) Batch 2.491 (2.881) Remain 102:37:14 loss: 0.4103 Lr: 0.00200
[2025-09-06 13:14:01,353 INFO misc.py line 117 1395871] Train: [13/100][405/1462] Data 0.001 (0.001) Batch 2.864 (2.880) Remain 102:37:06 loss: 0.7758 Lr: 0.00200
[2025-09-06 13:14:03,928 INFO misc.py line 117 1395871] Train: [13/100][406/1462] Data 0.001 (0.001) Batch 2.575 (2.880) Remain 102:35:26 loss: 0.7356 Lr: 0.00200
[2025-09-06 13:14:06,757 INFO misc.py line 117 1395871] Train: [13/100][407/1462] Data 0.001 (0.001) Batch 2.828 (2.880) Remain 102:35:07 loss: 0.8890 Lr: 0.00200
[2025-09-06 13:14:09,520 INFO misc.py line 117 1395871] Train: [13/100][408/1462] Data 0.001 (0.001) Batch 2.763 (2.879) Remain 102:34:27 loss: 0.3916 Lr: 0.00200
[2025-09-06 13:14:12,441 INFO misc.py line 117 1395871] Train: [13/100][409/1462] Data 0.001 (0.001) Batch 2.920 (2.879) Remain 102:34:37 loss: 0.5303 Lr: 0.00200
[2025-09-06 13:14:15,198 INFO misc.py line 117 1395871] Train: [13/100][410/1462] Data 0.002 (0.001) Batch 2.758 (2.879) Remain 102:33:56 loss: 0.5344 Lr: 0.00200
[2025-09-06 13:14:18,317 INFO misc.py line 117 1395871] Train: [13/100][411/1462] Data 0.002 (0.001) Batch 3.119 (2.880) Remain 102:35:09 loss: 0.5600 Lr: 0.00200
[2025-09-06 13:14:20,967 INFO misc.py line 117 1395871] Train: [13/100][412/1462] Data 0.002 (0.001) Batch 2.650 (2.879) Remain 102:33:54 loss: 0.4926 Lr: 0.00200
[2025-09-06 13:14:23,759 INFO misc.py line 117 1395871] Train: [13/100][413/1462] Data 0.002 (0.001) Batch 2.792 (2.879) Remain 102:33:24 loss: 0.3228 Lr: 0.00200
[2025-09-06 13:14:26,446 INFO misc.py line 117 1395871] Train: [13/100][414/1462] Data 0.002 (0.001) Batch 2.687 (2.878) Remain 102:32:21 loss: 0.4755 Lr: 0.00200
[2025-09-06 13:14:29,087 INFO misc.py line 117 1395871] Train: [13/100][415/1462] Data 0.001 (0.001) Batch 2.641 (2.878) Remain 102:31:04 loss: 0.6499 Lr: 0.00200
[2025-09-06 13:14:32,160 INFO misc.py line 117 1395871] Train: [13/100][416/1462] Data 0.001 (0.001) Batch 3.073 (2.878) Remain 102:32:02 loss: 0.3303 Lr: 0.00200
[2025-09-06 13:14:35,247 INFO misc.py line 117 1395871] Train: [13/100][417/1462] Data 0.002 (0.001) Batch 3.087 (2.879) Remain 102:33:03 loss: 0.4786 Lr: 0.00200
[2025-09-06 13:14:37,847 INFO misc.py line 117 1395871] Train: [13/100][418/1462] Data 0.002 (0.001) Batch 2.600 (2.878) Remain 102:31:34 loss: 0.4992 Lr: 0.00200
[2025-09-06 13:14:40,756 INFO misc.py line 117 1395871] Train: [13/100][419/1462] Data 0.001 (0.001) Batch 2.909 (2.878) Remain 102:31:41 loss: 0.6740 Lr: 0.00200
[2025-09-06 13:14:43,887 INFO misc.py line 117 1395871] Train: [13/100][420/1462] Data 0.001 (0.001) Batch 3.131 (2.879) Remain 102:32:56 loss: 0.3857 Lr: 0.00200
[2025-09-06 13:14:46,617 INFO misc.py line 117 1395871] Train: [13/100][421/1462] Data 0.002 (0.001) Batch 2.730 (2.879) Remain 102:32:07 loss: 0.4863 Lr: 0.00200
[2025-09-06 13:14:49,430 INFO misc.py line 117 1395871] Train: [13/100][422/1462] Data 0.001 (0.001) Batch 2.813 (2.878) Remain 102:31:45 loss: 0.2531 Lr: 0.00200
[2025-09-06 13:14:52,122 INFO misc.py line 117 1395871] Train: [13/100][423/1462] Data 0.002 (0.001) Batch 2.692 (2.878) Remain 102:30:45 loss: 0.8450 Lr: 0.00200
[2025-09-06 13:14:55,073 INFO misc.py line 117 1395871] Train: [13/100][424/1462] Data 0.002 (0.001) Batch 2.950 (2.878) Remain 102:31:04 loss: 0.3321 Lr: 0.00200
[2025-09-06 13:14:57,816 INFO misc.py line 117 1395871] Train: [13/100][425/1462] Data 0.002 (0.001) Batch 2.744 (2.878) Remain 102:30:20 loss: 0.3090 Lr: 0.00200
[2025-09-06 13:15:00,514 INFO misc.py line 117 1395871] Train: [13/100][426/1462] Data 0.002 (0.001) Batch 2.697 (2.877) Remain 102:29:23 loss: 0.4082 Lr: 0.00200
[2025-09-06 13:15:03,322 INFO misc.py line 117 1395871] Train: [13/100][427/1462] Data 0.002 (0.001) Batch 2.808 (2.877) Remain 102:28:59 loss: 0.4210 Lr: 0.00200
[2025-09-06 13:15:07,030 INFO misc.py line 117 1395871] Train: [13/100][428/1462] Data 0.002 (0.001) Batch 3.708 (2.879) Remain 102:33:07 loss: 0.3851 Lr: 0.00200
[2025-09-06 13:15:09,705 INFO misc.py line 117 1395871] Train: [13/100][429/1462] Data 0.001 (0.001) Batch 2.675 (2.879) Remain 102:32:02 loss: 0.5006 Lr: 0.00200
[2025-09-06 13:15:12,537 INFO misc.py line 117 1395871] Train: [13/100][430/1462] Data 0.001 (0.001) Batch 2.831 (2.879) Remain 102:31:45 loss: 0.4998 Lr: 0.00200
[2025-09-06 13:15:15,632 INFO misc.py line 117 1395871] Train: [13/100][431/1462] Data 0.001 (0.001) Batch 3.096 (2.879) Remain 102:32:48 loss: 0.3597 Lr: 0.00200
[2025-09-06 13:15:18,265 INFO misc.py line 117 1395871] Train: [13/100][432/1462] Data 0.001 (0.001) Batch 2.633 (2.878) Remain 102:31:31 loss: 0.8444 Lr: 0.00200
[2025-09-06 13:15:20,983 INFO misc.py line 117 1395871] Train: [13/100][433/1462] Data 0.001 (0.001) Batch 2.717 (2.878) Remain 102:30:40 loss: 0.3577 Lr: 0.00200
[2025-09-06 13:15:24,041 INFO misc.py line 117 1395871] Train: [13/100][434/1462] Data 0.002 (0.001) Batch 3.058 (2.879) Remain 102:31:31 loss: 0.1593 Lr: 0.00200
[2025-09-06 13:15:26,779 INFO misc.py line 117 1395871] Train: [13/100][435/1462] Data 0.002 (0.001) Batch 2.738 (2.878) Remain 102:30:46 loss: 0.5432 Lr: 0.00200
[2025-09-06 13:15:29,892 INFO misc.py line 117 1395871] Train: [13/100][436/1462] Data 0.002 (0.001) Batch 3.113 (2.879) Remain 102:31:53 loss: 0.7346 Lr: 0.00200
[2025-09-06 13:15:32,916 INFO misc.py line 117 1395871] Train: [13/100][437/1462] Data 0.002 (0.001) Batch 3.024 (2.879) Remain 102:32:33 loss: 0.3678 Lr: 0.00200
[2025-09-06 13:15:35,614 INFO misc.py line 117 1395871] Train: [13/100][438/1462] Data 0.002 (0.001) Batch 2.698 (2.879) Remain 102:31:37 loss: 0.3125 Lr: 0.00200
[2025-09-06 13:15:38,722 INFO misc.py line 117 1395871] Train: [13/100][439/1462] Data 0.001 (0.001) Batch 3.109 (2.879) Remain 102:32:41 loss: 0.5134 Lr: 0.00200
[2025-09-06 13:15:41,476 INFO misc.py line 117 1395871] Train: [13/100][440/1462] Data 0.002 (0.001) Batch 2.754 (2.879) Remain 102:32:02 loss: 0.3207 Lr: 0.00200
[2025-09-06 13:15:44,158 INFO misc.py line 117 1395871] Train: [13/100][441/1462] Data 0.001 (0.001) Batch 2.682 (2.878) Remain 102:31:01 loss: 0.6356 Lr: 0.00200
[2025-09-06 13:15:47,078 INFO misc.py line 117 1395871] Train: [13/100][442/1462] Data 0.002 (0.001) Batch 2.920 (2.879) Remain 102:31:10 loss: 0.2861 Lr: 0.00200
[2025-09-06 13:15:49,777 INFO misc.py line 117 1395871] Train: [13/100][443/1462] Data 0.001 (0.001) Batch 2.698 (2.878) Remain 102:30:15 loss: 0.3677 Lr: 0.00200
[2025-09-06 13:15:53,311 INFO misc.py line 117 1395871] Train: [13/100][444/1462] Data 0.002 (0.001) Batch 3.534 (2.880) Remain 102:33:23 loss: 0.4235 Lr: 0.00200
[2025-09-06 13:15:55,864 INFO misc.py line 117 1395871] Train: [13/100][445/1462] Data 0.002 (0.001) Batch 2.553 (2.879) Remain 102:31:45 loss: 0.5706 Lr: 0.00200
[2025-09-06 13:15:59,196 INFO misc.py line 117 1395871] Train: [13/100][446/1462] Data 0.002 (0.001) Batch 3.332 (2.880) Remain 102:33:54 loss: 0.6509 Lr: 0.00200
[2025-09-06 13:16:02,164 INFO misc.py line 117 1395871] Train: [13/100][447/1462] Data 0.001 (0.001) Batch 2.968 (2.880) Remain 102:34:16 loss: 0.3515 Lr: 0.00200
[2025-09-06 13:16:05,257 INFO misc.py line 117 1395871] Train: [13/100][448/1462] Data 0.002 (0.001) Batch 3.094 (2.881) Remain 102:35:15 loss: 0.3136 Lr: 0.00200
[2025-09-06 13:16:08,014 INFO misc.py line 117 1395871] Train: [13/100][449/1462] Data 0.001 (0.001) Batch 2.757 (2.880) Remain 102:34:36 loss: 0.3305 Lr: 0.00200
[2025-09-06 13:16:10,963 INFO misc.py line 117 1395871] Train: [13/100][450/1462] Data 0.002 (0.001) Batch 2.949 (2.880) Remain 102:34:53 loss: 0.6648 Lr: 0.00200
[2025-09-06 13:16:13,685 INFO misc.py line 117 1395871] Train: [13/100][451/1462] Data 0.002 (0.002) Batch 2.722 (2.880) Remain 102:34:05 loss: 0.5115 Lr: 0.00200
[2025-09-06 13:16:16,526 INFO misc.py line 117 1395871] Train: [13/100][452/1462] Data 0.002 (0.002) Batch 2.841 (2.880) Remain 102:33:51 loss: 0.3054 Lr: 0.00200
[2025-09-06 13:16:19,615 INFO misc.py line 117 1395871] Train: [13/100][453/1462] Data 0.002 (0.002) Batch 3.089 (2.880) Remain 102:34:47 loss: 0.5050 Lr: 0.00200
[2025-09-06 13:16:22,527 INFO misc.py line 117 1395871] Train: [13/100][454/1462] Data 0.002 (0.002) Batch 2.911 (2.881) Remain 102:34:53 loss: 0.3969 Lr: 0.00200
[2025-09-06 13:16:25,279 INFO misc.py line 117 1395871] Train: [13/100][455/1462] Data 0.001 (0.002) Batch 2.752 (2.880) Remain 102:34:14 loss: 0.4354 Lr: 0.00200
[2025-09-06 13:16:28,576 INFO misc.py line 117 1395871] Train: [13/100][456/1462] Data 0.002 (0.002) Batch 3.297 (2.881) Remain 102:36:09 loss: 0.5880 Lr: 0.00200
[2025-09-06 13:16:31,615 INFO misc.py line 117 1395871] Train: [13/100][457/1462] Data 0.002 (0.002) Batch 3.040 (2.882) Remain 102:36:51 loss: 0.3459 Lr: 0.00200
[2025-09-06 13:16:34,472 INFO misc.py line 117 1395871] Train: [13/100][458/1462] Data 0.002 (0.002) Batch 2.857 (2.881) Remain 102:36:41 loss: 0.4149 Lr: 0.00200
[2025-09-06 13:16:37,219 INFO misc.py line 117 1395871] Train: [13/100][459/1462] Data 0.002 (0.002) Batch 2.747 (2.881) Remain 102:36:00 loss: 0.5653 Lr: 0.00200
[2025-09-06 13:16:40,014 INFO misc.py line 117 1395871] Train: [13/100][460/1462] Data 0.001 (0.002) Batch 2.795 (2.881) Remain 102:35:33 loss: 0.2907 Lr: 0.00200
[2025-09-06 13:16:43,500 INFO misc.py line 117 1395871] Train: [13/100][461/1462] Data 0.001 (0.002) Batch 3.486 (2.882) Remain 102:38:20 loss: 0.3367 Lr: 0.00200
[2025-09-06 13:16:46,304 INFO misc.py line 117 1395871] Train: [13/100][462/1462] Data 0.002 (0.002) Batch 2.804 (2.882) Remain 102:37:55 loss: 0.4737 Lr: 0.00200
[2025-09-06 13:16:49,164 INFO misc.py line 117 1395871] Train: [13/100][463/1462] Data 0.001 (0.002) Batch 2.860 (2.882) Remain 102:37:46 loss: 0.9068 Lr: 0.00200
[2025-09-06 13:16:52,066 INFO misc.py line 117 1395871] Train: [13/100][464/1462] Data 0.001 (0.002) Batch 2.902 (2.882) Remain 102:37:48 loss: 0.4834 Lr: 0.00200
[2025-09-06 13:16:55,072 INFO misc.py line 117 1395871] Train: [13/100][465/1462] Data 0.002 (0.002) Batch 3.007 (2.882) Remain 102:38:20 loss: 0.6583 Lr: 0.00200
[2025-09-06 13:16:57,863 INFO misc.py line 117 1395871] Train: [13/100][466/1462] Data 0.002 (0.002) Batch 2.791 (2.882) Remain 102:37:52 loss: 0.2522 Lr: 0.00200
[2025-09-06 13:17:00,369 INFO misc.py line 117 1395871] Train: [13/100][467/1462] Data 0.001 (0.002) Batch 2.506 (2.881) Remain 102:36:05 loss: 0.2049 Lr: 0.00200
[2025-09-06 13:17:03,080 INFO misc.py line 117 1395871] Train: [13/100][468/1462] Data 0.002 (0.002) Batch 2.711 (2.881) Remain 102:35:15 loss: 0.6832 Lr: 0.00200
[2025-09-06 13:17:05,932 INFO misc.py line 117 1395871] Train: [13/100][469/1462] Data 0.001 (0.002) Batch 2.852 (2.881) Remain 102:35:04 loss: 0.6088 Lr: 0.00200
[2025-09-06 13:17:08,686 INFO misc.py line 117 1395871] Train: [13/100][470/1462] Data 0.002 (0.002) Batch 2.754 (2.881) Remain 102:34:26 loss: 0.3253 Lr: 0.00200
[2025-09-06 13:17:11,665 INFO misc.py line 117 1395871] Train: [13/100][471/1462] Data 0.001 (0.002) Batch 2.980 (2.881) Remain 102:34:51 loss: 0.4965 Lr: 0.00200
[2025-09-06 13:17:14,489 INFO misc.py line 117 1395871] Train: [13/100][472/1462] Data 0.001 (0.002) Batch 2.824 (2.881) Remain 102:34:32 loss: 0.3933 Lr: 0.00200
[2025-09-06 13:17:17,416 INFO misc.py line 117 1395871] Train: [13/100][473/1462] Data 0.001 (0.002) Batch 2.927 (2.881) Remain 102:34:42 loss: 0.5767 Lr: 0.00200
[2025-09-06 13:17:20,044 INFO misc.py line 117 1395871] Train: [13/100][474/1462] Data 0.002 (0.002) Batch 2.628 (2.880) Remain 102:33:30 loss: 0.3573 Lr: 0.00200
[2025-09-06 13:17:22,798 INFO misc.py line 117 1395871] Train: [13/100][475/1462] Data 0.002 (0.002) Batch 2.755 (2.880) Remain 102:32:53 loss: 0.3717 Lr: 0.00200
[2025-09-06 13:17:25,426 INFO misc.py line 117 1395871] Train: [13/100][476/1462] Data 0.001 (0.002) Batch 2.627 (2.880) Remain 102:31:42 loss: 0.3062 Lr: 0.00200
[2025-09-06 13:17:28,249 INFO misc.py line 117 1395871] Train: [13/100][477/1462] Data 0.002 (0.002) Batch 2.822 (2.879) Remain 102:31:23 loss: 0.4253 Lr: 0.00200
[2025-09-06 13:17:30,957 INFO misc.py line 117 1395871] Train: [13/100][478/1462] Data 0.003 (0.002) Batch 2.709 (2.879) Remain 102:30:34 loss: 0.2771 Lr: 0.00200
[2025-09-06 13:17:34,045 INFO misc.py line 117 1395871] Train: [13/100][479/1462] Data 0.001 (0.002) Batch 3.088 (2.880) Remain 102:31:28 loss: 0.4033 Lr: 0.00200
[2025-09-06 13:17:37,177 INFO misc.py line 117 1395871] Train: [13/100][480/1462] Data 0.002 (0.002) Batch 3.132 (2.880) Remain 102:32:33 loss: 0.4148 Lr: 0.00200
[2025-09-06 13:17:40,061 INFO misc.py line 117 1395871] Train: [13/100][481/1462] Data 0.002 (0.002) Batch 2.884 (2.880) Remain 102:32:31 loss: 0.4583 Lr: 0.00200
[2025-09-06 13:17:42,947 INFO misc.py line 117 1395871] Train: [13/100][482/1462] Data 0.002 (0.002) Batch 2.886 (2.880) Remain 102:32:30 loss: 0.4248 Lr: 0.00200
[2025-09-06 13:17:45,793 INFO misc.py line 117 1395871] Train: [13/100][483/1462] Data 0.002 (0.002) Batch 2.846 (2.880) Remain 102:32:18 loss: 0.2402 Lr: 0.00200
[2025-09-06 13:17:49,047 INFO misc.py line 117 1395871] Train: [13/100][484/1462] Data 0.002 (0.002) Batch 3.254 (2.881) Remain 102:33:54 loss: 0.5493 Lr: 0.00200
[2025-09-06 13:17:51,795 INFO misc.py line 117 1395871] Train: [13/100][485/1462] Data 0.002 (0.002) Batch 2.748 (2.881) Remain 102:33:16 loss: 0.2398 Lr: 0.00200
[2025-09-06 13:17:54,682 INFO misc.py line 117 1395871] Train: [13/100][486/1462] Data 0.002 (0.002) Batch 2.888 (2.881) Remain 102:33:15 loss: 0.2644 Lr: 0.00200
[2025-09-06 13:17:57,536 INFO misc.py line 117 1395871] Train: [13/100][487/1462] Data 0.002 (0.002) Batch 2.854 (2.880) Remain 102:33:05 loss: 0.6810 Lr: 0.00200
[2025-09-06 13:18:00,047 INFO misc.py line 117 1395871] Train: [13/100][488/1462] Data 0.002 (0.002) Batch 2.511 (2.880) Remain 102:31:25 loss: 0.2629 Lr: 0.00200
[2025-09-06 13:18:03,326 INFO misc.py line 117 1395871] Train: [13/100][489/1462] Data 0.002 (0.002) Batch 3.279 (2.881) Remain 102:33:07 loss: 0.3095 Lr: 0.00200
[2025-09-06 13:18:06,067 INFO misc.py line 117 1395871] Train: [13/100][490/1462] Data 0.002 (0.002) Batch 2.741 (2.880) Remain 102:32:28 loss: 0.3054 Lr: 0.00200
[2025-09-06 13:18:09,900 INFO misc.py line 117 1395871] Train: [13/100][491/1462] Data 0.002 (0.002) Batch 3.832 (2.882) Remain 102:36:35 loss: 0.3684 Lr: 0.00200
[2025-09-06 13:18:12,961 INFO misc.py line 117 1395871] Train: [13/100][492/1462] Data 0.001 (0.002) Batch 3.061 (2.883) Remain 102:37:19 loss: 0.3831 Lr: 0.00200
[2025-09-06 13:18:15,756 INFO misc.py line 117 1395871] Train: [13/100][493/1462] Data 0.002 (0.002) Batch 2.795 (2.882) Remain 102:36:53 loss: 0.3913 Lr: 0.00200
[2025-09-06 13:18:18,441 INFO misc.py line 117 1395871] Train: [13/100][494/1462] Data 0.001 (0.002) Batch 2.685 (2.882) Remain 102:35:59 loss: 0.5679 Lr: 0.00200
[2025-09-06 13:18:21,355 INFO misc.py line 117 1395871] Train: [13/100][495/1462] Data 0.002 (0.002) Batch 2.913 (2.882) Remain 102:36:04 loss: 0.4759 Lr: 0.00200
[2025-09-06 13:18:24,371 INFO misc.py line 117 1395871] Train: [13/100][496/1462] Data 0.002 (0.002) Batch 3.016 (2.882) Remain 102:36:36 loss: 0.3070 Lr: 0.00200
[2025-09-06 13:18:27,448 INFO misc.py line 117 1395871] Train: [13/100][497/1462] Data 0.001 (0.002) Batch 3.078 (2.883) Remain 102:37:24 loss: 0.6030 Lr: 0.00200
[2025-09-06 13:18:30,073 INFO misc.py line 117 1395871] Train: [13/100][498/1462] Data 0.002 (0.002) Batch 2.624 (2.882) Remain 102:36:14 loss: 0.6357 Lr: 0.00200
[2025-09-06 13:18:33,181 INFO misc.py line 117 1395871] Train: [13/100][499/1462] Data 0.002 (0.002) Batch 3.108 (2.883) Remain 102:37:10 loss: 0.4093 Lr: 0.00200
[2025-09-06 13:18:36,390 INFO misc.py line 117 1395871] Train: [13/100][500/1462] Data 0.002 (0.002) Batch 3.210 (2.883) Remain 102:38:31 loss: 0.3552 Lr: 0.00200
[2025-09-06 13:18:39,465 INFO misc.py line 117 1395871] Train: [13/100][501/1462] Data 0.002 (0.002) Batch 3.075 (2.884) Remain 102:39:17 loss: 0.7468 Lr: 0.00200
[2025-09-06 13:18:42,618 INFO misc.py line 117 1395871] Train: [13/100][502/1462] Data 0.002 (0.002) Batch 3.153 (2.884) Remain 102:40:24 loss: 0.7273 Lr: 0.00200
[2025-09-06 13:18:45,782 INFO misc.py line 117 1395871] Train: [13/100][503/1462] Data 0.002 (0.002) Batch 3.164 (2.885) Remain 102:41:32 loss: 0.7617 Lr: 0.00200
[2025-09-06 13:18:49,345 INFO misc.py line 117 1395871] Train: [13/100][504/1462] Data 0.001 (0.002) Batch 3.563 (2.886) Remain 102:44:23 loss: 0.3620 Lr: 0.00200
[2025-09-06 13:18:52,139 INFO misc.py line 117 1395871] Train: [13/100][505/1462] Data 0.002 (0.002) Batch 2.793 (2.886) Remain 102:43:56 loss: 0.1969 Lr: 0.00200
[2025-09-06 13:18:54,785 INFO misc.py line 117 1395871] Train: [13/100][506/1462] Data 0.002 (0.002) Batch 2.646 (2.885) Remain 102:42:53 loss: 0.5018 Lr: 0.00200
[2025-09-06 13:18:57,626 INFO misc.py line 117 1395871] Train: [13/100][507/1462] Data 0.002 (0.002) Batch 2.841 (2.885) Remain 102:42:38 loss: 0.7807 Lr: 0.00200
[2025-09-06 13:19:00,383 INFO misc.py line 117 1395871] Train: [13/100][508/1462] Data 0.001 (0.002) Batch 2.758 (2.885) Remain 102:42:03 loss: 0.3640 Lr: 0.00200
[2025-09-06 13:19:03,228 INFO misc.py line 117 1395871] Train: [13/100][509/1462] Data 0.002 (0.002) Batch 2.845 (2.885) Remain 102:41:50 loss: 0.4863 Lr: 0.00200
[2025-09-06 13:19:06,204 INFO misc.py line 117 1395871] Train: [13/100][510/1462] Data 0.002 (0.002) Batch 2.976 (2.885) Remain 102:42:10 loss: 0.2680 Lr: 0.00200
[2025-09-06 13:19:08,700 INFO misc.py line 117 1395871] Train: [13/100][511/1462] Data 0.001 (0.002) Batch 2.496 (2.884) Remain 102:40:29 loss: 0.2872 Lr: 0.00200
[2025-09-06 13:19:11,607 INFO misc.py line 117 1395871] Train: [13/100][512/1462] Data 0.001 (0.002) Batch 2.907 (2.885) Remain 102:40:32 loss: 0.5959 Lr: 0.00200
[2025-09-06 13:19:14,356 INFO misc.py line 117 1395871] Train: [13/100][513/1462] Data 0.002 (0.002) Batch 2.748 (2.884) Remain 102:39:55 loss: 0.3618 Lr: 0.00200
[2025-09-06 13:19:17,209 INFO misc.py line 117 1395871] Train: [13/100][514/1462] Data 0.002 (0.002) Batch 2.853 (2.884) Remain 102:39:44 loss: 0.3975 Lr: 0.00200
[2025-09-06 13:19:20,325 INFO misc.py line 117 1395871] Train: [13/100][515/1462] Data 0.002 (0.002) Batch 3.116 (2.885) Remain 102:40:39 loss: 0.2858 Lr: 0.00200
[2025-09-06 13:19:23,108 INFO misc.py line 117 1395871] Train: [13/100][516/1462] Data 0.001 (0.002) Batch 2.783 (2.884) Remain 102:40:11 loss: 0.2865 Lr: 0.00200
[2025-09-06 13:19:26,109 INFO misc.py line 117 1395871] Train: [13/100][517/1462] Data 0.001 (0.002) Batch 3.001 (2.885) Remain 102:40:37 loss: 0.4115 Lr: 0.00200
[2025-09-06 13:19:28,967 INFO misc.py line 117 1395871] Train: [13/100][518/1462] Data 0.001 (0.002) Batch 2.858 (2.885) Remain 102:40:28 loss: 0.4933 Lr: 0.00200
[2025-09-06 13:19:31,971 INFO misc.py line 117 1395871] Train: [13/100][519/1462] Data 0.001 (0.002) Batch 3.004 (2.885) Remain 102:40:54 loss: 0.6326 Lr: 0.00200
[2025-09-06 13:19:35,051 INFO misc.py line 117 1395871] Train: [13/100][520/1462] Data 0.002 (0.002) Batch 3.080 (2.885) Remain 102:41:40 loss: 0.3118 Lr: 0.00200
[2025-09-06 13:19:37,777 INFO misc.py line 117 1395871] Train: [13/100][521/1462] Data 0.001 (0.002) Batch 2.726 (2.885) Remain 102:40:58 loss: 0.5546 Lr: 0.00200
[2025-09-06 13:19:40,741 INFO misc.py line 117 1395871] Train: [13/100][522/1462] Data 0.001 (0.002) Batch 2.964 (2.885) Remain 102:41:14 loss: 0.5975 Lr: 0.00200
[2025-09-06 13:19:43,566 INFO misc.py line 117 1395871] Train: [13/100][523/1462] Data 0.001 (0.002) Batch 2.825 (2.885) Remain 102:40:56 loss: 0.5210 Lr: 0.00200
[2025-09-06 13:19:46,462 INFO misc.py line 117 1395871] Train: [13/100][524/1462] Data 0.001 (0.002) Batch 2.896 (2.885) Remain 102:40:56 loss: 0.4649 Lr: 0.00200
[2025-09-06 13:19:49,184 INFO misc.py line 117 1395871] Train: [13/100][525/1462] Data 0.002 (0.002) Batch 2.723 (2.885) Remain 102:40:14 loss: 0.3674 Lr: 0.00200
[2025-09-06 13:19:52,341 INFO misc.py line 117 1395871] Train: [13/100][526/1462] Data 0.002 (0.002) Batch 3.156 (2.885) Remain 102:41:17 loss: 0.6035 Lr: 0.00200
[2025-09-06 13:19:55,570 INFO misc.py line 117 1395871] Train: [13/100][527/1462] Data 0.002 (0.002) Batch 3.229 (2.886) Remain 102:42:38 loss: 0.2135 Lr: 0.00200
[2025-09-06 13:19:58,279 INFO misc.py line 117 1395871] Train: [13/100][528/1462] Data 0.002 (0.002) Batch 2.709 (2.885) Remain 102:41:52 loss: 0.4607 Lr: 0.00200
[2025-09-06 13:20:01,261 INFO misc.py line 117 1395871] Train: [13/100][529/1462] Data 0.001 (0.002) Batch 2.982 (2.886) Remain 102:42:13 loss: 0.2642 Lr: 0.00200
[2025-09-06 13:20:04,178 INFO misc.py line 117 1395871] Train: [13/100][530/1462] Data 0.002 (0.002) Batch 2.917 (2.886) Remain 102:42:18 loss: 0.4475 Lr: 0.00200
[2025-09-06 13:20:06,928 INFO misc.py line 117 1395871] Train: [13/100][531/1462] Data 0.001 (0.002) Batch 2.750 (2.885) Remain 102:41:42 loss: 0.4400 Lr: 0.00200
[2025-09-06 13:20:09,875 INFO misc.py line 117 1395871] Train: [13/100][532/1462] Data 0.001 (0.002) Batch 2.947 (2.886) Remain 102:41:54 loss: 0.5227 Lr: 0.00200
[2025-09-06 13:20:12,840 INFO misc.py line 117 1395871] Train: [13/100][533/1462] Data 0.002 (0.002) Batch 2.964 (2.886) Remain 102:42:10 loss: 0.4741 Lr: 0.00200
[2025-09-06 13:20:15,363 INFO misc.py line 117 1395871] Train: [13/100][534/1462] Data 0.001 (0.002) Batch 2.523 (2.885) Remain 102:40:40 loss: 0.3134 Lr: 0.00200
[2025-09-06 13:20:18,253 INFO misc.py line 117 1395871] Train: [13/100][535/1462] Data 0.001 (0.002) Batch 2.890 (2.885) Remain 102:40:38 loss: 0.2850 Lr: 0.00200
[2025-09-06 13:20:21,260 INFO misc.py line 117 1395871] Train: [13/100][536/1462] Data 0.002 (0.002) Batch 3.007 (2.885) Remain 102:41:05 loss: 0.5298 Lr: 0.00200
[2025-09-06 13:20:24,138 INFO misc.py line 117 1395871] Train: [13/100][537/1462] Data 0.002 (0.002) Batch 2.879 (2.885) Remain 102:41:00 loss: 0.5002 Lr: 0.00200
[2025-09-06 13:20:27,104 INFO misc.py line 117 1395871] Train: [13/100][538/1462] Data 0.002 (0.002) Batch 2.966 (2.885) Remain 102:41:16 loss: 0.3692 Lr: 0.00200
[2025-09-06 13:20:29,677 INFO misc.py line 117 1395871] Train: [13/100][539/1462] Data 0.002 (0.002) Batch 2.573 (2.885) Remain 102:39:59 loss: 0.2816 Lr: 0.00200
[2025-09-06 13:20:32,958 INFO misc.py line 117 1395871] Train: [13/100][540/1462] Data 0.001 (0.002) Batch 3.280 (2.886) Remain 102:41:30 loss: 0.3285 Lr: 0.00200
[2025-09-06 13:20:35,746 INFO misc.py line 117 1395871] Train: [13/100][541/1462] Data 0.002 (0.002) Batch 2.789 (2.885) Remain 102:41:04 loss: 0.3477 Lr: 0.00200
[2025-09-06 13:20:38,829 INFO misc.py line 117 1395871] Train: [13/100][542/1462] Data 0.001 (0.002) Batch 3.082 (2.886) Remain 102:41:48 loss: 0.3027 Lr: 0.00200
[2025-09-06 13:20:41,507 INFO misc.py line 117 1395871] Train: [13/100][543/1462] Data 0.002 (0.002) Batch 2.678 (2.885) Remain 102:40:56 loss: 0.4255 Lr: 0.00200
[2025-09-06 13:20:44,429 INFO misc.py line 117 1395871] Train: [13/100][544/1462] Data 0.002 (0.002) Batch 2.922 (2.885) Remain 102:41:02 loss: 0.2955 Lr: 0.00200
[2025-09-06 13:20:47,163 INFO misc.py line 117 1395871] Train: [13/100][545/1462] Data 0.002 (0.002) Batch 2.734 (2.885) Remain 102:40:23 loss: 0.2790 Lr: 0.00200
[2025-09-06 13:20:50,498 INFO misc.py line 117 1395871] Train: [13/100][546/1462] Data 0.002 (0.002) Batch 3.334 (2.886) Remain 102:42:06 loss: 0.6479 Lr: 0.00200
[2025-09-06 13:20:53,216 INFO misc.py line 117 1395871] Train: [13/100][547/1462] Data 0.001 (0.002) Batch 2.718 (2.886) Remain 102:41:24 loss: 0.7780 Lr: 0.00200
[2025-09-06 13:20:55,784 INFO misc.py line 117 1395871] Train: [13/100][548/1462] Data 0.002 (0.002) Batch 2.568 (2.885) Remain 102:40:07 loss: 0.5144 Lr: 0.00200
[2025-09-06 13:20:58,883 INFO misc.py line 117 1395871] Train: [13/100][549/1462] Data 0.001 (0.002) Batch 3.099 (2.886) Remain 102:40:54 loss: 0.6733 Lr: 0.00200
[2025-09-06 13:21:01,439 INFO misc.py line 117 1395871] Train: [13/100][550/1462] Data 0.001 (0.002) Batch 2.556 (2.885) Remain 102:39:34 loss: 0.3903 Lr: 0.00200
[2025-09-06 13:21:04,463 INFO misc.py line 117 1395871] Train: [13/100][551/1462] Data 0.002 (0.002) Batch 3.024 (2.885) Remain 102:40:03 loss: 0.2998 Lr: 0.00200
[2025-09-06 13:21:07,073 INFO misc.py line 117 1395871] Train: [13/100][552/1462] Data 0.002 (0.002) Batch 2.611 (2.885) Remain 102:38:56 loss: 0.4101 Lr: 0.00200
[2025-09-06 13:21:10,198 INFO misc.py line 117 1395871] Train: [13/100][553/1462] Data 0.001 (0.002) Batch 3.125 (2.885) Remain 102:39:49 loss: 0.2339 Lr: 0.00200
[2025-09-06 13:21:12,978 INFO misc.py line 117 1395871] Train: [13/100][554/1462] Data 0.002 (0.002) Batch 2.780 (2.885) Remain 102:39:22 loss: 0.3020 Lr: 0.00200
[2025-09-06 13:21:15,896 INFO misc.py line 117 1395871] Train: [13/100][555/1462] Data 0.002 (0.002) Batch 2.917 (2.885) Remain 102:39:27 loss: 0.2513 Lr: 0.00200
[2025-09-06 13:21:18,477 INFO misc.py line 117 1395871] Train: [13/100][556/1462] Data 0.002 (0.002) Batch 2.582 (2.884) Remain 102:38:14 loss: 0.3216 Lr: 0.00200
[2025-09-06 13:21:21,288 INFO misc.py line 117 1395871] Train: [13/100][557/1462] Data 0.001 (0.002) Batch 2.811 (2.884) Remain 102:37:54 loss: 0.3345 Lr: 0.00200
[2025-09-06 13:21:24,107 INFO misc.py line 117 1395871] Train: [13/100][558/1462] Data 0.001 (0.002) Batch 2.819 (2.884) Remain 102:37:36 loss: 0.3670 Lr: 0.00200
[2025-09-06 13:21:26,739 INFO misc.py line 117 1395871] Train: [13/100][559/1462] Data 0.002 (0.002) Batch 2.632 (2.884) Remain 102:36:35 loss: 0.3900 Lr: 0.00200
[2025-09-06 13:21:29,668 INFO misc.py line 117 1395871] Train: [13/100][560/1462] Data 0.002 (0.002) Batch 2.929 (2.884) Remain 102:36:42 loss: 0.5331 Lr: 0.00200
[2025-09-06 13:21:32,444 INFO misc.py line 117 1395871] Train: [13/100][561/1462] Data 0.002 (0.002) Batch 2.776 (2.884) Remain 102:36:15 loss: 0.4418 Lr: 0.00200
[2025-09-06 13:21:35,132 INFO misc.py line 117 1395871] Train: [13/100][562/1462] Data 0.002 (0.002) Batch 2.687 (2.883) Remain 102:35:27 loss: 0.4080 Lr: 0.00200
[2025-09-06 13:21:38,140 INFO misc.py line 117 1395871] Train: [13/100][563/1462] Data 0.002 (0.002) Batch 3.009 (2.883) Remain 102:35:53 loss: 0.3019 Lr: 0.00200
[2025-09-06 13:21:41,089 INFO misc.py line 117 1395871] Train: [13/100][564/1462] Data 0.002 (0.002) Batch 2.949 (2.884) Remain 102:36:05 loss: 0.2172 Lr: 0.00200
[2025-09-06 13:21:43,894 INFO misc.py line 117 1395871] Train: [13/100][565/1462] Data 0.001 (0.002) Batch 2.806 (2.883) Remain 102:35:44 loss: 0.3938 Lr: 0.00200
[2025-09-06 13:21:46,956 INFO misc.py line 117 1395871] Train: [13/100][566/1462] Data 0.001 (0.002) Batch 3.062 (2.884) Remain 102:36:22 loss: 0.3653 Lr: 0.00200
[2025-09-06 13:21:49,887 INFO misc.py line 117 1395871] Train: [13/100][567/1462] Data 0.002 (0.002) Batch 2.931 (2.884) Remain 102:36:30 loss: 0.5552 Lr: 0.00200
[2025-09-06 13:21:52,597 INFO misc.py line 117 1395871] Train: [13/100][568/1462] Data 0.002 (0.002) Batch 2.709 (2.884) Remain 102:35:47 loss: 0.3537 Lr: 0.00200
[2025-09-06 13:21:55,358 INFO misc.py line 117 1395871] Train: [13/100][569/1462] Data 0.002 (0.002) Batch 2.761 (2.883) Remain 102:35:16 loss: 0.5645 Lr: 0.00200
[2025-09-06 13:21:58,536 INFO misc.py line 117 1395871] Train: [13/100][570/1462] Data 0.002 (0.002) Batch 3.179 (2.884) Remain 102:36:20 loss: 0.5691 Lr: 0.00200
[2025-09-06 13:22:01,502 INFO misc.py line 117 1395871] Train: [13/100][571/1462] Data 0.001 (0.002) Batch 2.966 (2.884) Remain 102:36:36 loss: 0.4842 Lr: 0.00200
[2025-09-06 13:22:04,356 INFO misc.py line 117 1395871] Train: [13/100][572/1462] Data 0.002 (0.002) Batch 2.853 (2.884) Remain 102:36:26 loss: 0.4420 Lr: 0.00200
[2025-09-06 13:22:07,050 INFO misc.py line 117 1395871] Train: [13/100][573/1462] Data 0.002 (0.002) Batch 2.695 (2.884) Remain 102:35:41 loss: 0.3800 Lr: 0.00200
[2025-09-06 13:22:10,079 INFO misc.py line 117 1395871] Train: [13/100][574/1462] Data 0.001 (0.002) Batch 3.029 (2.884) Remain 102:36:10 loss: 0.4722 Lr: 0.00200
[2025-09-06 13:22:12,682 INFO misc.py line 117 1395871] Train: [13/100][575/1462] Data 0.002 (0.002) Batch 2.603 (2.883) Remain 102:35:05 loss: 0.4563 Lr: 0.00200
[2025-09-06 13:22:15,831 INFO misc.py line 117 1395871] Train: [13/100][576/1462] Data 0.002 (0.002) Batch 3.149 (2.884) Remain 102:36:01 loss: 0.2854 Lr: 0.00200
[2025-09-06 13:22:18,605 INFO misc.py line 117 1395871] Train: [13/100][577/1462] Data 0.002 (0.002) Batch 2.774 (2.884) Remain 102:35:34 loss: 0.4084 Lr: 0.00200
[2025-09-06 13:22:21,446 INFO misc.py line 117 1395871] Train: [13/100][578/1462] Data 0.001 (0.002) Batch 2.841 (2.884) Remain 102:35:21 loss: 0.4386 Lr: 0.00200
[2025-09-06 13:22:24,463 INFO misc.py line 117 1395871] Train: [13/100][579/1462] Data 0.002 (0.002) Batch 3.018 (2.884) Remain 102:35:48 loss: 0.2236 Lr: 0.00200
[2025-09-06 13:22:27,438 INFO misc.py line 117 1395871] Train: [13/100][580/1462] Data 0.001 (0.002) Batch 2.975 (2.884) Remain 102:36:06 loss: 0.9426 Lr: 0.00200
[2025-09-06 13:22:30,333 INFO misc.py line 117 1395871] Train: [13/100][581/1462] Data 0.001 (0.002) Batch 2.895 (2.884) Remain 102:36:05 loss: 0.4691 Lr: 0.00200
[2025-09-06 13:22:33,357 INFO misc.py line 117 1395871] Train: [13/100][582/1462] Data 0.001 (0.002) Batch 3.024 (2.884) Remain 102:36:33 loss: 0.4441 Lr: 0.00200
[2025-09-06 13:22:36,197 INFO misc.py line 117 1395871] Train: [13/100][583/1462] Data 0.002 (0.002) Batch 2.840 (2.884) Remain 102:36:20 loss: 0.7379 Lr: 0.00200
[2025-09-06 13:22:39,275 INFO misc.py line 117 1395871] Train: [13/100][584/1462] Data 0.001 (0.002) Batch 3.078 (2.884) Remain 102:37:00 loss: 0.6777 Lr: 0.00200
[2025-09-06 13:22:42,007 INFO misc.py line 117 1395871] Train: [13/100][585/1462] Data 0.002 (0.002) Batch 2.732 (2.884) Remain 102:36:24 loss: 0.5329 Lr: 0.00200
[2025-09-06 13:22:44,839 INFO misc.py line 117 1395871] Train: [13/100][586/1462] Data 0.001 (0.002) Batch 2.832 (2.884) Remain 102:36:10 loss: 0.2396 Lr: 0.00200
[2025-09-06 13:22:47,538 INFO misc.py line 117 1395871] Train: [13/100][587/1462] Data 0.002 (0.002) Batch 2.699 (2.884) Remain 102:35:26 loss: 0.4741 Lr: 0.00200
[2025-09-06 13:22:50,562 INFO misc.py line 117 1395871] Train: [13/100][588/1462] Data 0.001 (0.002) Batch 3.023 (2.884) Remain 102:35:54 loss: 0.3755 Lr: 0.00200
[2025-09-06 13:22:53,545 INFO misc.py line 117 1395871] Train: [13/100][589/1462] Data 0.001 (0.002) Batch 2.984 (2.884) Remain 102:36:13 loss: 0.2485 Lr: 0.00200
[2025-09-06 13:22:56,285 INFO misc.py line 117 1395871] Train: [13/100][590/1462] Data 0.002 (0.002) Batch 2.740 (2.884) Remain 102:35:38 loss: 1.0587 Lr: 0.00200
[2025-09-06 13:22:59,186 INFO misc.py line 117 1395871] Train: [13/100][591/1462] Data 0.002 (0.002) Batch 2.901 (2.884) Remain 102:35:39 loss: 0.9037 Lr: 0.00200
[2025-09-06 13:23:02,183 INFO misc.py line 117 1395871] Train: [13/100][592/1462] Data 0.002 (0.002) Batch 2.996 (2.884) Remain 102:36:01 loss: 0.5597 Lr: 0.00200
[2025-09-06 13:23:04,954 INFO misc.py line 117 1395871] Train: [13/100][593/1462] Data 0.001 (0.002) Batch 2.772 (2.884) Remain 102:35:33 loss: 0.5922 Lr: 0.00200
[2025-09-06 13:23:08,301 INFO misc.py line 117 1395871] Train: [13/100][594/1462] Data 0.001 (0.002) Batch 3.347 (2.885) Remain 102:37:11 loss: 0.3491 Lr: 0.00200
[2025-09-06 13:23:11,161 INFO misc.py line 117 1395871] Train: [13/100][595/1462] Data 0.002 (0.002) Batch 2.861 (2.885) Remain 102:37:03 loss: 0.6714 Lr: 0.00200
[2025-09-06 13:23:13,913 INFO misc.py line 117 1395871] Train: [13/100][596/1462] Data 0.001 (0.002) Batch 2.752 (2.885) Remain 102:36:31 loss: 0.4962 Lr: 0.00200
[2025-09-06 13:23:16,711 INFO misc.py line 117 1395871] Train: [13/100][597/1462] Data 0.001 (0.002) Batch 2.798 (2.884) Remain 102:36:09 loss: 0.3053 Lr: 0.00200
[2025-09-06 13:23:19,634 INFO misc.py line 117 1395871] Train: [13/100][598/1462] Data 0.001 (0.002) Batch 2.923 (2.884) Remain 102:36:15 loss: 0.5877 Lr: 0.00200
[2025-09-06 13:23:22,595 INFO misc.py line 117 1395871] Train: [13/100][599/1462] Data 0.001 (0.002) Batch 2.961 (2.885) Remain 102:36:28 loss: 0.5041 Lr: 0.00200
[2025-09-06 13:23:25,600 INFO misc.py line 117 1395871] Train: [13/100][600/1462] Data 0.002 (0.002) Batch 3.005 (2.885) Remain 102:36:51 loss: 0.5283 Lr: 0.00200
[2025-09-06 13:23:28,686 INFO misc.py line 117 1395871] Train: [13/100][601/1462] Data 0.001 (0.002) Batch 3.086 (2.885) Remain 102:37:32 loss: 0.3848 Lr: 0.00200
[2025-09-06 13:23:31,513 INFO misc.py line 117 1395871] Train: [13/100][602/1462] Data 0.001 (0.002) Batch 2.827 (2.885) Remain 102:37:16 loss: 0.6572 Lr: 0.00200
[2025-09-06 13:23:34,518 INFO misc.py line 117 1395871] Train: [13/100][603/1462] Data 0.002 (0.002) Batch 3.005 (2.885) Remain 102:37:39 loss: 0.2983 Lr: 0.00200
[2025-09-06 13:23:37,460 INFO misc.py line 117 1395871] Train: [13/100][604/1462] Data 0.002 (0.002) Batch 2.941 (2.885) Remain 102:37:48 loss: 0.5873 Lr: 0.00200
[2025-09-06 13:23:40,402 INFO misc.py line 117 1395871] Train: [13/100][605/1462] Data 0.002 (0.002) Batch 2.942 (2.885) Remain 102:37:57 loss: 0.3462 Lr: 0.00200
[2025-09-06 13:23:43,115 INFO misc.py line 117 1395871] Train: [13/100][606/1462] Data 0.002 (0.002) Batch 2.713 (2.885) Remain 102:37:18 loss: 0.3150 Lr: 0.00200
[2025-09-06 13:23:46,097 INFO misc.py line 117 1395871] Train: [13/100][607/1462] Data 0.002 (0.002) Batch 2.982 (2.885) Remain 102:37:36 loss: 0.4353 Lr: 0.00200
[2025-09-06 13:23:49,180 INFO misc.py line 117 1395871] Train: [13/100][608/1462] Data 0.001 (0.002) Batch 3.083 (2.886) Remain 102:38:15 loss: 0.3780 Lr: 0.00200
[2025-09-06 13:23:51,989 INFO misc.py line 117 1395871] Train: [13/100][609/1462] Data 0.002 (0.002) Batch 2.809 (2.885) Remain 102:37:56 loss: 0.2666 Lr: 0.00200
[2025-09-06 13:23:55,070 INFO misc.py line 117 1395871] Train: [13/100][610/1462] Data 0.001 (0.002) Batch 3.081 (2.886) Remain 102:38:34 loss: 0.2733 Lr: 0.00200
[2025-09-06 13:23:57,824 INFO misc.py line 117 1395871] Train: [13/100][611/1462] Data 0.002 (0.002) Batch 2.754 (2.886) Remain 102:38:03 loss: 0.6427 Lr: 0.00200
[2025-09-06 13:24:00,646 INFO misc.py line 117 1395871] Train: [13/100][612/1462] Data 0.002 (0.002) Batch 2.822 (2.885) Remain 102:37:47 loss: 0.2677 Lr: 0.00200
[2025-09-06 13:24:03,275 INFO misc.py line 117 1395871] Train: [13/100][613/1462] Data 0.002 (0.002) Batch 2.629 (2.885) Remain 102:36:50 loss: 0.3556 Lr: 0.00200
[2025-09-06 13:24:06,178 INFO misc.py line 117 1395871] Train: [13/100][614/1462] Data 0.001 (0.002) Batch 2.903 (2.885) Remain 102:36:51 loss: 0.5046 Lr: 0.00200
[2025-09-06 13:24:08,973 INFO misc.py line 117 1395871] Train: [13/100][615/1462] Data 0.002 (0.002) Batch 2.795 (2.885) Remain 102:36:29 loss: 0.3395 Lr: 0.00200
[2025-09-06 13:24:12,058 INFO misc.py line 117 1395871] Train: [13/100][616/1462] Data 0.001 (0.002) Batch 3.085 (2.885) Remain 102:37:08 loss: 0.8377 Lr: 0.00200
[2025-09-06 13:24:14,793 INFO misc.py line 117 1395871] Train: [13/100][617/1462] Data 0.001 (0.002) Batch 2.735 (2.885) Remain 102:36:34 loss: 0.9886 Lr: 0.00200
[2025-09-06 13:24:17,614 INFO misc.py line 117 1395871] Train: [13/100][618/1462] Data 0.001 (0.002) Batch 2.822 (2.885) Remain 102:36:18 loss: 0.2918 Lr: 0.00200
[2025-09-06 13:24:20,582 INFO misc.py line 117 1395871] Train: [13/100][619/1462] Data 0.002 (0.002) Batch 2.968 (2.885) Remain 102:36:32 loss: 0.2914 Lr: 0.00200
[2025-09-06 13:24:23,824 INFO misc.py line 117 1395871] Train: [13/100][620/1462] Data 0.001 (0.002) Batch 3.242 (2.886) Remain 102:37:43 loss: 0.3471 Lr: 0.00200
[2025-09-06 13:24:26,729 INFO misc.py line 117 1395871] Train: [13/100][621/1462] Data 0.001 (0.002) Batch 2.905 (2.886) Remain 102:37:45 loss: 0.7719 Lr: 0.00200
[2025-09-06 13:24:29,224 INFO misc.py line 117 1395871] Train: [13/100][622/1462] Data 0.001 (0.002) Batch 2.495 (2.885) Remain 102:36:21 loss: 0.4595 Lr: 0.00200
[2025-09-06 13:24:32,349 INFO misc.py line 117 1395871] Train: [13/100][623/1462] Data 0.001 (0.002) Batch 3.124 (2.885) Remain 102:37:07 loss: 0.6721 Lr: 0.00200
[2025-09-06 13:24:35,025 INFO misc.py line 117 1395871] Train: [13/100][624/1462] Data 0.002 (0.002) Batch 2.676 (2.885) Remain 102:36:21 loss: 0.7854 Lr: 0.00200
[2025-09-06 13:24:37,916 INFO misc.py line 117 1395871] Train: [13/100][625/1462] Data 0.001 (0.002) Batch 2.891 (2.885) Remain 102:36:20 loss: 0.5246 Lr: 0.00200
[2025-09-06 13:24:40,583 INFO misc.py line 117 1395871] Train: [13/100][626/1462] Data 0.001 (0.002) Batch 2.667 (2.885) Remain 102:35:32 loss: 0.5283 Lr: 0.00200
[2025-09-06 13:24:43,293 INFO misc.py line 117 1395871] Train: [13/100][627/1462] Data 0.002 (0.002) Batch 2.710 (2.884) Remain 102:34:53 loss: 0.3291 Lr: 0.00200
[2025-09-06 13:24:46,814 INFO misc.py line 117 1395871] Train: [13/100][628/1462] Data 0.001 (0.002) Batch 3.521 (2.885) Remain 102:37:01 loss: 0.3002 Lr: 0.00200
[2025-09-06 13:24:49,665 INFO misc.py line 117 1395871] Train: [13/100][629/1462] Data 0.001 (0.002) Batch 2.851 (2.885) Remain 102:36:51 loss: 0.6128 Lr: 0.00200
[2025-09-06 13:24:52,189 INFO misc.py line 117 1395871] Train: [13/100][630/1462] Data 0.001 (0.002) Batch 2.524 (2.885) Remain 102:35:34 loss: 0.3679 Lr: 0.00200
[2025-09-06 13:24:55,356 INFO misc.py line 117 1395871] Train: [13/100][631/1462] Data 0.001 (0.002) Batch 3.167 (2.885) Remain 102:36:29 loss: 0.3421 Lr: 0.00200
[2025-09-06 13:24:57,974 INFO misc.py line 117 1395871] Train: [13/100][632/1462] Data 0.002 (0.002) Batch 2.617 (2.885) Remain 102:35:32 loss: 0.2711 Lr: 0.00200
[2025-09-06 13:25:00,713 INFO misc.py line 117 1395871] Train: [13/100][633/1462] Data 0.001 (0.002) Batch 2.739 (2.885) Remain 102:34:59 loss: 0.6362 Lr: 0.00200
[2025-09-06 13:25:03,520 INFO misc.py line 117 1395871] Train: [13/100][634/1462] Data 0.002 (0.002) Batch 2.806 (2.885) Remain 102:34:40 loss: 0.3710 Lr: 0.00200
[2025-09-06 13:25:06,612 INFO misc.py line 117 1395871] Train: [13/100][635/1462] Data 0.002 (0.002) Batch 3.093 (2.885) Remain 102:35:20 loss: 0.4756 Lr: 0.00200
[2025-09-06 13:25:09,544 INFO misc.py line 117 1395871] Train: [13/100][636/1462] Data 0.002 (0.002) Batch 2.932 (2.885) Remain 102:35:26 loss: 0.5538 Lr: 0.00200
[2025-09-06 13:25:12,897 INFO misc.py line 117 1395871] Train: [13/100][637/1462] Data 0.002 (0.002) Batch 3.353 (2.886) Remain 102:36:58 loss: 0.5925 Lr: 0.00200
[2025-09-06 13:25:15,653 INFO misc.py line 117 1395871] Train: [13/100][638/1462] Data 0.001 (0.002) Batch 2.756 (2.885) Remain 102:36:29 loss: 0.4988 Lr: 0.00200
[2025-09-06 13:25:18,302 INFO misc.py line 117 1395871] Train: [13/100][639/1462] Data 0.002 (0.002) Batch 2.649 (2.885) Remain 102:35:38 loss: 0.3903 Lr: 0.00200
[2025-09-06 13:25:21,155 INFO misc.py line 117 1395871] Train: [13/100][640/1462] Data 0.001 (0.002) Batch 2.853 (2.885) Remain 102:35:29 loss: 0.3931 Lr: 0.00200
[2025-09-06 13:25:24,094 INFO misc.py line 117 1395871] Train: [13/100][641/1462] Data 0.002 (0.002) Batch 2.939 (2.885) Remain 102:35:37 loss: 0.5882 Lr: 0.00200
[2025-09-06 13:25:27,156 INFO misc.py line 117 1395871] Train: [13/100][642/1462] Data 0.002 (0.002) Batch 3.062 (2.885) Remain 102:36:10 loss: 0.5704 Lr: 0.00200
[2025-09-06 13:25:29,880 INFO misc.py line 117 1395871] Train: [13/100][643/1462] Data 0.002 (0.002) Batch 2.723 (2.885) Remain 102:35:34 loss: 0.5297 Lr: 0.00200
[2025-09-06 13:25:32,961 INFO misc.py line 117 1395871] Train: [13/100][644/1462] Data 0.002 (0.002) Batch 3.081 (2.885) Remain 102:36:11 loss: 0.6350 Lr: 0.00200
[2025-09-06 13:25:35,748 INFO misc.py line 117 1395871] Train: [13/100][645/1462] Data 0.001 (0.002) Batch 2.787 (2.885) Remain 102:35:48 loss: 0.4301 Lr: 0.00200
[2025-09-06 13:25:38,510 INFO misc.py line 117 1395871] Train: [13/100][646/1462] Data 0.001 (0.002) Batch 2.762 (2.885) Remain 102:35:21 loss: 0.6334 Lr: 0.00200
[2025-09-06 13:25:41,743 INFO misc.py line 117 1395871] Train: [13/100][647/1462] Data 0.001 (0.002) Batch 3.233 (2.886) Remain 102:36:27 loss: 0.3272 Lr: 0.00200
[2025-09-06 13:25:44,587 INFO misc.py line 117 1395871] Train: [13/100][648/1462] Data 0.001 (0.002) Batch 2.845 (2.886) Remain 102:36:16 loss: 0.3609 Lr: 0.00200
[2025-09-06 13:25:47,097 INFO misc.py line 117 1395871] Train: [13/100][649/1462] Data 0.001 (0.002) Batch 2.510 (2.885) Remain 102:34:58 loss: 0.2988 Lr: 0.00200
[2025-09-06 13:25:49,820 INFO misc.py line 117 1395871] Train: [13/100][650/1462] Data 0.001 (0.002) Batch 2.723 (2.885) Remain 102:34:23 loss: 0.5468 Lr: 0.00200
[2025-09-06 13:25:52,448 INFO misc.py line 117 1395871] Train: [13/100][651/1462] Data 0.001 (0.002) Batch 2.628 (2.884) Remain 102:33:30 loss: 0.3096 Lr: 0.00200
[2025-09-06 13:25:55,450 INFO misc.py line 117 1395871] Train: [13/100][652/1462] Data 0.002 (0.002) Batch 3.002 (2.885) Remain 102:33:50 loss: 0.3251 Lr: 0.00200
[2025-09-06 13:25:58,241 INFO misc.py line 117 1395871] Train: [13/100][653/1462] Data 0.001 (0.002) Batch 2.791 (2.884) Remain 102:33:29 loss: 0.5353 Lr: 0.00200
[2025-09-06 13:26:01,432 INFO misc.py line 117 1395871] Train: [13/100][654/1462] Data 0.001 (0.002) Batch 3.191 (2.885) Remain 102:34:26 loss: 0.3443 Lr: 0.00200
[2025-09-06 13:26:04,423 INFO misc.py line 117 1395871] Train: [13/100][655/1462] Data 0.001 (0.002) Batch 2.991 (2.885) Remain 102:34:44 loss: 0.4773 Lr: 0.00200
[2025-09-06 13:26:07,321 INFO misc.py line 117 1395871] Train: [13/100][656/1462] Data 0.001 (0.002) Batch 2.898 (2.885) Remain 102:34:44 loss: 0.3347 Lr: 0.00200
[2025-09-06 13:26:10,338 INFO misc.py line 117 1395871] Train: [13/100][657/1462] Data 0.001 (0.002) Batch 3.017 (2.885) Remain 102:35:07 loss: 0.5440 Lr: 0.00200
[2025-09-06 13:26:13,267 INFO misc.py line 117 1395871] Train: [13/100][658/1462] Data 0.002 (0.002) Batch 2.930 (2.885) Remain 102:35:13 loss: 0.3211 Lr: 0.00200
[2025-09-06 13:26:15,981 INFO misc.py line 117 1395871] Train: [13/100][659/1462] Data 0.001 (0.002) Batch 2.713 (2.885) Remain 102:34:36 loss: 0.3475 Lr: 0.00200
[2025-09-06 13:26:18,844 INFO misc.py line 117 1395871] Train: [13/100][660/1462] Data 0.002 (0.002) Batch 2.864 (2.885) Remain 102:34:29 loss: 0.5898 Lr: 0.00200
[2025-09-06 13:26:21,929 INFO misc.py line 117 1395871] Train: [13/100][661/1462] Data 0.001 (0.002) Batch 3.085 (2.885) Remain 102:35:05 loss: 0.3183 Lr: 0.00200
[2025-09-06 13:26:25,208 INFO misc.py line 117 1395871] Train: [13/100][662/1462] Data 0.002 (0.002) Batch 3.279 (2.886) Remain 102:36:19 loss: 0.4583 Lr: 0.00200
[2025-09-06 13:26:28,140 INFO misc.py line 117 1395871] Train: [13/100][663/1462] Data 0.001 (0.002) Batch 2.932 (2.886) Remain 102:36:25 loss: 0.6696 Lr: 0.00200
[2025-09-06 13:26:30,990 INFO misc.py line 117 1395871] Train: [13/100][664/1462] Data 0.001 (0.002) Batch 2.849 (2.886) Remain 102:36:15 loss: 0.3437 Lr: 0.00200
[2025-09-06 13:26:33,946 INFO misc.py line 117 1395871] Train: [13/100][665/1462] Data 0.002 (0.002) Batch 2.956 (2.886) Remain 102:36:25 loss: 0.3856 Lr: 0.00200
[2025-09-06 13:26:36,647 INFO misc.py line 117 1395871] Train: [13/100][666/1462] Data 0.001 (0.002) Batch 2.701 (2.886) Remain 102:35:47 loss: 0.3951 Lr: 0.00200
[2025-09-06 13:26:39,462 INFO misc.py line 117 1395871] Train: [13/100][667/1462] Data 0.002 (0.002) Batch 2.815 (2.886) Remain 102:35:30 loss: 0.4241 Lr: 0.00200
[2025-09-06 13:26:42,301 INFO misc.py line 117 1395871] Train: [13/100][668/1462] Data 0.001 (0.002) Batch 2.839 (2.886) Remain 102:35:19 loss: 0.3898 Lr: 0.00200
[2025-09-06 13:26:45,610 INFO misc.py line 117 1395871] Train: [13/100][669/1462] Data 0.002 (0.002) Batch 3.309 (2.886) Remain 102:36:37 loss: 0.2551 Lr: 0.00200
[2025-09-06 13:26:48,274 INFO misc.py line 117 1395871] Train: [13/100][670/1462] Data 0.001 (0.002) Batch 2.665 (2.886) Remain 102:35:52 loss: 0.5505 Lr: 0.00200
[2025-09-06 13:26:50,941 INFO misc.py line 117 1395871] Train: [13/100][671/1462] Data 0.001 (0.002) Batch 2.667 (2.886) Remain 102:35:07 loss: 0.4366 Lr: 0.00200
[2025-09-06 13:26:53,954 INFO misc.py line 117 1395871] Train: [13/100][672/1462] Data 0.002 (0.002) Batch 3.013 (2.886) Remain 102:35:28 loss: 0.4749 Lr: 0.00200
[2025-09-06 13:26:56,666 INFO misc.py line 117 1395871] Train: [13/100][673/1462] Data 0.001 (0.002) Batch 2.712 (2.885) Remain 102:34:52 loss: 0.3650 Lr: 0.00200
[2025-09-06 13:26:59,457 INFO misc.py line 117 1395871] Train: [13/100][674/1462] Data 0.001 (0.002) Batch 2.791 (2.885) Remain 102:34:31 loss: 0.5983 Lr: 0.00200
[2025-09-06 13:27:02,324 INFO misc.py line 117 1395871] Train: [13/100][675/1462] Data 0.001 (0.002) Batch 2.867 (2.885) Remain 102:34:25 loss: 0.5352 Lr: 0.00200
[2025-09-06 13:27:05,238 INFO misc.py line 117 1395871] Train: [13/100][676/1462] Data 0.001 (0.002) Batch 2.914 (2.885) Remain 102:34:27 loss: 0.3848 Lr: 0.00200
[2025-09-06 13:27:08,500 INFO misc.py line 117 1395871] Train: [13/100][677/1462] Data 0.001 (0.002) Batch 3.261 (2.886) Remain 102:35:36 loss: 0.4452 Lr: 0.00200
[2025-09-06 13:27:11,301 INFO misc.py line 117 1395871] Train: [13/100][678/1462] Data 0.001 (0.002) Batch 2.801 (2.886) Remain 102:35:17 loss: 0.4810 Lr: 0.00200
[2025-09-06 13:27:13,877 INFO misc.py line 117 1395871] Train: [13/100][679/1462] Data 0.002 (0.002) Batch 2.576 (2.885) Remain 102:34:16 loss: 0.3020 Lr: 0.00200
[2025-09-06 13:27:16,812 INFO misc.py line 117 1395871] Train: [13/100][680/1462] Data 0.002 (0.002) Batch 2.934 (2.885) Remain 102:34:22 loss: 0.3631 Lr: 0.00200
[2025-09-06 13:27:20,262 INFO misc.py line 117 1395871] Train: [13/100][681/1462] Data 0.002 (0.002) Batch 3.450 (2.886) Remain 102:36:06 loss: 0.5385 Lr: 0.00200
[2025-09-06 13:27:23,294 INFO misc.py line 117 1395871] Train: [13/100][682/1462] Data 0.002 (0.002) Batch 3.032 (2.886) Remain 102:36:30 loss: 0.4700 Lr: 0.00200
[2025-09-06 13:27:26,116 INFO misc.py line 117 1395871] Train: [13/100][683/1462] Data 0.002 (0.002) Batch 2.823 (2.886) Remain 102:36:15 loss: 0.4574 Lr: 0.00200
[2025-09-06 13:27:28,857 INFO misc.py line 117 1395871] Train: [13/100][684/1462] Data 0.002 (0.002) Batch 2.741 (2.886) Remain 102:35:45 loss: 0.2247 Lr: 0.00200
[2025-09-06 13:27:31,877 INFO misc.py line 117 1395871] Train: [13/100][685/1462] Data 0.001 (0.002) Batch 3.020 (2.886) Remain 102:36:07 loss: 0.5456 Lr: 0.00200
[2025-09-06 13:27:34,701 INFO misc.py line 117 1395871] Train: [13/100][686/1462] Data 0.002 (0.002) Batch 2.824 (2.886) Remain 102:35:53 loss: 0.2082 Lr: 0.00200
[2025-09-06 13:27:38,292 INFO misc.py line 117 1395871] Train: [13/100][687/1462] Data 0.002 (0.002) Batch 3.591 (2.887) Remain 102:38:02 loss: 0.3486 Lr: 0.00200
[2025-09-06 13:27:41,264 INFO misc.py line 117 1395871] Train: [13/100][688/1462] Data 0.002 (0.002) Batch 2.972 (2.887) Remain 102:38:15 loss: 0.7170 Lr: 0.00200
[2025-09-06 13:27:44,051 INFO misc.py line 117 1395871] Train: [13/100][689/1462] Data 0.002 (0.002) Batch 2.786 (2.887) Remain 102:37:53 loss: 0.3398 Lr: 0.00200
[2025-09-06 13:27:46,862 INFO misc.py line 117 1395871] Train: [13/100][690/1462] Data 0.002 (0.002) Batch 2.811 (2.887) Remain 102:37:36 loss: 0.4658 Lr: 0.00200
[2025-09-06 13:27:49,559 INFO misc.py line 117 1395871] Train: [13/100][691/1462] Data 0.002 (0.002) Batch 2.697 (2.887) Remain 102:36:58 loss: 0.5365 Lr: 0.00200
[2025-09-06 13:27:52,724 INFO misc.py line 117 1395871] Train: [13/100][692/1462] Data 0.002 (0.002) Batch 3.164 (2.887) Remain 102:37:46 loss: 0.3256 Lr: 0.00200
[2025-09-06 13:27:55,535 INFO misc.py line 117 1395871] Train: [13/100][693/1462] Data 0.002 (0.002) Batch 2.812 (2.887) Remain 102:37:29 loss: 0.2984 Lr: 0.00200
[2025-09-06 13:27:58,592 INFO misc.py line 117 1395871] Train: [13/100][694/1462] Data 0.002 (0.002) Batch 3.057 (2.887) Remain 102:37:58 loss: 0.5719 Lr: 0.00200
[2025-09-06 13:28:01,414 INFO misc.py line 117 1395871] Train: [13/100][695/1462] Data 0.001 (0.002) Batch 2.822 (2.887) Remain 102:37:43 loss: 0.4219 Lr: 0.00200
[2025-09-06 13:28:04,024 INFO misc.py line 117 1395871] Train: [13/100][696/1462] Data 0.001 (0.002) Batch 2.610 (2.887) Remain 102:36:49 loss: 0.5463 Lr: 0.00200
[2025-09-06 13:28:07,419 INFO misc.py line 117 1395871] Train: [13/100][697/1462] Data 0.002 (0.002) Batch 3.395 (2.888) Remain 102:38:20 loss: 0.4996 Lr: 0.00200
[2025-09-06 13:28:10,225 INFO misc.py line 117 1395871] Train: [13/100][698/1462] Data 0.002 (0.002) Batch 2.806 (2.888) Remain 102:38:02 loss: 0.3924 Lr: 0.00200
[2025-09-06 13:28:13,427 INFO misc.py line 117 1395871] Train: [13/100][699/1462] Data 0.001 (0.002) Batch 3.202 (2.888) Remain 102:38:57 loss: 0.4983 Lr: 0.00200
[2025-09-06 13:28:16,132 INFO misc.py line 117 1395871] Train: [13/100][700/1462] Data 0.001 (0.002) Batch 2.706 (2.888) Remain 102:38:20 loss: 0.4806 Lr: 0.00200
[2025-09-06 13:28:19,313 INFO misc.py line 117 1395871] Train: [13/100][701/1462] Data 0.001 (0.002) Batch 3.180 (2.888) Remain 102:39:11 loss: 0.5471 Lr: 0.00200
[2025-09-06 13:28:22,108 INFO misc.py line 117 1395871] Train: [13/100][702/1462] Data 0.002 (0.002) Batch 2.796 (2.888) Remain 102:38:51 loss: 0.4594 Lr: 0.00200
[2025-09-06 13:28:25,089 INFO misc.py line 117 1395871] Train: [13/100][703/1462] Data 0.002 (0.002) Batch 2.981 (2.888) Remain 102:39:05 loss: 0.3296 Lr: 0.00200
[2025-09-06 13:28:27,810 INFO misc.py line 117 1395871] Train: [13/100][704/1462] Data 0.002 (0.002) Batch 2.721 (2.888) Remain 102:38:32 loss: 0.4399 Lr: 0.00200
[2025-09-06 13:28:30,495 INFO misc.py line 117 1395871] Train: [13/100][705/1462] Data 0.002 (0.002) Batch 2.685 (2.888) Remain 102:37:52 loss: 0.2592 Lr: 0.00200
[2025-09-06 13:28:33,194 INFO misc.py line 117 1395871] Train: [13/100][706/1462] Data 0.002 (0.002) Batch 2.699 (2.887) Remain 102:37:15 loss: 0.6969 Lr: 0.00200
[2025-09-06 13:28:35,931 INFO misc.py line 117 1395871] Train: [13/100][707/1462] Data 0.002 (0.002) Batch 2.737 (2.887) Remain 102:36:44 loss: 0.2879 Lr: 0.00200
[2025-09-06 13:28:38,784 INFO misc.py line 117 1395871] Train: [13/100][708/1462] Data 0.002 (0.002) Batch 2.854 (2.887) Remain 102:36:36 loss: 0.3510 Lr: 0.00200
[2025-09-06 13:28:41,518 INFO misc.py line 117 1395871] Train: [13/100][709/1462] Data 0.001 (0.002) Batch 2.733 (2.887) Remain 102:36:05 loss: 0.6392 Lr: 0.00200
[2025-09-06 13:28:44,772 INFO misc.py line 117 1395871] Train: [13/100][710/1462] Data 0.001 (0.002) Batch 3.254 (2.887) Remain 102:37:08 loss: 0.5503 Lr: 0.00200
[2025-09-06 13:28:47,673 INFO misc.py line 117 1395871] Train: [13/100][711/1462] Data 0.001 (0.002) Batch 2.901 (2.887) Remain 102:37:08 loss: 0.5720 Lr: 0.00200
[2025-09-06 13:28:50,621 INFO misc.py line 117 1395871] Train: [13/100][712/1462] Data 0.001 (0.002) Batch 2.948 (2.887) Remain 102:37:16 loss: 0.5681 Lr: 0.00200
[2025-09-06 13:28:53,640 INFO misc.py line 117 1395871] Train: [13/100][713/1462] Data 0.001 (0.002) Batch 3.019 (2.888) Remain 102:37:37 loss: 0.4428 Lr: 0.00200
[2025-09-06 13:28:56,396 INFO misc.py line 117 1395871] Train: [13/100][714/1462] Data 0.001 (0.002) Batch 2.756 (2.887) Remain 102:37:10 loss: 0.4512 Lr: 0.00200
[2025-09-06 13:28:59,332 INFO misc.py line 117 1395871] Train: [13/100][715/1462] Data 0.002 (0.002) Batch 2.936 (2.888) Remain 102:37:16 loss: 0.3044 Lr: 0.00200
[2025-09-06 13:29:02,005 INFO misc.py line 117 1395871] Train: [13/100][716/1462] Data 0.001 (0.002) Batch 2.672 (2.887) Remain 102:36:35 loss: 0.6306 Lr: 0.00200
[2025-09-06 13:29:04,815 INFO misc.py line 117 1395871] Train: [13/100][717/1462] Data 0.001 (0.002) Batch 2.810 (2.887) Remain 102:36:18 loss: 0.6021 Lr: 0.00200
[2025-09-06 13:29:07,680 INFO misc.py line 117 1395871] Train: [13/100][718/1462] Data 0.001 (0.002) Batch 2.865 (2.887) Remain 102:36:11 loss: 0.6102 Lr: 0.00200
[2025-09-06 13:29:10,352 INFO misc.py line 117 1395871] Train: [13/100][719/1462] Data 0.001 (0.002) Batch 2.672 (2.887) Remain 102:35:30 loss: 0.5762 Lr: 0.00200
[2025-09-06 13:29:13,039 INFO misc.py line 117 1395871] Train: [13/100][720/1462] Data 0.001 (0.002) Batch 2.687 (2.887) Remain 102:34:51 loss: 0.3576 Lr: 0.00200
[2025-09-06 13:29:16,238 INFO misc.py line 117 1395871] Train: [13/100][721/1462] Data 0.001 (0.002) Batch 3.198 (2.887) Remain 102:35:44 loss: 0.4454 Lr: 0.00200
[2025-09-06 13:29:18,821 INFO misc.py line 117 1395871] Train: [13/100][722/1462] Data 0.001 (0.002) Batch 2.583 (2.887) Remain 102:34:47 loss: 0.5021 Lr: 0.00200
[2025-09-06 13:29:21,776 INFO misc.py line 117 1395871] Train: [13/100][723/1462] Data 0.001 (0.002) Batch 2.955 (2.887) Remain 102:34:56 loss: 0.2360 Lr: 0.00200
[2025-09-06 13:29:24,793 INFO misc.py line 117 1395871] Train: [13/100][724/1462] Data 0.002 (0.002) Batch 3.017 (2.887) Remain 102:35:17 loss: 0.5080 Lr: 0.00200
[2025-09-06 13:29:27,496 INFO misc.py line 117 1395871] Train: [13/100][725/1462] Data 0.001 (0.002) Batch 2.702 (2.887) Remain 102:34:41 loss: 0.4297 Lr: 0.00200
[2025-09-06 13:29:30,460 INFO misc.py line 117 1395871] Train: [13/100][726/1462] Data 0.001 (0.002) Batch 2.964 (2.887) Remain 102:34:52 loss: 0.9029 Lr: 0.00200
[2025-09-06 13:29:33,352 INFO misc.py line 117 1395871] Train: [13/100][727/1462] Data 0.002 (0.002) Batch 2.893 (2.887) Remain 102:34:50 loss: 0.6833 Lr: 0.00200
[2025-09-06 13:29:36,654 INFO misc.py line 117 1395871] Train: [13/100][728/1462] Data 0.001 (0.002) Batch 3.301 (2.887) Remain 102:36:00 loss: 0.4843 Lr: 0.00200
[2025-09-06 13:29:39,527 INFO misc.py line 117 1395871] Train: [13/100][729/1462] Data 0.001 (0.002) Batch 2.873 (2.887) Remain 102:35:55 loss: 0.3864 Lr: 0.00200
[2025-09-06 13:29:42,347 INFO misc.py line 117 1395871] Train: [13/100][730/1462] Data 0.001 (0.002) Batch 2.820 (2.887) Remain 102:35:40 loss: 0.4042 Lr: 0.00200
[2025-09-06 13:29:45,085 INFO misc.py line 117 1395871] Train: [13/100][731/1462] Data 0.001 (0.002) Batch 2.738 (2.887) Remain 102:35:11 loss: 0.6174 Lr: 0.00200
[2025-09-06 13:29:47,947 INFO misc.py line 117 1395871] Train: [13/100][732/1462] Data 0.002 (0.002) Batch 2.862 (2.887) Remain 102:35:04 loss: 0.2035 Lr: 0.00200
[2025-09-06 13:29:50,858 INFO misc.py line 117 1395871] Train: [13/100][733/1462] Data 0.001 (0.002) Batch 2.911 (2.887) Remain 102:35:05 loss: 0.4188 Lr: 0.00200
[2025-09-06 13:29:53,987 INFO misc.py line 117 1395871] Train: [13/100][734/1462] Data 0.002 (0.002) Batch 3.129 (2.887) Remain 102:35:45 loss: 0.4165 Lr: 0.00200
[2025-09-06 13:29:56,921 INFO misc.py line 117 1395871] Train: [13/100][735/1462] Data 0.001 (0.002) Batch 2.934 (2.887) Remain 102:35:50 loss: 0.3987 Lr: 0.00200
[2025-09-06 13:29:59,984 INFO misc.py line 117 1395871] Train: [13/100][736/1462] Data 0.001 (0.002) Batch 3.063 (2.888) Remain 102:36:18 loss: 0.3253 Lr: 0.00200
[2025-09-06 13:30:03,138 INFO misc.py line 117 1395871] Train: [13/100][737/1462] Data 0.001 (0.002) Batch 3.154 (2.888) Remain 102:37:01 loss: 0.3739 Lr: 0.00200
[2025-09-06 13:30:05,976 INFO misc.py line 117 1395871] Train: [13/100][738/1462] Data 0.001 (0.002) Batch 2.838 (2.888) Remain 102:36:50 loss: 0.3997 Lr: 0.00200
[2025-09-06 13:30:08,703 INFO misc.py line 117 1395871] Train: [13/100][739/1462] Data 0.001 (0.002) Batch 2.727 (2.888) Remain 102:36:19 loss: 0.3244 Lr: 0.00200
[2025-09-06 13:30:11,423 INFO misc.py line 117 1395871] Train: [13/100][740/1462] Data 0.001 (0.002) Batch 2.719 (2.887) Remain 102:35:47 loss: 0.4571 Lr: 0.00200
[2025-09-06 13:30:14,129 INFO misc.py line 117 1395871] Train: [13/100][741/1462] Data 0.001 (0.002) Batch 2.706 (2.887) Remain 102:35:13 loss: 0.4385 Lr: 0.00200
[2025-09-06 13:30:17,205 INFO misc.py line 117 1395871] Train: [13/100][742/1462] Data 0.001 (0.002) Batch 3.076 (2.887) Remain 102:35:42 loss: 0.3013 Lr: 0.00200
[2025-09-06 13:30:20,157 INFO misc.py line 117 1395871] Train: [13/100][743/1462] Data 0.001 (0.002) Batch 2.952 (2.888) Remain 102:35:51 loss: 0.5413 Lr: 0.00200
[2025-09-06 13:30:23,112 INFO misc.py line 117 1395871] Train: [13/100][744/1462] Data 0.001 (0.002) Batch 2.955 (2.888) Remain 102:35:59 loss: 0.3282 Lr: 0.00200
[2025-09-06 13:30:25,861 INFO misc.py line 117 1395871] Train: [13/100][745/1462] Data 0.001 (0.002) Batch 2.749 (2.887) Remain 102:35:33 loss: 0.2824 Lr: 0.00200
[2025-09-06 13:30:28,731 INFO misc.py line 117 1395871] Train: [13/100][746/1462] Data 0.002 (0.002) Batch 2.870 (2.887) Remain 102:35:27 loss: 0.3306 Lr: 0.00200
[2025-09-06 13:30:31,356 INFO misc.py line 117 1395871] Train: [13/100][747/1462] Data 0.001 (0.002) Batch 2.625 (2.887) Remain 102:34:39 loss: 0.3337 Lr: 0.00200
[2025-09-06 13:30:34,210 INFO misc.py line 117 1395871] Train: [13/100][748/1462] Data 0.001 (0.002) Batch 2.854 (2.887) Remain 102:34:30 loss: 0.2607 Lr: 0.00200
[2025-09-06 13:30:37,376 INFO misc.py line 117 1395871] Train: [13/100][749/1462] Data 0.001 (0.002) Batch 3.166 (2.887) Remain 102:35:15 loss: 0.1889 Lr: 0.00200
[2025-09-06 13:30:40,413 INFO misc.py line 117 1395871] Train: [13/100][750/1462] Data 0.001 (0.002) Batch 3.037 (2.888) Remain 102:35:38 loss: 0.3660 Lr: 0.00200
[2025-09-06 13:30:43,468 INFO misc.py line 117 1395871] Train: [13/100][751/1462] Data 0.001 (0.002) Batch 3.055 (2.888) Remain 102:36:04 loss: 0.5119 Lr: 0.00200
[2025-09-06 13:30:46,209 INFO misc.py line 117 1395871] Train: [13/100][752/1462] Data 0.001 (0.002) Batch 2.741 (2.888) Remain 102:35:36 loss: 0.4811 Lr: 0.00200
[2025-09-06 13:30:49,110 INFO misc.py line 117 1395871] Train: [13/100][753/1462] Data 0.001 (0.002) Batch 2.901 (2.888) Remain 102:35:35 loss: 0.4884 Lr: 0.00200
[2025-09-06 13:30:51,973 INFO misc.py line 117 1395871] Train: [13/100][754/1462] Data 0.001 (0.002) Batch 2.862 (2.888) Remain 102:35:28 loss: 0.3529 Lr: 0.00200
[2025-09-06 13:30:54,769 INFO misc.py line 117 1395871] Train: [13/100][755/1462] Data 0.001 (0.002) Batch 2.796 (2.887) Remain 102:35:09 loss: 0.2709 Lr: 0.00200
[2025-09-06 13:30:57,446 INFO misc.py line 117 1395871] Train: [13/100][756/1462] Data 0.002 (0.002) Batch 2.678 (2.887) Remain 102:34:31 loss: 0.4054 Lr: 0.00200
[2025-09-06 13:31:00,202 INFO misc.py line 117 1395871] Train: [13/100][757/1462] Data 0.001 (0.002) Batch 2.756 (2.887) Remain 102:34:06 loss: 0.4210 Lr: 0.00200
[2025-09-06 13:31:02,908 INFO misc.py line 117 1395871] Train: [13/100][758/1462] Data 0.001 (0.002) Batch 2.705 (2.887) Remain 102:33:32 loss: 0.5147 Lr: 0.00200
[2025-09-06 13:31:06,019 INFO misc.py line 117 1395871] Train: [13/100][759/1462] Data 0.001 (0.002) Batch 3.111 (2.887) Remain 102:34:07 loss: 0.3053 Lr: 0.00200
[2025-09-06 13:31:08,789 INFO misc.py line 117 1395871] Train: [13/100][760/1462] Data 0.001 (0.002) Batch 2.769 (2.887) Remain 102:33:44 loss: 0.5827 Lr: 0.00200
[2025-09-06 13:31:11,706 INFO misc.py line 117 1395871] Train: [13/100][761/1462] Data 0.001 (0.002) Batch 2.917 (2.887) Remain 102:33:47 loss: 0.3343 Lr: 0.00200
[2025-09-06 13:31:14,826 INFO misc.py line 117 1395871] Train: [13/100][762/1462] Data 0.001 (0.002) Batch 3.120 (2.887) Remain 102:34:23 loss: 0.3812 Lr: 0.00200
[2025-09-06 13:31:17,835 INFO misc.py line 117 1395871] Train: [13/100][763/1462] Data 0.001 (0.002) Batch 3.009 (2.887) Remain 102:34:41 loss: 0.8944 Lr: 0.00200
[2025-09-06 13:31:20,704 INFO misc.py line 117 1395871] Train: [13/100][764/1462] Data 0.001 (0.002) Batch 2.869 (2.887) Remain 102:34:35 loss: 0.2183 Lr: 0.00200
[2025-09-06 13:31:23,918 INFO misc.py line 117 1395871] Train: [13/100][765/1462] Data 0.001 (0.002) Batch 3.214 (2.888) Remain 102:35:27 loss: 0.4141 Lr: 0.00200
[2025-09-06 13:31:26,806 INFO misc.py line 117 1395871] Train: [13/100][766/1462] Data 0.002 (0.002) Batch 2.888 (2.888) Remain 102:35:24 loss: 0.3031 Lr: 0.00200
[2025-09-06 13:31:29,667 INFO misc.py line 117 1395871] Train: [13/100][767/1462] Data 0.002 (0.002) Batch 2.860 (2.888) Remain 102:35:16 loss: 0.4712 Lr: 0.00200
[2025-09-06 13:31:32,850 INFO misc.py line 117 1395871] Train: [13/100][768/1462] Data 0.002 (0.002) Batch 3.183 (2.888) Remain 102:36:03 loss: 0.5061 Lr: 0.00200
[2025-09-06 13:31:36,016 INFO misc.py line 117 1395871] Train: [13/100][769/1462] Data 0.002 (0.002) Batch 3.166 (2.889) Remain 102:36:46 loss: 0.2740 Lr: 0.00200
[2025-09-06 13:31:38,952 INFO misc.py line 117 1395871] Train: [13/100][770/1462] Data 0.002 (0.002) Batch 2.936 (2.889) Remain 102:36:51 loss: 0.4143 Lr: 0.00200
[2025-09-06 13:31:41,711 INFO misc.py line 117 1395871] Train: [13/100][771/1462] Data 0.002 (0.002) Batch 2.759 (2.888) Remain 102:36:27 loss: 0.4470 Lr: 0.00200
[2025-09-06 13:31:44,347 INFO misc.py line 117 1395871] Train: [13/100][772/1462] Data 0.002 (0.002) Batch 2.636 (2.888) Remain 102:35:42 loss: 0.3807 Lr: 0.00200
[2025-09-06 13:31:47,079 INFO misc.py line 117 1395871] Train: [13/100][773/1462] Data 0.002 (0.002) Batch 2.732 (2.888) Remain 102:35:13 loss: 0.2249 Lr: 0.00200
[2025-09-06 13:31:49,901 INFO misc.py line 117 1395871] Train: [13/100][774/1462] Data 0.001 (0.002) Batch 2.822 (2.888) Remain 102:34:59 loss: 0.3465 Lr: 0.00200
[2025-09-06 13:31:52,532 INFO misc.py line 117 1395871] Train: [13/100][775/1462] Data 0.002 (0.002) Batch 2.631 (2.887) Remain 102:34:14 loss: 0.2835 Lr: 0.00200
[2025-09-06 13:31:54,982 INFO misc.py line 117 1395871] Train: [13/100][776/1462] Data 0.002 (0.002) Batch 2.450 (2.887) Remain 102:32:59 loss: 0.4182 Lr: 0.00200
[2025-09-06 13:31:57,663 INFO misc.py line 117 1395871] Train: [13/100][777/1462] Data 0.002 (0.002) Batch 2.681 (2.887) Remain 102:32:22 loss: 0.3917 Lr: 0.00200
[2025-09-06 13:32:00,588 INFO misc.py line 117 1395871] Train: [13/100][778/1462] Data 0.002 (0.002) Batch 2.925 (2.887) Remain 102:32:25 loss: 0.3825 Lr: 0.00200
[2025-09-06 13:32:03,240 INFO misc.py line 117 1395871] Train: [13/100][779/1462] Data 0.002 (0.002) Batch 2.651 (2.886) Remain 102:31:44 loss: 0.3872 Lr: 0.00200
[2025-09-06 13:32:06,255 INFO misc.py line 117 1395871] Train: [13/100][780/1462] Data 0.002 (0.002) Batch 3.015 (2.887) Remain 102:32:02 loss: 0.2361 Lr: 0.00200
[2025-09-06 13:32:09,196 INFO misc.py line 117 1395871] Train: [13/100][781/1462] Data 0.002 (0.002) Batch 2.941 (2.887) Remain 102:32:08 loss: 0.4304 Lr: 0.00200
[2025-09-06 13:32:12,158 INFO misc.py line 117 1395871] Train: [13/100][782/1462] Data 0.002 (0.002) Batch 2.961 (2.887) Remain 102:32:17 loss: 0.4086 Lr: 0.00200
[2025-09-06 13:32:14,769 INFO misc.py line 117 1395871] Train: [13/100][783/1462] Data 0.001 (0.002) Batch 2.611 (2.886) Remain 102:31:29 loss: 0.4381 Lr: 0.00200
[2025-09-06 13:32:17,714 INFO misc.py line 117 1395871] Train: [13/100][784/1462] Data 0.002 (0.002) Batch 2.945 (2.886) Remain 102:31:36 loss: 0.2491 Lr: 0.00200
[2025-09-06 13:32:20,480 INFO misc.py line 117 1395871] Train: [13/100][785/1462] Data 0.002 (0.002) Batch 2.766 (2.886) Remain 102:31:13 loss: 0.4978 Lr: 0.00200
[2025-09-06 13:32:23,223 INFO misc.py line 117 1395871] Train: [13/100][786/1462] Data 0.002 (0.002) Batch 2.743 (2.886) Remain 102:30:47 loss: 0.6409 Lr: 0.00200
[2025-09-06 13:32:26,311 INFO misc.py line 117 1395871] Train: [13/100][787/1462] Data 0.002 (0.002) Batch 3.088 (2.886) Remain 102:31:17 loss: 0.4118 Lr: 0.00200
[2025-09-06 13:32:29,509 INFO misc.py line 117 1395871] Train: [13/100][788/1462] Data 0.002 (0.002) Batch 3.199 (2.887) Remain 102:32:05 loss: 0.6712 Lr: 0.00200
[2025-09-06 13:32:32,271 INFO misc.py line 117 1395871] Train: [13/100][789/1462] Data 0.002 (0.002) Batch 2.761 (2.887) Remain 102:31:42 loss: 0.5039 Lr: 0.00200
[2025-09-06 13:32:34,954 INFO misc.py line 117 1395871] Train: [13/100][790/1462] Data 0.001 (0.002) Batch 2.683 (2.886) Remain 102:31:06 loss: 0.4877 Lr: 0.00200
[2025-09-06 13:32:37,989 INFO misc.py line 117 1395871] Train: [13/100][791/1462] Data 0.002 (0.002) Batch 3.035 (2.887) Remain 102:31:27 loss: 0.5884 Lr: 0.00200
[2025-09-06 13:32:40,984 INFO misc.py line 117 1395871] Train: [13/100][792/1462] Data 0.001 (0.002) Batch 2.995 (2.887) Remain 102:31:42 loss: 0.3102 Lr: 0.00200
[2025-09-06 13:32:43,891 INFO misc.py line 117 1395871] Train: [13/100][793/1462] Data 0.002 (0.002) Batch 2.907 (2.887) Remain 102:31:42 loss: 0.5389 Lr: 0.00200
[2025-09-06 13:32:46,610 INFO misc.py line 117 1395871] Train: [13/100][794/1462] Data 0.002 (0.002) Batch 2.719 (2.886) Remain 102:31:12 loss: 0.5958 Lr: 0.00200
[2025-09-06 13:32:49,582 INFO misc.py line 117 1395871] Train: [13/100][795/1462] Data 0.001 (0.002) Batch 2.971 (2.887) Remain 102:31:23 loss: 0.4958 Lr: 0.00200
[2025-09-06 13:32:52,388 INFO misc.py line 117 1395871] Train: [13/100][796/1462] Data 0.002 (0.002) Batch 2.806 (2.886) Remain 102:31:07 loss: 0.6541 Lr: 0.00200
[2025-09-06 13:32:55,697 INFO misc.py line 117 1395871] Train: [13/100][797/1462] Data 0.002 (0.002) Batch 3.309 (2.887) Remain 102:32:12 loss: 0.6958 Lr: 0.00200
[2025-09-06 13:32:58,321 INFO misc.py line 117 1395871] Train: [13/100][798/1462] Data 0.002 (0.002) Batch 2.625 (2.887) Remain 102:31:27 loss: 0.4427 Lr: 0.00200
[2025-09-06 13:33:01,236 INFO misc.py line 117 1395871] Train: [13/100][799/1462] Data 0.002 (0.002) Batch 2.915 (2.887) Remain 102:31:29 loss: 0.3533 Lr: 0.00200
[2025-09-06 13:33:03,904 INFO misc.py line 117 1395871] Train: [13/100][800/1462] Data 0.002 (0.002) Batch 2.668 (2.886) Remain 102:30:51 loss: 0.4556 Lr: 0.00200
[2025-09-06 13:33:06,765 INFO misc.py line 117 1395871] Train: [13/100][801/1462] Data 0.002 (0.002) Batch 2.861 (2.886) Remain 102:30:44 loss: 0.3418 Lr: 0.00200
[2025-09-06 13:33:09,752 INFO misc.py line 117 1395871] Train: [13/100][802/1462] Data 0.002 (0.002) Batch 2.988 (2.887) Remain 102:30:57 loss: 0.9233 Lr: 0.00200
[2025-09-06 13:33:12,280 INFO misc.py line 117 1395871] Train: [13/100][803/1462] Data 0.001 (0.002) Batch 2.527 (2.886) Remain 102:29:57 loss: 0.5005 Lr: 0.00200
[2025-09-06 13:33:15,411 INFO misc.py line 117 1395871] Train: [13/100][804/1462] Data 0.002 (0.002) Batch 3.132 (2.886) Remain 102:30:33 loss: 0.4864 Lr: 0.00200
[2025-09-06 13:33:18,116 INFO misc.py line 117 1395871] Train: [13/100][805/1462] Data 0.002 (0.002) Batch 2.705 (2.886) Remain 102:30:01 loss: 0.5860 Lr: 0.00200
[2025-09-06 13:33:20,746 INFO misc.py line 117 1395871] Train: [13/100][806/1462] Data 0.002 (0.002) Batch 2.630 (2.886) Remain 102:29:18 loss: 0.3077 Lr: 0.00200
[2025-09-06 13:33:23,507 INFO misc.py line 117 1395871] Train: [13/100][807/1462] Data 0.002 (0.002) Batch 2.761 (2.886) Remain 102:28:55 loss: 0.3213 Lr: 0.00200
[2025-09-06 13:33:26,145 INFO misc.py line 117 1395871] Train: [13/100][808/1462] Data 0.002 (0.002) Batch 2.637 (2.885) Remain 102:28:13 loss: 0.6932 Lr: 0.00200
[2025-09-06 13:33:29,055 INFO misc.py line 117 1395871] Train: [13/100][809/1462] Data 0.003 (0.002) Batch 2.911 (2.885) Remain 102:28:14 loss: 0.4003 Lr: 0.00200
[2025-09-06 13:33:32,039 INFO misc.py line 117 1395871] Train: [13/100][810/1462] Data 0.001 (0.002) Batch 2.983 (2.886) Remain 102:28:26 loss: 0.5448 Lr: 0.00200
[2025-09-06 13:33:35,308 INFO misc.py line 117 1395871] Train: [13/100][811/1462] Data 0.002 (0.002) Batch 3.269 (2.886) Remain 102:29:24 loss: 0.3735 Lr: 0.00200
[2025-09-06 13:33:38,191 INFO misc.py line 117 1395871] Train: [13/100][812/1462] Data 0.002 (0.002) Batch 2.882 (2.886) Remain 102:29:21 loss: 0.5529 Lr: 0.00200
[2025-09-06 13:33:40,965 INFO misc.py line 117 1395871] Train: [13/100][813/1462] Data 0.002 (0.002) Batch 2.775 (2.886) Remain 102:29:00 loss: 0.4276 Lr: 0.00200
[2025-09-06 13:33:43,863 INFO misc.py line 117 1395871] Train: [13/100][814/1462] Data 0.002 (0.002) Batch 2.898 (2.886) Remain 102:28:59 loss: 0.6142 Lr: 0.00200
[2025-09-06 13:33:47,162 INFO misc.py line 117 1395871] Train: [13/100][815/1462] Data 0.002 (0.002) Batch 3.299 (2.886) Remain 102:30:02 loss: 0.6561 Lr: 0.00200
[2025-09-06 13:33:50,425 INFO misc.py line 117 1395871] Train: [13/100][816/1462] Data 0.002 (0.002) Batch 3.263 (2.887) Remain 102:30:58 loss: 0.2626 Lr: 0.00200
[2025-09-06 13:33:53,180 INFO misc.py line 117 1395871] Train: [13/100][817/1462] Data 0.002 (0.002) Batch 2.756 (2.887) Remain 102:30:34 loss: 0.3495 Lr: 0.00200
[2025-09-06 13:33:56,239 INFO misc.py line 117 1395871] Train: [13/100][818/1462] Data 0.002 (0.002) Batch 3.059 (2.887) Remain 102:30:58 loss: 0.8245 Lr: 0.00200
[2025-09-06 13:33:59,190 INFO misc.py line 117 1395871] Train: [13/100][819/1462] Data 0.002 (0.002) Batch 2.951 (2.887) Remain 102:31:06 loss: 0.5504 Lr: 0.00200
[2025-09-06 13:34:02,116 INFO misc.py line 117 1395871] Train: [13/100][820/1462] Data 0.002 (0.002) Batch 2.926 (2.887) Remain 102:31:09 loss: 0.4239 Lr: 0.00200
[2025-09-06 13:34:05,020 INFO misc.py line 117 1395871] Train: [13/100][821/1462] Data 0.002 (0.002) Batch 2.903 (2.887) Remain 102:31:08 loss: 0.3313 Lr: 0.00200
[2025-09-06 13:34:07,778 INFO misc.py line 117 1395871] Train: [13/100][822/1462] Data 0.002 (0.002) Batch 2.759 (2.887) Remain 102:30:46 loss: 0.3096 Lr: 0.00200
[2025-09-06 13:34:10,615 INFO misc.py line 117 1395871] Train: [13/100][823/1462] Data 0.002 (0.002) Batch 2.837 (2.887) Remain 102:30:35 loss: 1.1114 Lr: 0.00200
[2025-09-06 13:34:13,819 INFO misc.py line 117 1395871] Train: [13/100][824/1462] Data 0.002 (0.002) Batch 3.204 (2.887) Remain 102:31:21 loss: 0.4796 Lr: 0.00200
[2025-09-06 13:34:16,377 INFO misc.py line 117 1395871] Train: [13/100][825/1462] Data 0.002 (0.002) Batch 2.557 (2.887) Remain 102:30:27 loss: 0.3582 Lr: 0.00200
[2025-09-06 13:34:19,205 INFO misc.py line 117 1395871] Train: [13/100][826/1462] Data 0.002 (0.002) Batch 2.828 (2.887) Remain 102:30:15 loss: 0.4554 Lr: 0.00200
[2025-09-06 13:34:22,043 INFO misc.py line 117 1395871] Train: [13/100][827/1462] Data 0.002 (0.002) Batch 2.837 (2.887) Remain 102:30:05 loss: 0.2495 Lr: 0.00200
[2025-09-06 13:34:25,041 INFO misc.py line 117 1395871] Train: [13/100][828/1462] Data 0.003 (0.002) Batch 2.998 (2.887) Remain 102:30:19 loss: 0.4416 Lr: 0.00200
[2025-09-06 13:34:27,546 INFO misc.py line 117 1395871] Train: [13/100][829/1462] Data 0.002 (0.002) Batch 2.505 (2.886) Remain 102:29:17 loss: 0.4593 Lr: 0.00200
[2025-09-06 13:34:30,513 INFO misc.py line 117 1395871] Train: [13/100][830/1462] Data 0.002 (0.002) Batch 2.968 (2.886) Remain 102:29:27 loss: 0.3757 Lr: 0.00200
[2025-09-06 13:34:33,076 INFO misc.py line 117 1395871] Train: [13/100][831/1462] Data 0.002 (0.002) Batch 2.563 (2.886) Remain 102:28:34 loss: 0.7324 Lr: 0.00200
[2025-09-06 13:34:36,021 INFO misc.py line 117 1395871] Train: [13/100][832/1462] Data 0.002 (0.002) Batch 2.945 (2.886) Remain 102:28:40 loss: 0.5872 Lr: 0.00200
[2025-09-06 13:34:38,924 INFO misc.py line 117 1395871] Train: [13/100][833/1462] Data 0.002 (0.002) Batch 2.902 (2.886) Remain 102:28:40 loss: 0.3231 Lr: 0.00200
[2025-09-06 13:34:41,930 INFO misc.py line 117 1395871] Train: [13/100][834/1462] Data 0.002 (0.002) Batch 3.006 (2.886) Remain 102:28:55 loss: 0.4853 Lr: 0.00200
[2025-09-06 13:34:45,006 INFO misc.py line 117 1395871] Train: [13/100][835/1462] Data 0.002 (0.002) Batch 3.076 (2.887) Remain 102:29:22 loss: 0.7234 Lr: 0.00200
[2025-09-06 13:34:47,807 INFO misc.py line 117 1395871] Train: [13/100][836/1462] Data 0.001 (0.002) Batch 2.801 (2.886) Remain 102:29:06 loss: 0.6949 Lr: 0.00200
[2025-09-06 13:34:50,888 INFO misc.py line 117 1395871] Train: [13/100][837/1462] Data 0.002 (0.002) Batch 3.081 (2.887) Remain 102:29:33 loss: 0.6567 Lr: 0.00200
[2025-09-06 13:34:53,946 INFO misc.py line 117 1395871] Train: [13/100][838/1462] Data 0.002 (0.002) Batch 3.058 (2.887) Remain 102:29:56 loss: 0.4142 Lr: 0.00200
[2025-09-06 13:34:56,885 INFO misc.py line 117 1395871] Train: [13/100][839/1462] Data 0.002 (0.002) Batch 2.939 (2.887) Remain 102:30:01 loss: 0.5773 Lr: 0.00200
[2025-09-06 13:34:59,798 INFO misc.py line 117 1395871] Train: [13/100][840/1462] Data 0.002 (0.002) Batch 2.913 (2.887) Remain 102:30:02 loss: 0.6200 Lr: 0.00200
[2025-09-06 13:35:02,863 INFO misc.py line 117 1395871] Train: [13/100][841/1462] Data 0.002 (0.002) Batch 3.065 (2.887) Remain 102:30:26 loss: 0.4879 Lr: 0.00200
[2025-09-06 13:35:05,508 INFO misc.py line 117 1395871] Train: [13/100][842/1462] Data 0.002 (0.002) Batch 2.646 (2.887) Remain 102:29:47 loss: 0.6406 Lr: 0.00200
[2025-09-06 13:35:08,466 INFO misc.py line 117 1395871] Train: [13/100][843/1462] Data 0.002 (0.002) Batch 2.958 (2.887) Remain 102:29:54 loss: 0.4046 Lr: 0.00200
[2025-09-06 13:35:11,423 INFO misc.py line 117 1395871] Train: [13/100][844/1462] Data 0.002 (0.002) Batch 2.957 (2.887) Remain 102:30:02 loss: 0.5943 Lr: 0.00200
[2025-09-06 13:35:14,285 INFO misc.py line 117 1395871] Train: [13/100][845/1462] Data 0.002 (0.002) Batch 2.862 (2.887) Remain 102:29:56 loss: 0.4300 Lr: 0.00200
[2025-09-06 13:35:17,123 INFO misc.py line 117 1395871] Train: [13/100][846/1462] Data 0.001 (0.002) Batch 2.838 (2.887) Remain 102:29:45 loss: 0.5145 Lr: 0.00200
[2025-09-06 13:35:19,950 INFO misc.py line 117 1395871] Train: [13/100][847/1462] Data 0.002 (0.002) Batch 2.826 (2.887) Remain 102:29:33 loss: 0.2576 Lr: 0.00200
[2025-09-06 13:35:22,561 INFO misc.py line 117 1395871] Train: [13/100][848/1462] Data 0.002 (0.002) Batch 2.611 (2.887) Remain 102:28:49 loss: 0.4499 Lr: 0.00200
[2025-09-06 13:35:25,437 INFO misc.py line 117 1395871] Train: [13/100][849/1462] Data 0.001 (0.002) Batch 2.876 (2.887) Remain 102:28:44 loss: 0.3055 Lr: 0.00200
[2025-09-06 13:35:28,549 INFO misc.py line 117 1395871] Train: [13/100][850/1462] Data 0.002 (0.002) Batch 3.112 (2.887) Remain 102:29:15 loss: 0.7451 Lr: 0.00200
[2025-09-06 13:35:31,793 INFO misc.py line 117 1395871] Train: [13/100][851/1462] Data 0.001 (0.002) Batch 3.244 (2.887) Remain 102:30:06 loss: 0.9155 Lr: 0.00200
[2025-09-06 13:35:34,525 INFO misc.py line 117 1395871] Train: [13/100][852/1462] Data 0.002 (0.002) Batch 2.732 (2.887) Remain 102:29:40 loss: 0.3607 Lr: 0.00200
[2025-09-06 13:35:37,345 INFO misc.py line 117 1395871] Train: [13/100][853/1462] Data 0.002 (0.002) Batch 2.821 (2.887) Remain 102:29:27 loss: 0.6192 Lr: 0.00200
[2025-09-06 13:35:39,856 INFO misc.py line 117 1395871] Train: [13/100][854/1462] Data 0.002 (0.002) Batch 2.511 (2.887) Remain 102:28:28 loss: 0.4185 Lr: 0.00200
[2025-09-06 13:35:42,661 INFO misc.py line 117 1395871] Train: [13/100][855/1462] Data 0.002 (0.002) Batch 2.805 (2.886) Remain 102:28:13 loss: 0.5108 Lr: 0.00200
[2025-09-06 13:35:45,482 INFO misc.py line 117 1395871] Train: [13/100][856/1462] Data 0.001 (0.002) Batch 2.821 (2.886) Remain 102:28:00 loss: 0.5005 Lr: 0.00200
[2025-09-06 13:35:48,271 INFO misc.py line 117 1395871] Train: [13/100][857/1462] Data 0.001 (0.002) Batch 2.790 (2.886) Remain 102:27:42 loss: 1.2754 Lr: 0.00200
[2025-09-06 13:35:51,183 INFO misc.py line 117 1395871] Train: [13/100][858/1462] Data 0.002 (0.002) Batch 2.911 (2.886) Remain 102:27:43 loss: 0.3541 Lr: 0.00200
[2025-09-06 13:35:54,281 INFO misc.py line 117 1395871] Train: [13/100][859/1462] Data 0.001 (0.002) Batch 3.098 (2.887) Remain 102:28:12 loss: 0.6099 Lr: 0.00200
[2025-09-06 13:35:57,093 INFO misc.py line 117 1395871] Train: [13/100][860/1462] Data 0.001 (0.002) Batch 2.812 (2.886) Remain 102:27:58 loss: 0.4380 Lr: 0.00200
[2025-09-06 13:36:00,026 INFO misc.py line 117 1395871] Train: [13/100][861/1462] Data 0.002 (0.002) Batch 2.933 (2.887) Remain 102:28:02 loss: 0.2430 Lr: 0.00200
[2025-09-06 13:36:02,811 INFO misc.py line 117 1395871] Train: [13/100][862/1462] Data 0.001 (0.002) Batch 2.784 (2.886) Remain 102:27:44 loss: 0.5230 Lr: 0.00200
[2025-09-06 13:36:05,905 INFO misc.py line 117 1395871] Train: [13/100][863/1462] Data 0.001 (0.002) Batch 3.095 (2.887) Remain 102:28:12 loss: 0.4289 Lr: 0.00200
[2025-09-06 13:36:08,575 INFO misc.py line 117 1395871] Train: [13/100][864/1462] Data 0.001 (0.002) Batch 2.670 (2.886) Remain 102:27:37 loss: 0.7772 Lr: 0.00200
[2025-09-06 13:36:11,180 INFO misc.py line 117 1395871] Train: [13/100][865/1462] Data 0.002 (0.002) Batch 2.605 (2.886) Remain 102:26:52 loss: 0.6386 Lr: 0.00200
[2025-09-06 13:36:14,406 INFO misc.py line 117 1395871] Train: [13/100][866/1462] Data 0.002 (0.002) Batch 3.226 (2.886) Remain 102:27:40 loss: 0.4610 Lr: 0.00200
[2025-09-06 13:36:17,343 INFO misc.py line 117 1395871] Train: [13/100][867/1462] Data 0.001 (0.002) Batch 2.937 (2.887) Remain 102:27:44 loss: 0.5309 Lr: 0.00200
[2025-09-06 13:36:20,184 INFO misc.py line 117 1395871] Train: [13/100][868/1462] Data 0.001 (0.002) Batch 2.841 (2.886) Remain 102:27:35 loss: 0.2799 Lr: 0.00200
[2025-09-06 13:36:22,896 INFO misc.py line 117 1395871] Train: [13/100][869/1462] Data 0.001 (0.002) Batch 2.711 (2.886) Remain 102:27:06 loss: 0.7995 Lr: 0.00200
[2025-09-06 13:36:25,914 INFO misc.py line 117 1395871] Train: [13/100][870/1462] Data 0.002 (0.002) Batch 3.019 (2.886) Remain 102:27:23 loss: 0.5334 Lr: 0.00200
[2025-09-06 13:36:29,094 INFO misc.py line 117 1395871] Train: [13/100][871/1462] Data 0.002 (0.002) Batch 3.180 (2.887) Remain 102:28:03 loss: 0.5097 Lr: 0.00200
[2025-09-06 13:36:31,784 INFO misc.py line 117 1395871] Train: [13/100][872/1462] Data 0.002 (0.002) Batch 2.690 (2.887) Remain 102:27:31 loss: 0.3465 Lr: 0.00200
[2025-09-06 13:36:34,506 INFO misc.py line 117 1395871] Train: [13/100][873/1462] Data 0.002 (0.002) Batch 2.722 (2.886) Remain 102:27:04 loss: 0.3513 Lr: 0.00200
[2025-09-06 13:36:37,217 INFO misc.py line 117 1395871] Train: [13/100][874/1462] Data 0.001 (0.002) Batch 2.711 (2.886) Remain 102:26:36 loss: 0.6866 Lr: 0.00200
[2025-09-06 13:36:40,091 INFO misc.py line 117 1395871] Train: [13/100][875/1462] Data 0.002 (0.002) Batch 2.874 (2.886) Remain 102:26:31 loss: 0.3092 Lr: 0.00200
[2025-09-06 13:36:42,727 INFO misc.py line 117 1395871] Train: [13/100][876/1462] Data 0.001 (0.002) Batch 2.636 (2.886) Remain 102:25:51 loss: 0.3919 Lr: 0.00200
[2025-09-06 13:36:45,681 INFO misc.py line 117 1395871] Train: [13/100][877/1462] Data 0.002 (0.002) Batch 2.954 (2.886) Remain 102:25:58 loss: 0.5771 Lr: 0.00200
[2025-09-06 13:36:48,515 INFO misc.py line 117 1395871] Train: [13/100][878/1462] Data 0.002 (0.002) Batch 2.834 (2.886) Remain 102:25:48 loss: 0.2749 Lr: 0.00200
[2025-09-06 13:36:51,155 INFO misc.py line 117 1395871] Train: [13/100][879/1462] Data 0.001 (0.002) Batch 2.640 (2.886) Remain 102:25:09 loss: 0.5949 Lr: 0.00200
[2025-09-06 13:36:53,822 INFO misc.py line 117 1395871] Train: [13/100][880/1462] Data 0.001 (0.002) Batch 2.667 (2.885) Remain 102:24:34 loss: 0.3985 Lr: 0.00200
[2025-09-06 13:36:56,738 INFO misc.py line 117 1395871] Train: [13/100][881/1462] Data 0.001 (0.002) Batch 2.917 (2.885) Remain 102:24:36 loss: 0.6417 Lr: 0.00200
[2025-09-06 13:36:59,702 INFO misc.py line 117 1395871] Train: [13/100][882/1462] Data 0.002 (0.002) Batch 2.964 (2.885) Remain 102:24:45 loss: 0.3733 Lr: 0.00200
[2025-09-06 13:37:02,904 INFO misc.py line 117 1395871] Train: [13/100][883/1462] Data 0.002 (0.002) Batch 3.201 (2.886) Remain 102:25:28 loss: 0.3547 Lr: 0.00200
[2025-09-06 13:37:05,520 INFO misc.py line 117 1395871] Train: [13/100][884/1462] Data 0.002 (0.002) Batch 2.616 (2.886) Remain 102:24:46 loss: 0.4332 Lr: 0.00200
[2025-09-06 13:37:08,031 INFO misc.py line 117 1395871] Train: [13/100][885/1462] Data 0.002 (0.002) Batch 2.511 (2.885) Remain 102:23:49 loss: 0.5823 Lr: 0.00200
[2025-09-06 13:37:10,777 INFO misc.py line 117 1395871] Train: [13/100][886/1462] Data 0.002 (0.002) Batch 2.745 (2.885) Remain 102:23:26 loss: 0.5726 Lr: 0.00200
[2025-09-06 13:37:13,513 INFO misc.py line 117 1395871] Train: [13/100][887/1462] Data 0.002 (0.002) Batch 2.736 (2.885) Remain 102:23:01 loss: 0.2299 Lr: 0.00200
[2025-09-06 13:37:16,214 INFO misc.py line 117 1395871] Train: [13/100][888/1462] Data 0.002 (0.002) Batch 2.701 (2.885) Remain 102:22:32 loss: 0.2069 Lr: 0.00200
[2025-09-06 13:37:18,995 INFO misc.py line 117 1395871] Train: [13/100][889/1462] Data 0.002 (0.002) Batch 2.781 (2.884) Remain 102:22:14 loss: 0.2961 Lr: 0.00200
[2025-09-06 13:37:21,435 INFO misc.py line 117 1395871] Train: [13/100][890/1462] Data 0.002 (0.002) Batch 2.440 (2.884) Remain 102:21:07 loss: 1.0774 Lr: 0.00200
[2025-09-06 13:37:24,147 INFO misc.py line 117 1395871] Train: [13/100][891/1462] Data 0.002 (0.002) Batch 2.712 (2.884) Remain 102:20:39 loss: 0.5642 Lr: 0.00200
[2025-09-06 13:37:26,701 INFO misc.py line 117 1395871] Train: [13/100][892/1462] Data 0.001 (0.002) Batch 2.555 (2.883) Remain 102:19:49 loss: 0.7211 Lr: 0.00200
[2025-09-06 13:37:29,910 INFO misc.py line 117 1395871] Train: [13/100][893/1462] Data 0.001 (0.002) Batch 3.209 (2.884) Remain 102:20:33 loss: 0.5886 Lr: 0.00200
[2025-09-06 13:37:32,861 INFO misc.py line 117 1395871] Train: [13/100][894/1462] Data 0.001 (0.002) Batch 2.950 (2.884) Remain 102:20:40 loss: 0.6120 Lr: 0.00200
[2025-09-06 13:37:36,008 INFO misc.py line 117 1395871] Train: [13/100][895/1462] Data 0.002 (0.002) Batch 3.147 (2.884) Remain 102:21:15 loss: 0.4911 Lr: 0.00200
[2025-09-06 13:37:39,015 INFO misc.py line 117 1395871] Train: [13/100][896/1462] Data 0.001 (0.002) Batch 3.007 (2.884) Remain 102:21:29 loss: 0.6717 Lr: 0.00200
[2025-09-06 13:37:42,220 INFO misc.py line 117 1395871] Train: [13/100][897/1462] Data 0.001 (0.002) Batch 3.205 (2.885) Remain 102:22:12 loss: 0.2681 Lr: 0.00200
[2025-09-06 13:37:44,989 INFO misc.py line 117 1395871] Train: [13/100][898/1462] Data 0.002 (0.002) Batch 2.769 (2.884) Remain 102:21:53 loss: 0.5578 Lr: 0.00200
[2025-09-06 13:37:47,655 INFO misc.py line 117 1395871] Train: [13/100][899/1462] Data 0.001 (0.002) Batch 2.666 (2.884) Remain 102:21:19 loss: 0.3782 Lr: 0.00200
[2025-09-06 13:37:50,640 INFO misc.py line 117 1395871] Train: [13/100][900/1462] Data 0.001 (0.002) Batch 2.985 (2.884) Remain 102:21:30 loss: 0.8003 Lr: 0.00200
[2025-09-06 13:37:53,680 INFO misc.py line 117 1395871] Train: [13/100][901/1462] Data 0.002 (0.002) Batch 3.040 (2.885) Remain 102:21:50 loss: 0.4483 Lr: 0.00200
[2025-09-06 13:37:56,652 INFO misc.py line 117 1395871] Train: [13/100][902/1462] Data 0.001 (0.002) Batch 2.972 (2.885) Remain 102:21:59 loss: 0.8360 Lr: 0.00200
[2025-09-06 13:37:59,516 INFO misc.py line 117 1395871] Train: [13/100][903/1462] Data 0.001 (0.002) Batch 2.863 (2.885) Remain 102:21:53 loss: 0.3483 Lr: 0.00200
[2025-09-06 13:38:02,210 INFO misc.py line 117 1395871] Train: [13/100][904/1462] Data 0.001 (0.002) Batch 2.694 (2.884) Remain 102:21:23 loss: 0.5509 Lr: 0.00200
[2025-09-06 13:38:04,964 INFO misc.py line 117 1395871] Train: [13/100][905/1462] Data 0.001 (0.002) Batch 2.754 (2.884) Remain 102:21:02 loss: 0.5574 Lr: 0.00200
[2025-09-06 13:38:07,602 INFO misc.py line 117 1395871] Train: [13/100][906/1462] Data 0.002 (0.002) Batch 2.638 (2.884) Remain 102:20:24 loss: 0.5397 Lr: 0.00200
[2025-09-06 13:38:10,618 INFO misc.py line 117 1395871] Train: [13/100][907/1462] Data 0.001 (0.002) Batch 3.016 (2.884) Remain 102:20:40 loss: 0.5985 Lr: 0.00200
[2025-09-06 13:38:13,609 INFO misc.py line 117 1395871] Train: [13/100][908/1462] Data 0.002 (0.002) Batch 2.991 (2.884) Remain 102:20:52 loss: 0.4990 Lr: 0.00200
[2025-09-06 13:38:16,675 INFO misc.py line 117 1395871] Train: [13/100][909/1462] Data 0.001 (0.002) Batch 3.066 (2.884) Remain 102:21:15 loss: 0.4501 Lr: 0.00200
[2025-09-06 13:38:19,410 INFO misc.py line 117 1395871] Train: [13/100][910/1462] Data 0.002 (0.002) Batch 2.735 (2.884) Remain 102:20:51 loss: 0.3207 Lr: 0.00200
[2025-09-06 13:38:22,440 INFO misc.py line 117 1395871] Train: [13/100][911/1462] Data 0.001 (0.002) Batch 3.031 (2.884) Remain 102:21:09 loss: 0.6345 Lr: 0.00200
[2025-09-06 13:38:25,357 INFO misc.py line 117 1395871] Train: [13/100][912/1462] Data 0.001 (0.002) Batch 2.917 (2.884) Remain 102:21:10 loss: 0.5317 Lr: 0.00200
[2025-09-06 13:38:27,975 INFO misc.py line 117 1395871] Train: [13/100][913/1462] Data 0.002 (0.002) Batch 2.617 (2.884) Remain 102:20:30 loss: 0.5650 Lr: 0.00200
[2025-09-06 13:38:31,327 INFO misc.py line 117 1395871] Train: [13/100][914/1462] Data 0.001 (0.002) Batch 3.353 (2.885) Remain 102:21:33 loss: 0.4500 Lr: 0.00200
[2025-09-06 13:38:33,936 INFO misc.py line 117 1395871] Train: [13/100][915/1462] Data 0.001 (0.002) Batch 2.608 (2.884) Remain 102:20:51 loss: 0.3481 Lr: 0.00200
[2025-09-06 13:38:36,918 INFO misc.py line 117 1395871] Train: [13/100][916/1462] Data 0.001 (0.002) Batch 2.982 (2.884) Remain 102:21:02 loss: 0.4783 Lr: 0.00200
[2025-09-06 13:38:39,909 INFO misc.py line 117 1395871] Train: [13/100][917/1462] Data 0.002 (0.002) Batch 2.991 (2.885) Remain 102:21:14 loss: 0.4651 Lr: 0.00200
[2025-09-06 13:38:42,759 INFO misc.py line 117 1395871] Train: [13/100][918/1462] Data 0.002 (0.002) Batch 2.850 (2.885) Remain 102:21:06 loss: 0.2523 Lr: 0.00200
[2025-09-06 13:38:45,460 INFO misc.py line 117 1395871] Train: [13/100][919/1462] Data 0.001 (0.002) Batch 2.701 (2.884) Remain 102:20:38 loss: 0.4870 Lr: 0.00200
[2025-09-06 13:38:48,311 INFO misc.py line 117 1395871] Train: [13/100][920/1462] Data 0.001 (0.002) Batch 2.851 (2.884) Remain 102:20:30 loss: 0.6058 Lr: 0.00200
[2025-09-06 13:38:51,086 INFO misc.py line 117 1395871] Train: [13/100][921/1462] Data 0.002 (0.002) Batch 2.775 (2.884) Remain 102:20:12 loss: 0.2986 Lr: 0.00200
[2025-09-06 13:38:54,065 INFO misc.py line 117 1395871] Train: [13/100][922/1462] Data 0.002 (0.002) Batch 2.978 (2.884) Remain 102:20:23 loss: 0.7095 Lr: 0.00200
[2025-09-06 13:38:56,948 INFO misc.py line 117 1395871] Train: [13/100][923/1462] Data 0.001 (0.002) Batch 2.884 (2.884) Remain 102:20:20 loss: 0.2822 Lr: 0.00200
[2025-09-06 13:38:59,938 INFO misc.py line 117 1395871] Train: [13/100][924/1462] Data 0.001 (0.002) Batch 2.990 (2.884) Remain 102:20:31 loss: 0.2629 Lr: 0.00200
[2025-09-06 13:39:02,931 INFO misc.py line 117 1395871] Train: [13/100][925/1462] Data 0.001 (0.002) Batch 2.993 (2.885) Remain 102:20:44 loss: 0.4161 Lr: 0.00200
[2025-09-06 13:39:05,918 INFO misc.py line 117 1395871] Train: [13/100][926/1462] Data 0.002 (0.002) Batch 2.986 (2.885) Remain 102:20:55 loss: 0.4046 Lr: 0.00200
[2025-09-06 13:39:08,924 INFO misc.py line 117 1395871] Train: [13/100][927/1462] Data 0.002 (0.002) Batch 3.006 (2.885) Remain 102:21:09 loss: 0.8891 Lr: 0.00200
[2025-09-06 13:39:11,456 INFO misc.py line 117 1395871] Train: [13/100][928/1462] Data 0.002 (0.002) Batch 2.532 (2.884) Remain 102:20:17 loss: 0.4503 Lr: 0.00200
[2025-09-06 13:39:13,977 INFO misc.py line 117 1395871] Train: [13/100][929/1462] Data 0.002 (0.002) Batch 2.521 (2.884) Remain 102:19:24 loss: 0.4497 Lr: 0.00200
[2025-09-06 13:39:16,620 INFO misc.py line 117 1395871] Train: [13/100][930/1462] Data 0.002 (0.002) Batch 2.642 (2.884) Remain 102:18:48 loss: 0.6306 Lr: 0.00200
[2025-09-06 13:39:19,659 INFO misc.py line 117 1395871] Train: [13/100][931/1462] Data 0.002 (0.002) Batch 3.040 (2.884) Remain 102:19:06 loss: 0.7070 Lr: 0.00200
[2025-09-06 13:39:22,839 INFO misc.py line 117 1395871] Train: [13/100][932/1462] Data 0.002 (0.002) Batch 3.180 (2.884) Remain 102:19:44 loss: 0.4880 Lr: 0.00200
[2025-09-06 13:39:26,236 INFO misc.py line 117 1395871] Train: [13/100][933/1462] Data 0.001 (0.002) Batch 3.397 (2.885) Remain 102:20:52 loss: 0.3355 Lr: 0.00200
[2025-09-06 13:39:29,180 INFO misc.py line 117 1395871] Train: [13/100][934/1462] Data 0.002 (0.002) Batch 2.944 (2.885) Remain 102:20:57 loss: 0.3669 Lr: 0.00200
[2025-09-06 13:39:31,966 INFO misc.py line 117 1395871] Train: [13/100][935/1462] Data 0.002 (0.002) Batch 2.786 (2.885) Remain 102:20:41 loss: 0.3438 Lr: 0.00200
[2025-09-06 13:39:34,928 INFO misc.py line 117 1395871] Train: [13/100][936/1462] Data 0.002 (0.002) Batch 2.962 (2.885) Remain 102:20:48 loss: 0.4848 Lr: 0.00200
[2025-09-06 13:39:37,330 INFO misc.py line 117 1395871] Train: [13/100][937/1462] Data 0.001 (0.002) Batch 2.402 (2.884) Remain 102:19:39 loss: 0.3528 Lr: 0.00200
[2025-09-06 13:39:40,097 INFO misc.py line 117 1395871] Train: [13/100][938/1462] Data 0.001 (0.002) Batch 2.767 (2.884) Remain 102:19:21 loss: 0.3492 Lr: 0.00200
[2025-09-06 13:39:43,094 INFO misc.py line 117 1395871] Train: [13/100][939/1462] Data 0.002 (0.002) Batch 2.996 (2.884) Remain 102:19:33 loss: 0.3094 Lr: 0.00200
[2025-09-06 13:39:46,076 INFO misc.py line 117 1395871] Train: [13/100][940/1462] Data 0.001 (0.002) Batch 2.982 (2.884) Remain 102:19:43 loss: 0.4023 Lr: 0.00200
[2025-09-06 13:39:48,879 INFO misc.py line 117 1395871] Train: [13/100][941/1462] Data 0.002 (0.002) Batch 2.803 (2.884) Remain 102:19:30 loss: 0.4150 Lr: 0.00200
[2025-09-06 13:39:51,749 INFO misc.py line 117 1395871] Train: [13/100][942/1462] Data 0.001 (0.002) Batch 2.869 (2.884) Remain 102:19:25 loss: 0.4729 Lr: 0.00200
[2025-09-06 13:39:54,451 INFO misc.py line 117 1395871] Train: [13/100][943/1462] Data 0.002 (0.002) Batch 2.702 (2.884) Remain 102:18:57 loss: 0.3022 Lr: 0.00200
[2025-09-06 13:39:57,133 INFO misc.py line 117 1395871] Train: [13/100][944/1462] Data 0.001 (0.002) Batch 2.682 (2.884) Remain 102:18:27 loss: 0.5941 Lr: 0.00200
[2025-09-06 13:39:59,837 INFO misc.py line 117 1395871] Train: [13/100][945/1462] Data 0.002 (0.002) Batch 2.704 (2.884) Remain 102:17:59 loss: 0.4945 Lr: 0.00200
[2025-09-06 13:40:02,721 INFO misc.py line 117 1395871] Train: [13/100][946/1462] Data 0.002 (0.002) Batch 2.884 (2.884) Remain 102:17:57 loss: 0.3733 Lr: 0.00200
[2025-09-06 13:40:05,553 INFO misc.py line 117 1395871] Train: [13/100][947/1462] Data 0.001 (0.002) Batch 2.832 (2.884) Remain 102:17:47 loss: 0.3763 Lr: 0.00200
[2025-09-06 13:40:08,395 INFO misc.py line 117 1395871] Train: [13/100][948/1462] Data 0.001 (0.002) Batch 2.842 (2.884) Remain 102:17:38 loss: 0.3176 Lr: 0.00200
[2025-09-06 13:40:11,158 INFO misc.py line 117 1395871] Train: [13/100][949/1462] Data 0.002 (0.002) Batch 2.763 (2.883) Remain 102:17:19 loss: 0.2639 Lr: 0.00200
[2025-09-06 13:40:14,335 INFO misc.py line 117 1395871] Train: [13/100][950/1462] Data 0.002 (0.002) Batch 3.177 (2.884) Remain 102:17:56 loss: 0.4287 Lr: 0.00200
[2025-09-06 13:40:17,128 INFO misc.py line 117 1395871] Train: [13/100][951/1462] Data 0.001 (0.002) Batch 2.792 (2.884) Remain 102:17:41 loss: 0.2586 Lr: 0.00200
[2025-09-06 13:40:20,146 INFO misc.py line 117 1395871] Train: [13/100][952/1462] Data 0.002 (0.002) Batch 3.018 (2.884) Remain 102:17:56 loss: 0.3484 Lr: 0.00200
[2025-09-06 13:40:22,933 INFO misc.py line 117 1395871] Train: [13/100][953/1462] Data 0.002 (0.002) Batch 2.787 (2.884) Remain 102:17:40 loss: 0.3375 Lr: 0.00200
[2025-09-06 13:40:26,264 INFO misc.py line 117 1395871] Train: [13/100][954/1462] Data 0.001 (0.002) Batch 3.331 (2.884) Remain 102:18:37 loss: 0.2658 Lr: 0.00200
[2025-09-06 13:40:29,374 INFO misc.py line 117 1395871] Train: [13/100][955/1462] Data 0.002 (0.002) Batch 3.110 (2.884) Remain 102:19:04 loss: 0.6395 Lr: 0.00200
[2025-09-06 13:40:32,268 INFO misc.py line 117 1395871] Train: [13/100][956/1462] Data 0.001 (0.002) Batch 2.894 (2.884) Remain 102:19:03 loss: 0.4994 Lr: 0.00200
[2025-09-06 13:40:35,289 INFO misc.py line 117 1395871] Train: [13/100][957/1462] Data 0.002 (0.002) Batch 3.021 (2.885) Remain 102:19:18 loss: 0.2399 Lr: 0.00200
[2025-09-06 13:40:38,549 INFO misc.py line 117 1395871] Train: [13/100][958/1462] Data 0.001 (0.002) Batch 3.261 (2.885) Remain 102:20:06 loss: 0.7962 Lr: 0.00200
[2025-09-06 13:40:41,401 INFO misc.py line 117 1395871] Train: [13/100][959/1462] Data 0.001 (0.002) Batch 2.852 (2.885) Remain 102:19:58 loss: 0.4362 Lr: 0.00200
[2025-09-06 13:40:44,333 INFO misc.py line 117 1395871] Train: [13/100][960/1462] Data 0.002 (0.002) Batch 2.932 (2.885) Remain 102:20:02 loss: 0.2744 Lr: 0.00200
[2025-09-06 13:40:47,005 INFO misc.py line 117 1395871] Train: [13/100][961/1462] Data 0.001 (0.002) Batch 2.672 (2.885) Remain 102:19:30 loss: 0.3770 Lr: 0.00200
[2025-09-06 13:40:50,173 INFO misc.py line 117 1395871] Train: [13/100][962/1462] Data 0.002 (0.002) Batch 3.168 (2.885) Remain 102:20:05 loss: 0.3361 Lr: 0.00200
[2025-09-06 13:40:52,932 INFO misc.py line 117 1395871] Train: [13/100][963/1462] Data 0.002 (0.002) Batch 2.759 (2.885) Remain 102:19:46 loss: 0.3502 Lr: 0.00200
[2025-09-06 13:40:55,982 INFO misc.py line 117 1395871] Train: [13/100][964/1462] Data 0.001 (0.002) Batch 3.049 (2.885) Remain 102:20:05 loss: 0.5583 Lr: 0.00200
[2025-09-06 13:40:58,862 INFO misc.py line 117 1395871] Train: [13/100][965/1462] Data 0.002 (0.002) Batch 2.880 (2.885) Remain 102:20:01 loss: 0.3690 Lr: 0.00200
[2025-09-06 13:41:01,744 INFO misc.py line 117 1395871] Train: [13/100][966/1462] Data 0.001 (0.002) Batch 2.882 (2.885) Remain 102:19:58 loss: 0.2712 Lr: 0.00200
[2025-09-06 13:41:04,703 INFO misc.py line 117 1395871] Train: [13/100][967/1462] Data 0.002 (0.002) Batch 2.959 (2.885) Remain 102:20:05 loss: 0.3700 Lr: 0.00200
[2025-09-06 13:41:07,414 INFO misc.py line 117 1395871] Train: [13/100][968/1462] Data 0.002 (0.002) Batch 2.711 (2.885) Remain 102:19:39 loss: 0.5682 Lr: 0.00200
[2025-09-06 13:41:10,242 INFO misc.py line 117 1395871] Train: [13/100][969/1462] Data 0.002 (0.002) Batch 2.828 (2.885) Remain 102:19:28 loss: 0.7433 Lr: 0.00200
[2025-09-06 13:41:13,080 INFO misc.py line 117 1395871] Train: [13/100][970/1462] Data 0.002 (0.002) Batch 2.838 (2.885) Remain 102:19:19 loss: 0.6406 Lr: 0.00200
[2025-09-06 13:41:16,131 INFO misc.py line 117 1395871] Train: [13/100][971/1462] Data 0.001 (0.002) Batch 3.051 (2.885) Remain 102:19:38 loss: 0.3928 Lr: 0.00200
[2025-09-06 13:41:19,079 INFO misc.py line 117 1395871] Train: [13/100][972/1462] Data 0.002 (0.002) Batch 2.947 (2.885) Remain 102:19:44 loss: 0.1945 Lr: 0.00200
[2025-09-06 13:41:21,942 INFO misc.py line 117 1395871] Train: [13/100][973/1462] Data 0.002 (0.002) Batch 2.864 (2.885) Remain 102:19:38 loss: 0.5262 Lr: 0.00200
[2025-09-06 13:41:24,773 INFO misc.py line 117 1395871] Train: [13/100][974/1462] Data 0.002 (0.002) Batch 2.831 (2.885) Remain 102:19:28 loss: 0.2421 Lr: 0.00200
[2025-09-06 13:41:27,406 INFO misc.py line 117 1395871] Train: [13/100][975/1462] Data 0.001 (0.002) Batch 2.633 (2.885) Remain 102:18:52 loss: 0.7955 Lr: 0.00200
[2025-09-06 13:41:30,094 INFO misc.py line 117 1395871] Train: [13/100][976/1462] Data 0.002 (0.002) Batch 2.688 (2.885) Remain 102:18:23 loss: 0.4815 Lr: 0.00200
[2025-09-06 13:41:33,118 INFO misc.py line 117 1395871] Train: [13/100][977/1462] Data 0.002 (0.002) Batch 3.023 (2.885) Remain 102:18:38 loss: 0.4868 Lr: 0.00200
[2025-09-06 13:41:36,019 INFO misc.py line 117 1395871] Train: [13/100][978/1462] Data 0.001 (0.002) Batch 2.902 (2.885) Remain 102:18:38 loss: 0.3681 Lr: 0.00200
[2025-09-06 13:41:38,684 INFO misc.py line 117 1395871] Train: [13/100][979/1462] Data 0.002 (0.002) Batch 2.664 (2.885) Remain 102:18:06 loss: 0.4600 Lr: 0.00200
[2025-09-06 13:41:41,314 INFO misc.py line 117 1395871] Train: [13/100][980/1462] Data 0.002 (0.002) Batch 2.631 (2.884) Remain 102:17:30 loss: 0.3213 Lr: 0.00200
[2025-09-06 13:41:44,291 INFO misc.py line 117 1395871] Train: [13/100][981/1462] Data 0.002 (0.002) Batch 2.976 (2.884) Remain 102:17:39 loss: 0.6989 Lr: 0.00200
[2025-09-06 13:41:47,281 INFO misc.py line 117 1395871] Train: [13/100][982/1462] Data 0.002 (0.002) Batch 2.990 (2.884) Remain 102:17:50 loss: 0.5924 Lr: 0.00200
[2025-09-06 13:41:49,949 INFO misc.py line 117 1395871] Train: [13/100][983/1462] Data 0.002 (0.002) Batch 2.668 (2.884) Remain 102:17:19 loss: 0.3159 Lr: 0.00200
[2025-09-06 13:41:52,874 INFO misc.py line 117 1395871] Train: [13/100][984/1462] Data 0.002 (0.002) Batch 2.925 (2.884) Remain 102:17:21 loss: 0.4528 Lr: 0.00200
[2025-09-06 13:41:55,467 INFO misc.py line 117 1395871] Train: [13/100][985/1462] Data 0.002 (0.002) Batch 2.593 (2.884) Remain 102:16:41 loss: 0.3898 Lr: 0.00200
[2025-09-06 13:41:58,670 INFO misc.py line 117 1395871] Train: [13/100][986/1462] Data 0.001 (0.002) Batch 3.203 (2.884) Remain 102:17:19 loss: 0.3573 Lr: 0.00200
[2025-09-06 13:42:01,618 INFO misc.py line 117 1395871] Train: [13/100][987/1462] Data 0.002 (0.002) Batch 2.948 (2.884) Remain 102:17:25 loss: 0.3690 Lr: 0.00200
[2025-09-06 13:42:04,305 INFO misc.py line 117 1395871] Train: [13/100][988/1462] Data 0.001 (0.002) Batch 2.687 (2.884) Remain 102:16:56 loss: 0.3187 Lr: 0.00200
[2025-09-06 13:42:06,738 INFO misc.py line 117 1395871] Train: [13/100][989/1462] Data 0.001 (0.002) Batch 2.432 (2.884) Remain 102:15:55 loss: 0.8104 Lr: 0.00200
[2025-09-06 13:42:09,599 INFO misc.py line 117 1395871] Train: [13/100][990/1462] Data 0.001 (0.002) Batch 2.861 (2.884) Remain 102:15:49 loss: 0.5571 Lr: 0.00200
[2025-09-06 13:42:12,400 INFO misc.py line 117 1395871] Train: [13/100][991/1462] Data 0.002 (0.002) Batch 2.801 (2.884) Remain 102:15:35 loss: 0.4230 Lr: 0.00200
[2025-09-06 13:42:15,369 INFO misc.py line 117 1395871] Train: [13/100][992/1462] Data 0.002 (0.002) Batch 2.969 (2.884) Remain 102:15:44 loss: 0.6334 Lr: 0.00200
[2025-09-06 13:42:18,044 INFO misc.py line 117 1395871] Train: [13/100][993/1462] Data 0.002 (0.002) Batch 2.675 (2.883) Remain 102:15:14 loss: 0.3278 Lr: 0.00200
[2025-09-06 13:42:20,828 INFO misc.py line 117 1395871] Train: [13/100][994/1462] Data 0.002 (0.002) Batch 2.784 (2.883) Remain 102:14:58 loss: 0.5593 Lr: 0.00200
[2025-09-06 13:42:23,665 INFO misc.py line 117 1395871] Train: [13/100][995/1462] Data 0.002 (0.002) Batch 2.836 (2.883) Remain 102:14:49 loss: 0.3264 Lr: 0.00200
[2025-09-06 13:42:26,724 INFO misc.py line 117 1395871] Train: [13/100][996/1462] Data 0.002 (0.002) Batch 3.060 (2.884) Remain 102:15:09 loss: 0.5238 Lr: 0.00200
[2025-09-06 13:42:29,431 INFO misc.py line 117 1395871] Train: [13/100][997/1462] Data 0.002 (0.002) Batch 2.707 (2.883) Remain 102:14:43 loss: 0.4039 Lr: 0.00200
[2025-09-06 13:42:32,394 INFO misc.py line 117 1395871] Train: [13/100][998/1462] Data 0.002 (0.002) Batch 2.963 (2.883) Remain 102:14:51 loss: 0.9725 Lr: 0.00200
[2025-09-06 13:42:35,122 INFO misc.py line 117 1395871] Train: [13/100][999/1462] Data 0.002 (0.002) Batch 2.727 (2.883) Remain 102:14:28 loss: 0.6251 Lr: 0.00200
[2025-09-06 13:42:38,136 INFO misc.py line 117 1395871] Train: [13/100][1000/1462] Data 0.002 (0.002) Batch 3.014 (2.883) Remain 102:14:42 loss: 0.6313 Lr: 0.00200
[2025-09-06 13:42:41,169 INFO misc.py line 117 1395871] Train: [13/100][1001/1462] Data 0.001 (0.002) Batch 3.033 (2.884) Remain 102:14:58 loss: 0.4711 Lr: 0.00200
[2025-09-06 13:42:44,104 INFO misc.py line 117 1395871] Train: [13/100][1002/1462] Data 0.002 (0.002) Batch 2.936 (2.884) Remain 102:15:02 loss: 0.3751 Lr: 0.00200
[2025-09-06 13:42:47,002 INFO misc.py line 117 1395871] Train: [13/100][1003/1462] Data 0.002 (0.002) Batch 2.897 (2.884) Remain 102:15:01 loss: 0.5630 Lr: 0.00200
[2025-09-06 13:42:50,113 INFO misc.py line 117 1395871] Train: [13/100][1004/1462] Data 0.002 (0.002) Batch 3.111 (2.884) Remain 102:15:27 loss: 0.3881 Lr: 0.00200
[2025-09-06 13:42:52,867 INFO misc.py line 117 1395871] Train: [13/100][1005/1462] Data 0.001 (0.002) Batch 2.754 (2.884) Remain 102:15:07 loss: 0.5143 Lr: 0.00200
[2025-09-06 13:42:55,595 INFO misc.py line 117 1395871] Train: [13/100][1006/1462] Data 0.002 (0.002) Batch 2.728 (2.884) Remain 102:14:45 loss: 0.3150 Lr: 0.00200
[2025-09-06 13:42:58,160 INFO misc.py line 117 1395871] Train: [13/100][1007/1462] Data 0.002 (0.002) Batch 2.565 (2.883) Remain 102:14:01 loss: 0.4502 Lr: 0.00200
[2025-09-06 13:43:01,013 INFO misc.py line 117 1395871] Train: [13/100][1008/1462] Data 0.002 (0.002) Batch 2.854 (2.883) Remain 102:13:55 loss: 0.4870 Lr: 0.00200
[2025-09-06 13:43:03,791 INFO misc.py line 117 1395871] Train: [13/100][1009/1462] Data 0.001 (0.002) Batch 2.778 (2.883) Remain 102:13:38 loss: 0.5517 Lr: 0.00200
[2025-09-06 13:43:06,918 INFO misc.py line 117 1395871] Train: [13/100][1010/1462] Data 0.001 (0.002) Batch 3.128 (2.883) Remain 102:14:06 loss: 0.6694 Lr: 0.00200
[2025-09-06 13:43:09,718 INFO misc.py line 117 1395871] Train: [13/100][1011/1462] Data 0.001 (0.002) Batch 2.799 (2.883) Remain 102:13:53 loss: 0.6541 Lr: 0.00200
[2025-09-06 13:43:12,539 INFO misc.py line 117 1395871] Train: [13/100][1012/1462] Data 0.002 (0.002) Batch 2.821 (2.883) Remain 102:13:42 loss: 0.5347 Lr: 0.00200
[2025-09-06 13:43:15,525 INFO misc.py line 117 1395871] Train: [13/100][1013/1462] Data 0.002 (0.002) Batch 2.987 (2.883) Remain 102:13:52 loss: 0.4267 Lr: 0.00200
[2025-09-06 13:43:18,242 INFO misc.py line 117 1395871] Train: [13/100][1014/1462] Data 0.001 (0.002) Batch 2.717 (2.883) Remain 102:13:28 loss: 0.3405 Lr: 0.00200
[2025-09-06 13:43:21,139 INFO misc.py line 117 1395871] Train: [13/100][1015/1462] Data 0.001 (0.002) Batch 2.897 (2.883) Remain 102:13:27 loss: 0.5400 Lr: 0.00200
[2025-09-06 13:43:23,810 INFO misc.py line 117 1395871] Train: [13/100][1016/1462] Data 0.001 (0.002) Batch 2.671 (2.883) Remain 102:12:58 loss: 0.3137 Lr: 0.00200
[2025-09-06 13:43:26,843 INFO misc.py line 117 1395871] Train: [13/100][1017/1462] Data 0.001 (0.002) Batch 3.033 (2.883) Remain 102:13:14 loss: 0.3952 Lr: 0.00200
[2025-09-06 13:43:29,395 INFO misc.py line 117 1395871] Train: [13/100][1018/1462] Data 0.001 (0.002) Batch 2.552 (2.883) Remain 102:12:29 loss: 0.3955 Lr: 0.00200
[2025-09-06 13:43:32,138 INFO misc.py line 117 1395871] Train: [13/100][1019/1462] Data 0.002 (0.002) Batch 2.743 (2.883) Remain 102:12:09 loss: 0.5558 Lr: 0.00200
[2025-09-06 13:43:34,750 INFO misc.py line 117 1395871] Train: [13/100][1020/1462] Data 0.001 (0.002) Batch 2.612 (2.882) Remain 102:11:32 loss: 0.6956 Lr: 0.00200
[2025-09-06 13:43:37,655 INFO misc.py line 117 1395871] Train: [13/100][1021/1462] Data 0.001 (0.002) Batch 2.905 (2.882) Remain 102:11:32 loss: 0.4683 Lr: 0.00200
[2025-09-06 13:43:40,541 INFO misc.py line 117 1395871] Train: [13/100][1022/1462] Data 0.002 (0.002) Batch 2.886 (2.882) Remain 102:11:29 loss: 0.3403 Lr: 0.00200
[2025-09-06 13:43:43,288 INFO misc.py line 117 1395871] Train: [13/100][1023/1462] Data 0.002 (0.002) Batch 2.747 (2.882) Remain 102:11:10 loss: 0.2508 Lr: 0.00200
[2025-09-06 13:43:46,046 INFO misc.py line 117 1395871] Train: [13/100][1024/1462] Data 0.001 (0.002) Batch 2.758 (2.882) Remain 102:10:51 loss: 0.3725 Lr: 0.00200
[2025-09-06 13:43:49,076 INFO misc.py line 117 1395871] Train: [13/100][1025/1462] Data 0.001 (0.002) Batch 3.030 (2.882) Remain 102:11:07 loss: 0.2527 Lr: 0.00200
[2025-09-06 13:43:51,904 INFO misc.py line 117 1395871] Train: [13/100][1026/1462] Data 0.001 (0.002) Batch 2.828 (2.882) Remain 102:10:57 loss: 0.4214 Lr: 0.00200
[2025-09-06 13:43:54,701 INFO misc.py line 117 1395871] Train: [13/100][1027/1462] Data 0.001 (0.002) Batch 2.796 (2.882) Remain 102:10:44 loss: 0.4443 Lr: 0.00200
[2025-09-06 13:43:57,694 INFO misc.py line 117 1395871] Train: [13/100][1028/1462] Data 0.001 (0.002) Batch 2.994 (2.882) Remain 102:10:55 loss: 0.5504 Lr: 0.00200
[2025-09-06 13:44:00,537 INFO misc.py line 117 1395871] Train: [13/100][1029/1462] Data 0.001 (0.002) Batch 2.843 (2.882) Remain 102:10:47 loss: 0.5131 Lr: 0.00200
[2025-09-06 13:44:03,227 INFO misc.py line 117 1395871] Train: [13/100][1030/1462] Data 0.001 (0.002) Batch 2.690 (2.882) Remain 102:10:20 loss: 0.6311 Lr: 0.00200
[2025-09-06 13:44:06,392 INFO misc.py line 117 1395871] Train: [13/100][1031/1462] Data 0.001 (0.002) Batch 3.165 (2.882) Remain 102:10:52 loss: 0.4692 Lr: 0.00200
[2025-09-06 13:44:09,325 INFO misc.py line 117 1395871] Train: [13/100][1032/1462] Data 0.001 (0.002) Batch 2.933 (2.882) Remain 102:10:56 loss: 0.6219 Lr: 0.00200
[2025-09-06 13:44:12,302 INFO misc.py line 117 1395871] Train: [13/100][1033/1462] Data 0.001 (0.002) Batch 2.976 (2.882) Remain 102:11:04 loss: 0.4288 Lr: 0.00200
[2025-09-06 13:44:15,033 INFO misc.py line 117 1395871] Train: [13/100][1034/1462] Data 0.002 (0.002) Batch 2.732 (2.882) Remain 102:10:43 loss: 0.4167 Lr: 0.00200
[2025-09-06 13:44:17,739 INFO misc.py line 117 1395871] Train: [13/100][1035/1462] Data 0.001 (0.002) Batch 2.706 (2.882) Remain 102:10:18 loss: 0.3652 Lr: 0.00200
[2025-09-06 13:44:20,687 INFO misc.py line 117 1395871] Train: [13/100][1036/1462] Data 0.001 (0.002) Batch 2.947 (2.882) Remain 102:10:23 loss: 0.1590 Lr: 0.00200
[2025-09-06 13:44:23,691 INFO misc.py line 117 1395871] Train: [13/100][1037/1462] Data 0.001 (0.002) Batch 3.004 (2.882) Remain 102:10:36 loss: 0.2247 Lr: 0.00200
[2025-09-06 13:44:26,684 INFO misc.py line 117 1395871] Train: [13/100][1038/1462] Data 0.001 (0.002) Batch 2.993 (2.882) Remain 102:10:46 loss: 0.4461 Lr: 0.00200
[2025-09-06 13:44:29,533 INFO misc.py line 117 1395871] Train: [13/100][1039/1462] Data 0.001 (0.002) Batch 2.849 (2.882) Remain 102:10:39 loss: 0.3170 Lr: 0.00200
[2025-09-06 13:44:32,765 INFO misc.py line 117 1395871] Train: [13/100][1040/1462] Data 0.002 (0.002) Batch 3.233 (2.883) Remain 102:11:20 loss: 0.5405 Lr: 0.00200
[2025-09-06 13:44:35,567 INFO misc.py line 117 1395871] Train: [13/100][1041/1462] Data 0.001 (0.002) Batch 2.801 (2.883) Remain 102:11:07 loss: 0.4614 Lr: 0.00200
[2025-09-06 13:44:38,450 INFO misc.py line 117 1395871] Train: [13/100][1042/1462] Data 0.001 (0.002) Batch 2.883 (2.883) Remain 102:11:04 loss: 0.6786 Lr: 0.00200
[2025-09-06 13:44:41,102 INFO misc.py line 117 1395871] Train: [13/100][1043/1462] Data 0.002 (0.002) Batch 2.652 (2.882) Remain 102:10:33 loss: 0.3158 Lr: 0.00200
[2025-09-06 13:44:43,619 INFO misc.py line 117 1395871] Train: [13/100][1044/1462] Data 0.001 (0.002) Batch 2.517 (2.882) Remain 102:09:45 loss: 0.6868 Lr: 0.00200
[2025-09-06 13:44:46,370 INFO misc.py line 117 1395871] Train: [13/100][1045/1462] Data 0.002 (0.002) Batch 2.750 (2.882) Remain 102:09:26 loss: 0.4907 Lr: 0.00200
[2025-09-06 13:44:49,077 INFO misc.py line 117 1395871] Train: [13/100][1046/1462] Data 0.001 (0.002) Batch 2.707 (2.882) Remain 102:09:02 loss: 0.7545 Lr: 0.00200
[2025-09-06 13:44:51,684 INFO misc.py line 117 1395871] Train: [13/100][1047/1462] Data 0.001 (0.002) Batch 2.607 (2.882) Remain 102:08:25 loss: 0.6252 Lr: 0.00200
[2025-09-06 13:44:54,659 INFO misc.py line 117 1395871] Train: [13/100][1048/1462] Data 0.001 (0.002) Batch 2.975 (2.882) Remain 102:08:34 loss: 0.3659 Lr: 0.00200
[2025-09-06 13:44:57,449 INFO misc.py line 117 1395871] Train: [13/100][1049/1462] Data 0.002 (0.002) Batch 2.791 (2.882) Remain 102:08:20 loss: 0.3813 Lr: 0.00200
[2025-09-06 13:45:00,427 INFO misc.py line 117 1395871] Train: [13/100][1050/1462] Data 0.002 (0.002) Batch 2.977 (2.882) Remain 102:08:29 loss: 0.3515 Lr: 0.00200
[2025-09-06 13:45:03,444 INFO misc.py line 117 1395871] Train: [13/100][1051/1462] Data 0.001 (0.002) Batch 3.017 (2.882) Remain 102:08:42 loss: 0.3321 Lr: 0.00200
[2025-09-06 13:45:06,579 INFO misc.py line 117 1395871] Train: [13/100][1052/1462] Data 0.001 (0.002) Batch 3.135 (2.882) Remain 102:09:10 loss: 0.3463 Lr: 0.00200
[2025-09-06 13:45:09,254 INFO misc.py line 117 1395871] Train: [13/100][1053/1462] Data 0.002 (0.002) Batch 2.675 (2.882) Remain 102:08:42 loss: 0.3244 Lr: 0.00200
[2025-09-06 13:45:12,026 INFO misc.py line 117 1395871] Train: [13/100][1054/1462] Data 0.002 (0.002) Batch 2.773 (2.882) Remain 102:08:26 loss: 0.5088 Lr: 0.00200
[2025-09-06 13:45:15,149 INFO misc.py line 117 1395871] Train: [13/100][1055/1462] Data 0.001 (0.002) Batch 3.122 (2.882) Remain 102:08:52 loss: 0.4985 Lr: 0.00200
[2025-09-06 13:45:17,829 INFO misc.py line 117 1395871] Train: [13/100][1056/1462] Data 0.001 (0.002) Batch 2.680 (2.882) Remain 102:08:25 loss: 0.6397 Lr: 0.00200
[2025-09-06 13:45:20,457 INFO misc.py line 117 1395871] Train: [13/100][1057/1462] Data 0.001 (0.002) Batch 2.629 (2.881) Remain 102:07:51 loss: 0.4527 Lr: 0.00200
[2025-09-06 13:45:23,316 INFO misc.py line 117 1395871] Train: [13/100][1058/1462] Data 0.002 (0.002) Batch 2.858 (2.881) Remain 102:07:46 loss: 0.7807 Lr: 0.00200
[2025-09-06 13:45:26,375 INFO misc.py line 117 1395871] Train: [13/100][1059/1462] Data 0.001 (0.002) Batch 3.059 (2.882) Remain 102:08:04 loss: 0.4287 Lr: 0.00200
[2025-09-06 13:45:29,212 INFO misc.py line 117 1395871] Train: [13/100][1060/1462] Data 0.001 (0.002) Batch 2.837 (2.882) Remain 102:07:56 loss: 0.2952 Lr: 0.00200
[2025-09-06 13:45:32,061 INFO misc.py line 117 1395871] Train: [13/100][1061/1462] Data 0.001 (0.002) Batch 2.849 (2.882) Remain 102:07:49 loss: 0.5344 Lr: 0.00200
[2025-09-06 13:45:35,157 INFO misc.py line 117 1395871] Train: [13/100][1062/1462] Data 0.001 (0.002) Batch 3.097 (2.882) Remain 102:08:12 loss: 0.4327 Lr: 0.00200
[2025-09-06 13:45:38,230 INFO misc.py line 117 1395871] Train: [13/100][1063/1462] Data 0.001 (0.002) Batch 3.072 (2.882) Remain 102:08:32 loss: 0.3689 Lr: 0.00200
[2025-09-06 13:45:40,748 INFO misc.py line 117 1395871] Train: [13/100][1064/1462] Data 0.001 (0.002) Batch 2.519 (2.882) Remain 102:07:46 loss: 0.6736 Lr: 0.00200
[2025-09-06 13:45:43,590 INFO misc.py line 117 1395871] Train: [13/100][1065/1462] Data 0.001 (0.002) Batch 2.842 (2.882) Remain 102:07:38 loss: 0.4143 Lr: 0.00200
[2025-09-06 13:45:46,421 INFO misc.py line 117 1395871] Train: [13/100][1066/1462] Data 0.001 (0.002) Batch 2.830 (2.881) Remain 102:07:29 loss: 0.4380 Lr: 0.00200
[2025-09-06 13:45:49,290 INFO misc.py line 117 1395871] Train: [13/100][1067/1462] Data 0.001 (0.002) Batch 2.870 (2.881) Remain 102:07:25 loss: 0.4993 Lr: 0.00200
[2025-09-06 13:45:52,156 INFO misc.py line 117 1395871] Train: [13/100][1068/1462] Data 0.002 (0.002) Batch 2.866 (2.881) Remain 102:07:20 loss: 0.2895 Lr: 0.00200
[2025-09-06 13:45:55,036 INFO misc.py line 117 1395871] Train: [13/100][1069/1462] Data 0.001 (0.002) Batch 2.880 (2.881) Remain 102:07:17 loss: 0.3417 Lr: 0.00200
[2025-09-06 13:45:58,477 INFO misc.py line 117 1395871] Train: [13/100][1070/1462] Data 0.001 (0.002) Batch 3.441 (2.882) Remain 102:08:21 loss: 0.4737 Lr: 0.00200
[2025-09-06 13:46:01,517 INFO misc.py line 117 1395871] Train: [13/100][1071/1462] Data 0.001 (0.002) Batch 3.040 (2.882) Remain 102:08:37 loss: 0.3912 Lr: 0.00200
[2025-09-06 13:46:04,657 INFO misc.py line 117 1395871] Train: [13/100][1072/1462] Data 0.002 (0.002) Batch 3.139 (2.882) Remain 102:09:05 loss: 0.2673 Lr: 0.00200
[2025-09-06 13:46:07,476 INFO misc.py line 117 1395871] Train: [13/100][1073/1462] Data 0.001 (0.002) Batch 2.819 (2.882) Remain 102:08:54 loss: 0.3352 Lr: 0.00200
[2025-09-06 13:46:10,246 INFO misc.py line 117 1395871] Train: [13/100][1074/1462] Data 0.002 (0.002) Batch 2.770 (2.882) Remain 102:08:38 loss: 0.5208 Lr: 0.00200
[2025-09-06 13:46:13,050 INFO misc.py line 117 1395871] Train: [13/100][1075/1462] Data 0.001 (0.002) Batch 2.804 (2.882) Remain 102:08:26 loss: 0.2497 Lr: 0.00200
[2025-09-06 13:46:16,097 INFO misc.py line 117 1395871] Train: [13/100][1076/1462] Data 0.002 (0.002) Batch 3.047 (2.882) Remain 102:08:43 loss: 0.4865 Lr: 0.00200
[2025-09-06 13:46:19,222 INFO misc.py line 117 1395871] Train: [13/100][1077/1462] Data 0.001 (0.002) Batch 3.125 (2.883) Remain 102:09:09 loss: 0.2490 Lr: 0.00200
[2025-09-06 13:46:21,942 INFO misc.py line 117 1395871] Train: [13/100][1078/1462] Data 0.002 (0.002) Batch 2.720 (2.882) Remain 102:08:46 loss: 0.3947 Lr: 0.00200
[2025-09-06 13:46:24,672 INFO misc.py line 117 1395871] Train: [13/100][1079/1462] Data 0.001 (0.002) Batch 2.730 (2.882) Remain 102:08:26 loss: 0.4421 Lr: 0.00200
[2025-09-06 13:46:27,523 INFO misc.py line 117 1395871] Train: [13/100][1080/1462] Data 0.002 (0.002) Batch 2.851 (2.882) Remain 102:08:19 loss: 0.5107 Lr: 0.00200
[2025-09-06 13:46:30,360 INFO misc.py line 117 1395871] Train: [13/100][1081/1462] Data 0.001 (0.002) Batch 2.838 (2.882) Remain 102:08:11 loss: 0.4347 Lr: 0.00200
[2025-09-06 13:46:33,297 INFO misc.py line 117 1395871] Train: [13/100][1082/1462] Data 0.001 (0.002) Batch 2.936 (2.882) Remain 102:08:14 loss: 0.3351 Lr: 0.00200
[2025-09-06 13:46:35,955 INFO misc.py line 117 1395871] Train: [13/100][1083/1462] Data 0.001 (0.002) Batch 2.658 (2.882) Remain 102:07:45 loss: 0.3117 Lr: 0.00200
[2025-09-06 13:46:39,160 INFO misc.py line 117 1395871] Train: [13/100][1084/1462] Data 0.001 (0.002) Batch 3.206 (2.882) Remain 102:08:20 loss: 0.4339 Lr: 0.00200
[2025-09-06 13:46:41,841 INFO misc.py line 117 1395871] Train: [13/100][1085/1462] Data 0.001 (0.002) Batch 2.681 (2.882) Remain 102:07:54 loss: 0.5946 Lr: 0.00200
[2025-09-06 13:46:44,561 INFO misc.py line 117 1395871] Train: [13/100][1086/1462] Data 0.002 (0.002) Batch 2.720 (2.882) Remain 102:07:32 loss: 0.3539 Lr: 0.00200
[2025-09-06 13:46:47,478 INFO misc.py line 117 1395871] Train: [13/100][1087/1462] Data 0.001 (0.002) Batch 2.916 (2.882) Remain 102:07:33 loss: 0.8108 Lr: 0.00200
[2025-09-06 13:46:50,271 INFO misc.py line 117 1395871] Train: [13/100][1088/1462] Data 0.001 (0.002) Batch 2.793 (2.882) Remain 102:07:19 loss: 0.3624 Lr: 0.00200
[2025-09-06 13:46:52,772 INFO misc.py line 117 1395871] Train: [13/100][1089/1462] Data 0.002 (0.002) Batch 2.501 (2.882) Remain 102:06:32 loss: 0.5291 Lr: 0.00200
[2025-09-06 13:46:55,562 INFO misc.py line 117 1395871] Train: [13/100][1090/1462] Data 0.002 (0.002) Batch 2.790 (2.881) Remain 102:06:18 loss: 0.5774 Lr: 0.00200
[2025-09-06 13:46:58,454 INFO misc.py line 117 1395871] Train: [13/100][1091/1462] Data 0.002 (0.002) Batch 2.892 (2.881) Remain 102:06:17 loss: 0.4199 Lr: 0.00200
[2025-09-06 13:47:01,229 INFO misc.py line 117 1395871] Train: [13/100][1092/1462] Data 0.001 (0.002) Batch 2.776 (2.881) Remain 102:06:01 loss: 0.5296 Lr: 0.00200
[2025-09-06 13:47:03,886 INFO misc.py line 117 1395871] Train: [13/100][1093/1462] Data 0.001 (0.002) Batch 2.657 (2.881) Remain 102:05:32 loss: 0.4068 Lr: 0.00200
[2025-09-06 13:47:06,637 INFO misc.py line 117 1395871] Train: [13/100][1094/1462] Data 0.001 (0.002) Batch 2.752 (2.881) Remain 102:05:14 loss: 0.4370 Lr: 0.00200
[2025-09-06 13:47:09,243 INFO misc.py line 117 1395871] Train: [13/100][1095/1462] Data 0.002 (0.002) Batch 2.605 (2.881) Remain 102:04:39 loss: 0.5703 Lr: 0.00200
[2025-09-06 13:47:11,957 INFO misc.py line 117 1395871] Train: [13/100][1096/1462] Data 0.002 (0.002) Batch 2.714 (2.881) Remain 102:04:17 loss: 0.5052 Lr: 0.00200
[2025-09-06 13:47:14,771 INFO misc.py line 117 1395871] Train: [13/100][1097/1462] Data 0.001 (0.002) Batch 2.814 (2.881) Remain 102:04:06 loss: 0.6215 Lr: 0.00200
[2025-09-06 13:47:17,625 INFO misc.py line 117 1395871] Train: [13/100][1098/1462] Data 0.001 (0.002) Batch 2.854 (2.881) Remain 102:04:00 loss: 0.3668 Lr: 0.00200
[2025-09-06 13:47:20,471 INFO misc.py line 117 1395871] Train: [13/100][1099/1462] Data 0.001 (0.002) Batch 2.846 (2.881) Remain 102:03:53 loss: 0.4137 Lr: 0.00200
[2025-09-06 13:47:23,382 INFO misc.py line 117 1395871] Train: [13/100][1100/1462] Data 0.001 (0.002) Batch 2.911 (2.881) Remain 102:03:54 loss: 0.2721 Lr: 0.00200
[2025-09-06 13:47:26,059 INFO misc.py line 117 1395871] Train: [13/100][1101/1462] Data 0.001 (0.002) Batch 2.678 (2.880) Remain 102:03:27 loss: 0.3454 Lr: 0.00200
[2025-09-06 13:47:28,807 INFO misc.py line 117 1395871] Train: [13/100][1102/1462] Data 0.001 (0.002) Batch 2.747 (2.880) Remain 102:03:09 loss: 0.8338 Lr: 0.00200
[2025-09-06 13:47:31,404 INFO misc.py line 117 1395871] Train: [13/100][1103/1462] Data 0.001 (0.002) Batch 2.598 (2.880) Remain 102:02:33 loss: 0.4062 Lr: 0.00200
[2025-09-06 13:47:33,649 INFO misc.py line 117 1395871] Train: [13/100][1104/1462] Data 0.001 (0.002) Batch 2.245 (2.879) Remain 102:01:17 loss: 0.7504 Lr: 0.00200
[2025-09-06 13:47:36,590 INFO misc.py line 117 1395871] Train: [13/100][1105/1462] Data 0.001 (0.002) Batch 2.941 (2.879) Remain 102:01:21 loss: 0.5643 Lr: 0.00200
[2025-09-06 13:47:39,395 INFO misc.py line 117 1395871] Train: [13/100][1106/1462] Data 0.001 (0.002) Batch 2.804 (2.879) Remain 102:01:09 loss: 0.5077 Lr: 0.00200
[2025-09-06 13:47:42,161 INFO misc.py line 117 1395871] Train: [13/100][1107/1462] Data 0.002 (0.002) Batch 2.767 (2.879) Remain 102:00:54 loss: 0.5083 Lr: 0.00200
[2025-09-06 13:47:44,927 INFO misc.py line 117 1395871] Train: [13/100][1108/1462] Data 0.001 (0.002) Batch 2.766 (2.879) Remain 102:00:38 loss: 0.5123 Lr: 0.00200
[2025-09-06 13:47:47,500 INFO misc.py line 117 1395871] Train: [13/100][1109/1462] Data 0.001 (0.002) Batch 2.572 (2.879) Remain 101:59:59 loss: 0.3838 Lr: 0.00200
[2025-09-06 13:47:50,353 INFO misc.py line 117 1395871] Train: [13/100][1110/1462] Data 0.002 (0.002) Batch 2.853 (2.879) Remain 101:59:54 loss: 0.3674 Lr: 0.00200
[2025-09-06 13:47:53,148 INFO misc.py line 117 1395871] Train: [13/100][1111/1462] Data 0.002 (0.002) Batch 2.795 (2.879) Remain 101:59:41 loss: 0.3873 Lr: 0.00200
[2025-09-06 13:47:55,917 INFO misc.py line 117 1395871] Train: [13/100][1112/1462] Data 0.002 (0.002) Batch 2.769 (2.879) Remain 101:59:26 loss: 0.5357 Lr: 0.00200
[2025-09-06 13:47:59,100 INFO misc.py line 117 1395871] Train: [13/100][1113/1462] Data 0.001 (0.002) Batch 3.183 (2.879) Remain 101:59:58 loss: 0.4362 Lr: 0.00200
[2025-09-06 13:48:02,356 INFO misc.py line 117 1395871] Train: [13/100][1114/1462] Data 0.002 (0.002) Batch 3.256 (2.879) Remain 102:00:38 loss: 0.4041 Lr: 0.00200
[2025-09-06 13:48:05,252 INFO misc.py line 117 1395871] Train: [13/100][1115/1462] Data 0.001 (0.002) Batch 2.896 (2.879) Remain 102:00:37 loss: 0.3111 Lr: 0.00200
[2025-09-06 13:48:08,054 INFO misc.py line 117 1395871] Train: [13/100][1116/1462] Data 0.002 (0.002) Batch 2.802 (2.879) Remain 102:00:25 loss: 0.3560 Lr: 0.00200
[2025-09-06 13:48:10,989 INFO misc.py line 117 1395871] Train: [13/100][1117/1462] Data 0.002 (0.002) Batch 2.935 (2.879) Remain 102:00:29 loss: 0.3524 Lr: 0.00200
[2025-09-06 13:48:13,879 INFO misc.py line 117 1395871] Train: [13/100][1118/1462] Data 0.002 (0.002) Batch 2.890 (2.879) Remain 102:00:27 loss: 0.4684 Lr: 0.00200
[2025-09-06 13:48:16,723 INFO misc.py line 117 1395871] Train: [13/100][1119/1462] Data 0.001 (0.002) Batch 2.844 (2.879) Remain 102:00:20 loss: 0.3449 Lr: 0.00200
[2025-09-06 13:48:19,633 INFO misc.py line 117 1395871] Train: [13/100][1120/1462] Data 0.001 (0.002) Batch 2.910 (2.879) Remain 102:00:21 loss: 0.5437 Lr: 0.00200
[2025-09-06 13:48:22,337 INFO misc.py line 117 1395871] Train: [13/100][1121/1462] Data 0.002 (0.002) Batch 2.704 (2.879) Remain 101:59:58 loss: 0.4488 Lr: 0.00200
[2025-09-06 13:48:25,315 INFO misc.py line 117 1395871] Train: [13/100][1122/1462] Data 0.001 (0.002) Batch 2.977 (2.879) Remain 102:00:06 loss: 0.2571 Lr: 0.00200
[2025-09-06 13:48:27,983 INFO misc.py line 117 1395871] Train: [13/100][1123/1462] Data 0.001 (0.002) Batch 2.668 (2.879) Remain 101:59:39 loss: 0.2689 Lr: 0.00200
[2025-09-06 13:48:30,773 INFO misc.py line 117 1395871] Train: [13/100][1124/1462] Data 0.001 (0.002) Batch 2.790 (2.879) Remain 101:59:26 loss: 0.4506 Lr: 0.00200
[2025-09-06 13:48:33,382 INFO misc.py line 117 1395871] Train: [13/100][1125/1462] Data 0.001 (0.002) Batch 2.609 (2.879) Remain 101:58:53 loss: 0.3864 Lr: 0.00200
[2025-09-06 13:48:35,962 INFO misc.py line 117 1395871] Train: [13/100][1126/1462] Data 0.001 (0.002) Batch 2.579 (2.879) Remain 101:58:16 loss: 0.2965 Lr: 0.00200
[2025-09-06 13:48:38,826 INFO misc.py line 117 1395871] Train: [13/100][1127/1462] Data 0.002 (0.002) Batch 2.865 (2.878) Remain 101:58:11 loss: 0.4507 Lr: 0.00200
[2025-09-06 13:48:41,622 INFO misc.py line 117 1395871] Train: [13/100][1128/1462] Data 0.001 (0.002) Batch 2.796 (2.878) Remain 101:57:59 loss: 0.4935 Lr: 0.00200
[2025-09-06 13:48:44,408 INFO misc.py line 117 1395871] Train: [13/100][1129/1462] Data 0.001 (0.002) Batch 2.786 (2.878) Remain 101:57:46 loss: 0.7794 Lr: 0.00200
[2025-09-06 13:48:47,389 INFO misc.py line 117 1395871] Train: [13/100][1130/1462] Data 0.002 (0.002) Batch 2.981 (2.878) Remain 101:57:55 loss: 0.2679 Lr: 0.00200
[2025-09-06 13:48:50,322 INFO misc.py line 117 1395871] Train: [13/100][1131/1462] Data 0.001 (0.002) Batch 2.933 (2.878) Remain 101:57:58 loss: 0.5877 Lr: 0.00200
[2025-09-06 13:48:53,135 INFO misc.py line 117 1395871] Train: [13/100][1132/1462] Data 0.001 (0.002) Batch 2.813 (2.878) Remain 101:57:48 loss: 0.5615 Lr: 0.00200
[2025-09-06 13:48:55,834 INFO misc.py line 117 1395871] Train: [13/100][1133/1462] Data 0.002 (0.002) Batch 2.699 (2.878) Remain 101:57:24 loss: 0.6882 Lr: 0.00200
[2025-09-06 13:48:58,455 INFO misc.py line 117 1395871] Train: [13/100][1134/1462] Data 0.002 (0.002) Batch 2.621 (2.878) Remain 101:56:53 loss: 0.3894 Lr: 0.00200
[2025-09-06 13:49:01,684 INFO misc.py line 117 1395871] Train: [13/100][1135/1462] Data 0.002 (0.002) Batch 3.229 (2.878) Remain 101:57:29 loss: 0.3492 Lr: 0.00200
[2025-09-06 13:49:04,879 INFO misc.py line 117 1395871] Train: [13/100][1136/1462] Data 0.001 (0.002) Batch 3.195 (2.879) Remain 101:58:02 loss: 0.8267 Lr: 0.00200
[2025-09-06 13:49:07,799 INFO misc.py line 117 1395871] Train: [13/100][1137/1462] Data 0.001 (0.002) Batch 2.921 (2.879) Remain 101:58:04 loss: 0.5128 Lr: 0.00200
[2025-09-06 13:49:10,690 INFO misc.py line 117 1395871] Train: [13/100][1138/1462] Data 0.002 (0.002) Batch 2.890 (2.879) Remain 101:58:02 loss: 0.3091 Lr: 0.00200
[2025-09-06 13:49:13,631 INFO misc.py line 117 1395871] Train: [13/100][1139/1462] Data 0.002 (0.002) Batch 2.941 (2.879) Remain 101:58:06 loss: 0.2742 Lr: 0.00200
[2025-09-06 13:49:16,582 INFO misc.py line 117 1395871] Train: [13/100][1140/1462] Data 0.002 (0.002) Batch 2.952 (2.879) Remain 101:58:12 loss: 0.3099 Lr: 0.00200
[2025-09-06 13:49:19,672 INFO misc.py line 117 1395871] Train: [13/100][1141/1462] Data 0.001 (0.002) Batch 3.089 (2.879) Remain 101:58:32 loss: 0.3511 Lr: 0.00200
[2025-09-06 13:49:22,557 INFO misc.py line 117 1395871] Train: [13/100][1142/1462] Data 0.002 (0.002) Batch 2.885 (2.879) Remain 101:58:30 loss: 0.3557 Lr: 0.00200
[2025-09-06 13:49:25,335 INFO misc.py line 117 1395871] Train: [13/100][1143/1462] Data 0.002 (0.002) Batch 2.778 (2.879) Remain 101:58:16 loss: 0.5086 Lr: 0.00200
[2025-09-06 13:49:28,008 INFO misc.py line 117 1395871] Train: [13/100][1144/1462] Data 0.001 (0.002) Batch 2.673 (2.879) Remain 101:57:50 loss: 0.4671 Lr: 0.00200
[2025-09-06 13:49:30,970 INFO misc.py line 117 1395871] Train: [13/100][1145/1462] Data 0.001 (0.002) Batch 2.962 (2.879) Remain 101:57:57 loss: 0.3230 Lr: 0.00200
[2025-09-06 13:49:33,710 INFO misc.py line 117 1395871] Train: [13/100][1146/1462] Data 0.001 (0.002) Batch 2.739 (2.879) Remain 101:57:38 loss: 0.3506 Lr: 0.00200
[2025-09-06 13:49:36,548 INFO misc.py line 117 1395871] Train: [13/100][1147/1462] Data 0.003 (0.002) Batch 2.839 (2.879) Remain 101:57:31 loss: 0.3383 Lr: 0.00200
[2025-09-06 13:49:39,524 INFO misc.py line 117 1395871] Train: [13/100][1148/1462] Data 0.002 (0.002) Batch 2.976 (2.879) Remain 101:57:39 loss: 0.6129 Lr: 0.00200
[2025-09-06 13:49:42,541 INFO misc.py line 117 1395871] Train: [13/100][1149/1462] Data 0.002 (0.002) Batch 3.017 (2.879) Remain 101:57:51 loss: 0.5312 Lr: 0.00200
[2025-09-06 13:49:45,525 INFO misc.py line 117 1395871] Train: [13/100][1150/1462] Data 0.002 (0.002) Batch 2.984 (2.879) Remain 101:58:00 loss: 0.2653 Lr: 0.00200
[2025-09-06 13:49:48,527 INFO misc.py line 117 1395871] Train: [13/100][1151/1462] Data 0.002 (0.002) Batch 3.002 (2.879) Remain 101:58:11 loss: 0.4071 Lr: 0.00200
[2025-09-06 13:49:51,755 INFO misc.py line 117 1395871] Train: [13/100][1152/1462] Data 0.002 (0.002) Batch 3.228 (2.879) Remain 101:58:47 loss: 0.4296 Lr: 0.00200
[2025-09-06 13:49:54,831 INFO misc.py line 117 1395871] Train: [13/100][1153/1462] Data 0.001 (0.002) Batch 3.076 (2.880) Remain 101:59:06 loss: 0.4443 Lr: 0.00200
[2025-09-06 13:49:57,538 INFO misc.py line 117 1395871] Train: [13/100][1154/1462] Data 0.001 (0.002) Batch 2.707 (2.879) Remain 101:58:44 loss: 0.4839 Lr: 0.00200
[2025-09-06 13:50:00,501 INFO misc.py line 117 1395871] Train: [13/100][1155/1462] Data 0.002 (0.002) Batch 2.963 (2.879) Remain 101:58:50 loss: 0.3560 Lr: 0.00200
[2025-09-06 13:50:03,077 INFO misc.py line 117 1395871] Train: [13/100][1156/1462] Data 0.001 (0.002) Batch 2.576 (2.879) Remain 101:58:14 loss: 0.3537 Lr: 0.00200
[2025-09-06 13:50:05,585 INFO misc.py line 117 1395871] Train: [13/100][1157/1462] Data 0.002 (0.002) Batch 2.508 (2.879) Remain 101:57:30 loss: 0.6553 Lr: 0.00200
[2025-09-06 13:50:08,454 INFO misc.py line 117 1395871] Train: [13/100][1158/1462] Data 0.001 (0.002) Batch 2.868 (2.879) Remain 101:57:26 loss: 0.2872 Lr: 0.00200
[2025-09-06 13:50:11,312 INFO misc.py line 117 1395871] Train: [13/100][1159/1462] Data 0.002 (0.002) Batch 2.858 (2.879) Remain 101:57:21 loss: 0.4709 Lr: 0.00200
[2025-09-06 13:50:13,875 INFO misc.py line 117 1395871] Train: [13/100][1160/1462] Data 0.002 (0.002) Batch 2.564 (2.879) Remain 101:56:43 loss: 0.3862 Lr: 0.00200
[2025-09-06 13:50:17,013 INFO misc.py line 117 1395871] Train: [13/100][1161/1462] Data 0.001 (0.002) Batch 3.138 (2.879) Remain 101:57:09 loss: 0.3097 Lr: 0.00200
[2025-09-06 13:50:20,077 INFO misc.py line 117 1395871] Train: [13/100][1162/1462] Data 0.002 (0.002) Batch 3.064 (2.879) Remain 101:57:26 loss: 0.3979 Lr: 0.00200
[2025-09-06 13:50:23,018 INFO misc.py line 117 1395871] Train: [13/100][1163/1462] Data 0.002 (0.002) Batch 2.940 (2.879) Remain 101:57:30 loss: 0.7280 Lr: 0.00200
[2025-09-06 13:50:26,008 INFO misc.py line 117 1395871] Train: [13/100][1164/1462] Data 0.001 (0.002) Batch 2.990 (2.879) Remain 101:57:39 loss: 0.5019 Lr: 0.00200
[2025-09-06 13:50:28,777 INFO misc.py line 117 1395871] Train: [13/100][1165/1462] Data 0.002 (0.002) Batch 2.769 (2.879) Remain 101:57:24 loss: 0.5995 Lr: 0.00200
[2025-09-06 13:50:31,650 INFO misc.py line 117 1395871] Train: [13/100][1166/1462] Data 0.001 (0.002) Batch 2.873 (2.879) Remain 101:57:21 loss: 0.4485 Lr: 0.00200
[2025-09-06 13:50:34,723 INFO misc.py line 117 1395871] Train: [13/100][1167/1462] Data 0.001 (0.002) Batch 3.073 (2.879) Remain 101:57:39 loss: 0.5178 Lr: 0.00200
[2025-09-06 13:50:37,541 INFO misc.py line 117 1395871] Train: [13/100][1168/1462] Data 0.001 (0.002) Batch 2.818 (2.879) Remain 101:57:30 loss: 0.5315 Lr: 0.00200
[2025-09-06 13:50:40,721 INFO misc.py line 117 1395871] Train: [13/100][1169/1462] Data 0.001 (0.002) Batch 3.180 (2.879) Remain 101:58:00 loss: 0.5176 Lr: 0.00200
[2025-09-06 13:50:43,540 INFO misc.py line 117 1395871] Train: [13/100][1170/1462] Data 0.002 (0.002) Batch 2.820 (2.879) Remain 101:57:50 loss: 0.5355 Lr: 0.00200
[2025-09-06 13:50:46,438 INFO misc.py line 117 1395871] Train: [13/100][1171/1462] Data 0.001 (0.002) Batch 2.898 (2.879) Remain 101:57:49 loss: 0.3883 Lr: 0.00200
[2025-09-06 13:50:49,501 INFO misc.py line 117 1395871] Train: [13/100][1172/1462] Data 0.001 (0.002) Batch 3.063 (2.879) Remain 101:58:07 loss: 0.5750 Lr: 0.00200
[2025-09-06 13:50:52,339 INFO misc.py line 117 1395871] Train: [13/100][1173/1462] Data 0.001 (0.002) Batch 2.838 (2.879) Remain 101:57:59 loss: 0.5873 Lr: 0.00200
[2025-09-06 13:50:55,257 INFO misc.py line 117 1395871] Train: [13/100][1174/1462] Data 0.001 (0.002) Batch 2.918 (2.879) Remain 101:58:00 loss: 0.5224 Lr: 0.00200
[2025-09-06 13:50:57,885 INFO misc.py line 117 1395871] Train: [13/100][1175/1462] Data 0.002 (0.002) Batch 2.628 (2.879) Remain 101:57:30 loss: 0.4133 Lr: 0.00200
[2025-09-06 13:51:00,627 INFO misc.py line 117 1395871] Train: [13/100][1176/1462] Data 0.001 (0.002) Batch 2.741 (2.879) Remain 101:57:12 loss: 0.2770 Lr: 0.00200
[2025-09-06 13:51:03,593 INFO misc.py line 117 1395871] Train: [13/100][1177/1462] Data 0.001 (0.002) Batch 2.966 (2.879) Remain 101:57:19 loss: 0.2092 Lr: 0.00200
[2025-09-06 13:51:06,576 INFO misc.py line 117 1395871] Train: [13/100][1178/1462] Data 0.001 (0.002) Batch 2.983 (2.879) Remain 101:57:27 loss: 0.8369 Lr: 0.00200
[2025-09-06 13:51:09,450 INFO misc.py line 117 1395871] Train: [13/100][1179/1462] Data 0.002 (0.002) Batch 2.874 (2.879) Remain 101:57:24 loss: 0.4509 Lr: 0.00200
[2025-09-06 13:51:12,077 INFO misc.py line 117 1395871] Train: [13/100][1180/1462] Data 0.002 (0.002) Batch 2.626 (2.879) Remain 101:56:54 loss: 0.4905 Lr: 0.00200
[2025-09-06 13:51:14,820 INFO misc.py line 117 1395871] Train: [13/100][1181/1462] Data 0.001 (0.002) Batch 2.744 (2.879) Remain 101:56:36 loss: 0.5964 Lr: 0.00200
[2025-09-06 13:51:17,642 INFO misc.py line 117 1395871] Train: [13/100][1182/1462] Data 0.002 (0.002) Batch 2.822 (2.879) Remain 101:56:27 loss: 0.3250 Lr: 0.00200
[2025-09-06 13:51:20,270 INFO misc.py line 117 1395871] Train: [13/100][1183/1462] Data 0.001 (0.002) Batch 2.627 (2.879) Remain 101:55:57 loss: 0.5378 Lr: 0.00200
[2025-09-06 13:51:22,886 INFO misc.py line 117 1395871] Train: [13/100][1184/1462] Data 0.002 (0.002) Batch 2.616 (2.878) Remain 101:55:26 loss: 0.6296 Lr: 0.00200
[2025-09-06 13:51:25,701 INFO misc.py line 117 1395871] Train: [13/100][1185/1462] Data 0.001 (0.002) Batch 2.815 (2.878) Remain 101:55:16 loss: 0.3562 Lr: 0.00200
[2025-09-06 13:51:28,756 INFO misc.py line 117 1395871] Train: [13/100][1186/1462] Data 0.002 (0.002) Batch 3.055 (2.879) Remain 101:55:32 loss: 0.4022 Lr: 0.00200
[2025-09-06 13:51:31,463 INFO misc.py line 117 1395871] Train: [13/100][1187/1462] Data 0.002 (0.002) Batch 2.707 (2.878) Remain 101:55:11 loss: 0.4915 Lr: 0.00200
[2025-09-06 13:51:34,282 INFO misc.py line 117 1395871] Train: [13/100][1188/1462] Data 0.001 (0.002) Batch 2.819 (2.878) Remain 101:55:02 loss: 0.6651 Lr: 0.00200
[2025-09-06 13:51:37,153 INFO misc.py line 117 1395871] Train: [13/100][1189/1462] Data 0.002 (0.002) Batch 2.871 (2.878) Remain 101:54:58 loss: 0.3799 Lr: 0.00200
[2025-09-06 13:51:39,800 INFO misc.py line 117 1395871] Train: [13/100][1190/1462] Data 0.002 (0.002) Batch 2.647 (2.878) Remain 101:54:30 loss: 0.4484 Lr: 0.00200
[2025-09-06 13:51:42,759 INFO misc.py line 117 1395871] Train: [13/100][1191/1462] Data 0.002 (0.002) Batch 2.959 (2.878) Remain 101:54:36 loss: 0.5381 Lr: 0.00200
[2025-09-06 13:51:46,112 INFO misc.py line 117 1395871] Train: [13/100][1192/1462] Data 0.002 (0.002) Batch 3.353 (2.879) Remain 101:55:24 loss: 0.2958 Lr: 0.00200
[2025-09-06 13:51:48,880 INFO misc.py line 117 1395871] Train: [13/100][1193/1462] Data 0.001 (0.002) Batch 2.768 (2.879) Remain 101:55:09 loss: 0.4572 Lr: 0.00200
[2025-09-06 13:51:51,677 INFO misc.py line 117 1395871] Train: [13/100][1194/1462] Data 0.002 (0.002) Batch 2.797 (2.878) Remain 101:54:58 loss: 0.4621 Lr: 0.00200
[2025-09-06 13:51:54,526 INFO misc.py line 117 1395871] Train: [13/100][1195/1462] Data 0.001 (0.002) Batch 2.849 (2.878) Remain 101:54:52 loss: 0.3515 Lr: 0.00200
[2025-09-06 13:51:57,265 INFO misc.py line 117 1395871] Train: [13/100][1196/1462] Data 0.001 (0.002) Batch 2.738 (2.878) Remain 101:54:34 loss: 0.3633 Lr: 0.00200
[2025-09-06 13:51:59,928 INFO misc.py line 117 1395871] Train: [13/100][1197/1462] Data 0.002 (0.002) Batch 2.664 (2.878) Remain 101:54:08 loss: 0.3073 Lr: 0.00200
[2025-09-06 13:52:03,097 INFO misc.py line 117 1395871] Train: [13/100][1198/1462] Data 0.001 (0.002) Batch 3.169 (2.878) Remain 101:54:36 loss: 0.3415 Lr: 0.00200
[2025-09-06 13:52:05,754 INFO misc.py line 117 1395871] Train: [13/100][1199/1462] Data 0.002 (0.002) Batch 2.657 (2.878) Remain 101:54:10 loss: 0.4064 Lr: 0.00200
[2025-09-06 13:52:08,699 INFO misc.py line 117 1395871] Train: [13/100][1200/1462] Data 0.001 (0.002) Batch 2.945 (2.878) Remain 101:54:14 loss: 0.4379 Lr: 0.00200
[2025-09-06 13:52:11,423 INFO misc.py line 117 1395871] Train: [13/100][1201/1462] Data 0.002 (0.002) Batch 2.724 (2.878) Remain 101:53:55 loss: 0.3174 Lr: 0.00200
[2025-09-06 13:52:14,260 INFO misc.py line 117 1395871] Train: [13/100][1202/1462] Data 0.002 (0.002) Batch 2.837 (2.878) Remain 101:53:47 loss: 0.5853 Lr: 0.00200
[2025-09-06 13:52:17,301 INFO misc.py line 117 1395871] Train: [13/100][1203/1462] Data 0.002 (0.002) Batch 3.040 (2.878) Remain 101:54:02 loss: 0.4252 Lr: 0.00200
[2025-09-06 13:52:19,864 INFO misc.py line 117 1395871] Train: [13/100][1204/1462] Data 0.002 (0.002) Batch 2.563 (2.878) Remain 101:53:25 loss: 0.4949 Lr: 0.00200
[2025-09-06 13:52:23,114 INFO misc.py line 117 1395871] Train: [13/100][1205/1462] Data 0.002 (0.002) Batch 3.250 (2.878) Remain 101:54:02 loss: 0.5185 Lr: 0.00200
[2025-09-06 13:52:26,083 INFO misc.py line 117 1395871] Train: [13/100][1206/1462] Data 0.002 (0.002) Batch 2.969 (2.878) Remain 101:54:09 loss: 0.5263 Lr: 0.00200
[2025-09-06 13:52:29,009 INFO misc.py line 117 1395871] Train: [13/100][1207/1462] Data 0.001 (0.002) Batch 2.926 (2.878) Remain 101:54:11 loss: 0.3914 Lr: 0.00200
[2025-09-06 13:52:32,109 INFO misc.py line 117 1395871] Train: [13/100][1208/1462] Data 0.002 (0.002) Batch 3.100 (2.879) Remain 101:54:31 loss: 0.5043 Lr: 0.00200
[2025-09-06 13:52:35,063 INFO misc.py line 117 1395871] Train: [13/100][1209/1462] Data 0.001 (0.002) Batch 2.954 (2.879) Remain 101:54:37 loss: 0.4577 Lr: 0.00200
[2025-09-06 13:52:38,435 INFO misc.py line 117 1395871] Train: [13/100][1210/1462] Data 0.001 (0.002) Batch 3.372 (2.879) Remain 101:55:26 loss: 0.3671 Lr: 0.00200
[2025-09-06 13:52:41,174 INFO misc.py line 117 1395871] Train: [13/100][1211/1462] Data 0.001 (0.002) Batch 2.739 (2.879) Remain 101:55:08 loss: 0.3888 Lr: 0.00200
[2025-09-06 13:52:44,363 INFO misc.py line 117 1395871] Train: [13/100][1212/1462] Data 0.002 (0.002) Batch 3.189 (2.879) Remain 101:55:38 loss: 0.3292 Lr: 0.00200
[2025-09-06 13:52:47,210 INFO misc.py line 117 1395871] Train: [13/100][1213/1462] Data 0.002 (0.002) Batch 2.847 (2.879) Remain 101:55:32 loss: 0.5659 Lr: 0.00200
[2025-09-06 13:52:49,843 INFO misc.py line 117 1395871] Train: [13/100][1214/1462] Data 0.002 (0.002) Batch 2.633 (2.879) Remain 101:55:03 loss: 0.8128 Lr: 0.00200
[2025-09-06 13:52:52,470 INFO misc.py line 117 1395871] Train: [13/100][1215/1462] Data 0.001 (0.002) Batch 2.627 (2.879) Remain 101:54:33 loss: 0.2609 Lr: 0.00200
[2025-09-06 13:52:55,579 INFO misc.py line 117 1395871] Train: [13/100][1216/1462] Data 0.001 (0.002) Batch 3.109 (2.879) Remain 101:54:55 loss: 0.6600 Lr: 0.00200
[2025-09-06 13:52:58,072 INFO misc.py line 117 1395871] Train: [13/100][1217/1462] Data 0.001 (0.002) Batch 2.493 (2.879) Remain 101:54:11 loss: 0.4693 Lr: 0.00200
[2025-09-06 13:53:01,125 INFO misc.py line 117 1395871] Train: [13/100][1218/1462] Data 0.002 (0.002) Batch 3.053 (2.879) Remain 101:54:27 loss: 0.4111 Lr: 0.00200
[2025-09-06 13:53:03,654 INFO misc.py line 117 1395871] Train: [13/100][1219/1462] Data 0.002 (0.002) Batch 2.529 (2.879) Remain 101:53:47 loss: 0.3962 Lr: 0.00200
[2025-09-06 13:53:06,343 INFO misc.py line 117 1395871] Train: [13/100][1220/1462] Data 0.002 (0.002) Batch 2.689 (2.878) Remain 101:53:25 loss: 0.5457 Lr: 0.00200
[2025-09-06 13:53:09,185 INFO misc.py line 117 1395871] Train: [13/100][1221/1462] Data 0.002 (0.002) Batch 2.842 (2.878) Remain 101:53:18 loss: 0.4716 Lr: 0.00200
[2025-09-06 13:53:11,565 INFO misc.py line 117 1395871] Train: [13/100][1222/1462] Data 0.002 (0.002) Batch 2.380 (2.878) Remain 101:52:23 loss: 0.3072 Lr: 0.00200
[2025-09-06 13:53:14,752 INFO misc.py line 117 1395871] Train: [13/100][1223/1462] Data 0.002 (0.002) Batch 3.186 (2.878) Remain 101:52:52 loss: 0.3026 Lr: 0.00200
[2025-09-06 13:53:17,278 INFO misc.py line 117 1395871] Train: [13/100][1224/1462] Data 0.002 (0.002) Batch 2.526 (2.878) Remain 101:52:13 loss: 0.4027 Lr: 0.00200
[2025-09-06 13:53:20,070 INFO misc.py line 117 1395871] Train: [13/100][1225/1462] Data 0.002 (0.002) Batch 2.792 (2.878) Remain 101:52:01 loss: 0.2825 Lr: 0.00200
[2025-09-06 13:53:22,830 INFO misc.py line 117 1395871] Train: [13/100][1226/1462] Data 0.002 (0.002) Batch 2.759 (2.878) Remain 101:51:46 loss: 0.4568 Lr: 0.00200
[2025-09-06 13:53:25,754 INFO misc.py line 117 1395871] Train: [13/100][1227/1462] Data 0.003 (0.002) Batch 2.925 (2.878) Remain 101:51:48 loss: 0.6143 Lr: 0.00200
[2025-09-06 13:53:28,759 INFO misc.py line 117 1395871] Train: [13/100][1228/1462] Data 0.002 (0.002) Batch 3.005 (2.878) Remain 101:51:58 loss: 0.4498 Lr: 0.00200
[2025-09-06 13:53:31,710 INFO misc.py line 117 1395871] Train: [13/100][1229/1462] Data 0.002 (0.002) Batch 2.951 (2.878) Remain 101:52:03 loss: 0.3936 Lr: 0.00200
[2025-09-06 13:53:34,769 INFO misc.py line 117 1395871] Train: [13/100][1230/1462] Data 0.002 (0.002) Batch 3.058 (2.878) Remain 101:52:19 loss: 0.3932 Lr: 0.00200
[2025-09-06 13:53:37,514 INFO misc.py line 117 1395871] Train: [13/100][1231/1462] Data 0.001 (0.002) Batch 2.746 (2.878) Remain 101:52:02 loss: 0.3636 Lr: 0.00200
[2025-09-06 13:53:40,469 INFO misc.py line 117 1395871] Train: [13/100][1232/1462] Data 0.001 (0.002) Batch 2.955 (2.878) Remain 101:52:07 loss: 0.3588 Lr: 0.00200
[2025-09-06 13:53:43,691 INFO misc.py line 117 1395871] Train: [13/100][1233/1462] Data 0.002 (0.002) Batch 3.222 (2.878) Remain 101:52:40 loss: 0.3361 Lr: 0.00200
[2025-09-06 13:53:46,347 INFO misc.py line 117 1395871] Train: [13/100][1234/1462] Data 0.002 (0.002) Batch 2.656 (2.878) Remain 101:52:14 loss: 0.3883 Lr: 0.00200
[2025-09-06 13:53:49,157 INFO misc.py line 117 1395871] Train: [13/100][1235/1462] Data 0.002 (0.002) Batch 2.811 (2.878) Remain 101:52:04 loss: 0.9918 Lr: 0.00200
[2025-09-06 13:53:51,842 INFO misc.py line 117 1395871] Train: [13/100][1236/1462] Data 0.002 (0.002) Batch 2.684 (2.878) Remain 101:51:41 loss: 0.4702 Lr: 0.00200
[2025-09-06 13:53:54,528 INFO misc.py line 117 1395871] Train: [13/100][1237/1462] Data 0.002 (0.002) Batch 2.686 (2.878) Remain 101:51:18 loss: 0.5199 Lr: 0.00200
[2025-09-06 13:53:57,545 INFO misc.py line 117 1395871] Train: [13/100][1238/1462] Data 0.001 (0.002) Batch 3.017 (2.878) Remain 101:51:30 loss: 0.6850 Lr: 0.00200
[2025-09-06 13:54:00,169 INFO misc.py line 117 1395871] Train: [13/100][1239/1462] Data 0.001 (0.002) Batch 2.623 (2.878) Remain 101:51:01 loss: 0.4627 Lr: 0.00200
[2025-09-06 13:54:02,768 INFO misc.py line 117 1395871] Train: [13/100][1240/1462] Data 0.001 (0.002) Batch 2.599 (2.877) Remain 101:50:29 loss: 0.3524 Lr: 0.00200
[2025-09-06 13:54:05,447 INFO misc.py line 117 1395871] Train: [13/100][1241/1462] Data 0.001 (0.002) Batch 2.679 (2.877) Remain 101:50:06 loss: 0.3350 Lr: 0.00200
[2025-09-06 13:54:07,988 INFO misc.py line 117 1395871] Train: [13/100][1242/1462] Data 0.002 (0.002) Batch 2.540 (2.877) Remain 101:49:29 loss: 0.5784 Lr: 0.00200
[2025-09-06 13:54:10,631 INFO misc.py line 117 1395871] Train: [13/100][1243/1462] Data 0.002 (0.002) Batch 2.644 (2.877) Remain 101:49:02 loss: 0.3547 Lr: 0.00200
[2025-09-06 13:54:13,493 INFO misc.py line 117 1395871] Train: [13/100][1244/1462] Data 0.002 (0.002) Batch 2.862 (2.877) Remain 101:48:57 loss: 0.4829 Lr: 0.00200
[2025-09-06 13:54:16,486 INFO misc.py line 117 1395871] Train: [13/100][1245/1462] Data 0.002 (0.002) Batch 2.993 (2.877) Remain 101:49:06 loss: 0.7086 Lr: 0.00200
[2025-09-06 13:54:19,534 INFO misc.py line 117 1395871] Train: [13/100][1246/1462] Data 0.001 (0.002) Batch 3.048 (2.877) Remain 101:49:21 loss: 0.3433 Lr: 0.00200
[2025-09-06 13:54:22,320 INFO misc.py line 117 1395871] Train: [13/100][1247/1462] Data 0.001 (0.002) Batch 2.786 (2.877) Remain 101:49:09 loss: 0.3859 Lr: 0.00200
[2025-09-06 13:54:25,098 INFO misc.py line 117 1395871] Train: [13/100][1248/1462] Data 0.002 (0.002) Batch 2.779 (2.877) Remain 101:48:56 loss: 0.3732 Lr: 0.00200
[2025-09-06 13:54:28,151 INFO misc.py line 117 1395871] Train: [13/100][1249/1462] Data 0.001 (0.002) Batch 3.053 (2.877) Remain 101:49:11 loss: 0.4555 Lr: 0.00200
[2025-09-06 13:54:31,163 INFO misc.py line 117 1395871] Train: [13/100][1250/1462] Data 0.002 (0.002) Batch 3.012 (2.877) Remain 101:49:22 loss: 0.3817 Lr: 0.00200
[2025-09-06 13:54:34,129 INFO misc.py line 117 1395871] Train: [13/100][1251/1462] Data 0.002 (0.002) Batch 2.966 (2.877) Remain 101:49:28 loss: 0.2325 Lr: 0.00200
[2025-09-06 13:54:36,850 INFO misc.py line 117 1395871] Train: [13/100][1252/1462] Data 0.001 (0.002) Batch 2.720 (2.877) Remain 101:49:09 loss: 0.5164 Lr: 0.00200
[2025-09-06 13:54:39,592 INFO misc.py line 117 1395871] Train: [13/100][1253/1462] Data 0.001 (0.002) Batch 2.742 (2.877) Remain 101:48:53 loss: 0.3348 Lr: 0.00200
[2025-09-06 13:54:42,649 INFO misc.py line 117 1395871] Train: [13/100][1254/1462] Data 0.002 (0.002) Batch 3.057 (2.877) Remain 101:49:08 loss: 0.4352 Lr: 0.00200
[2025-09-06 13:54:45,692 INFO misc.py line 117 1395871] Train: [13/100][1255/1462] Data 0.002 (0.002) Batch 3.043 (2.877) Remain 101:49:22 loss: 0.3252 Lr: 0.00200
[2025-09-06 13:54:48,633 INFO misc.py line 117 1395871] Train: [13/100][1256/1462] Data 0.002 (0.002) Batch 2.941 (2.877) Remain 101:49:26 loss: 0.5746 Lr: 0.00200
[2025-09-06 13:54:51,433 INFO misc.py line 117 1395871] Train: [13/100][1257/1462] Data 0.001 (0.002) Batch 2.800 (2.877) Remain 101:49:15 loss: 0.4363 Lr: 0.00200
[2025-09-06 13:54:54,146 INFO misc.py line 117 1395871] Train: [13/100][1258/1462] Data 0.001 (0.002) Batch 2.713 (2.877) Remain 101:48:55 loss: 0.2614 Lr: 0.00200
[2025-09-06 13:54:57,419 INFO misc.py line 117 1395871] Train: [13/100][1259/1462] Data 0.001 (0.002) Batch 3.273 (2.877) Remain 101:49:33 loss: 0.4308 Lr: 0.00200
[2025-09-06 13:55:00,254 INFO misc.py line 117 1395871] Train: [13/100][1260/1462] Data 0.001 (0.002) Batch 2.835 (2.877) Remain 101:49:25 loss: 0.8736 Lr: 0.00200
[2025-09-06 13:55:03,075 INFO misc.py line 117 1395871] Train: [13/100][1261/1462] Data 0.001 (0.002) Batch 2.821 (2.877) Remain 101:49:17 loss: 0.3033 Lr: 0.00200
[2025-09-06 13:55:06,237 INFO misc.py line 117 1395871] Train: [13/100][1262/1462] Data 0.001 (0.002) Batch 3.162 (2.878) Remain 101:49:43 loss: 0.6230 Lr: 0.00200
[2025-09-06 13:55:09,201 INFO misc.py line 117 1395871] Train: [13/100][1263/1462] Data 0.001 (0.002) Batch 2.964 (2.878) Remain 101:49:49 loss: 0.3169 Lr: 0.00200
[2025-09-06 13:55:12,386 INFO misc.py line 117 1395871] Train: [13/100][1264/1462] Data 0.001 (0.002) Batch 3.186 (2.878) Remain 101:50:17 loss: 0.7058 Lr: 0.00200
[2025-09-06 13:55:15,348 INFO misc.py line 117 1395871] Train: [13/100][1265/1462] Data 0.001 (0.002) Batch 2.962 (2.878) Remain 101:50:22 loss: 0.2497 Lr: 0.00200
[2025-09-06 13:55:18,549 INFO misc.py line 117 1395871] Train: [13/100][1266/1462] Data 0.001 (0.002) Batch 3.201 (2.878) Remain 101:50:52 loss: 0.4396 Lr: 0.00200
[2025-09-06 13:55:21,502 INFO misc.py line 117 1395871] Train: [13/100][1267/1462] Data 0.001 (0.002) Batch 2.953 (2.878) Remain 101:50:57 loss: 0.3685 Lr: 0.00200
[2025-09-06 13:55:24,458 INFO misc.py line 117 1395871] Train: [13/100][1268/1462] Data 0.001 (0.002) Batch 2.956 (2.878) Remain 101:51:02 loss: 0.6582 Lr: 0.00200
[2025-09-06 13:55:27,326 INFO misc.py line 117 1395871] Train: [13/100][1269/1462] Data 0.001 (0.002) Batch 2.868 (2.878) Remain 101:50:58 loss: 0.3724 Lr: 0.00200
[2025-09-06 13:55:30,178 INFO misc.py line 117 1395871] Train: [13/100][1270/1462] Data 0.001 (0.002) Batch 2.853 (2.878) Remain 101:50:52 loss: 0.5234 Lr: 0.00200
[2025-09-06 13:55:32,974 INFO misc.py line 117 1395871] Train: [13/100][1271/1462] Data 0.001 (0.002) Batch 2.796 (2.878) Remain 101:50:41 loss: 0.5541 Lr: 0.00200
[2025-09-06 13:55:35,625 INFO misc.py line 117 1395871] Train: [13/100][1272/1462] Data 0.001 (0.002) Batch 2.650 (2.878) Remain 101:50:15 loss: 0.3449 Lr: 0.00200
[2025-09-06 13:55:38,134 INFO misc.py line 117 1395871] Train: [13/100][1273/1462] Data 0.001 (0.002) Batch 2.509 (2.878) Remain 101:49:36 loss: 0.4486 Lr: 0.00200
[2025-09-06 13:55:41,390 INFO misc.py line 117 1395871] Train: [13/100][1274/1462] Data 0.001 (0.002) Batch 3.256 (2.878) Remain 101:50:11 loss: 0.5534 Lr: 0.00200
[2025-09-06 13:55:44,604 INFO misc.py line 117 1395871] Train: [13/100][1275/1462] Data 0.002 (0.002) Batch 3.214 (2.878) Remain 101:50:41 loss: 0.4537 Lr: 0.00200
[2025-09-06 13:55:47,646 INFO misc.py line 117 1395871] Train: [13/100][1276/1462] Data 0.001 (0.002) Batch 3.042 (2.878) Remain 101:50:55 loss: 0.5864 Lr: 0.00200
[2025-09-06 13:55:50,621 INFO misc.py line 117 1395871] Train: [13/100][1277/1462] Data 0.001 (0.002) Batch 2.975 (2.879) Remain 101:51:02 loss: 0.3873 Lr: 0.00200
[2025-09-06 13:55:53,660 INFO misc.py line 117 1395871] Train: [13/100][1278/1462] Data 0.001 (0.002) Batch 3.039 (2.879) Remain 101:51:15 loss: 0.7775 Lr: 0.00200
[2025-09-06 13:55:56,554 INFO misc.py line 117 1395871] Train: [13/100][1279/1462] Data 0.002 (0.002) Batch 2.893 (2.879) Remain 101:51:13 loss: 0.4300 Lr: 0.00200
[2025-09-06 13:55:59,574 INFO misc.py line 117 1395871] Train: [13/100][1280/1462] Data 0.001 (0.002) Batch 3.020 (2.879) Remain 101:51:25 loss: 0.3812 Lr: 0.00200
[2025-09-06 13:56:02,447 INFO misc.py line 117 1395871] Train: [13/100][1281/1462] Data 0.001 (0.002) Batch 2.873 (2.879) Remain 101:51:21 loss: 0.5052 Lr: 0.00200
[2025-09-06 13:56:05,251 INFO misc.py line 117 1395871] Train: [13/100][1282/1462] Data 0.002 (0.002) Batch 2.804 (2.879) Remain 101:51:11 loss: 0.6580 Lr: 0.00200
[2025-09-06 13:56:08,071 INFO misc.py line 117 1395871] Train: [13/100][1283/1462] Data 0.001 (0.002) Batch 2.820 (2.879) Remain 101:51:02 loss: 0.3322 Lr: 0.00200
[2025-09-06 13:56:10,931 INFO misc.py line 117 1395871] Train: [13/100][1284/1462] Data 0.001 (0.002) Batch 2.861 (2.879) Remain 101:50:58 loss: 0.3366 Lr: 0.00200
[2025-09-06 13:56:13,774 INFO misc.py line 117 1395871] Train: [13/100][1285/1462] Data 0.001 (0.002) Batch 2.843 (2.879) Remain 101:50:51 loss: 0.3369 Lr: 0.00200
[2025-09-06 13:56:16,617 INFO misc.py line 117 1395871] Train: [13/100][1286/1462] Data 0.001 (0.002) Batch 2.843 (2.879) Remain 101:50:45 loss: 0.3567 Lr: 0.00200
[2025-09-06 13:56:19,296 INFO misc.py line 117 1395871] Train: [13/100][1287/1462] Data 0.001 (0.002) Batch 2.679 (2.878) Remain 101:50:22 loss: 0.5122 Lr: 0.00200
[2025-09-06 13:56:22,278 INFO misc.py line 117 1395871] Train: [13/100][1288/1462] Data 0.001 (0.002) Batch 2.982 (2.879) Remain 101:50:29 loss: 0.5713 Lr: 0.00200
[2025-09-06 13:56:25,112 INFO misc.py line 117 1395871] Train: [13/100][1289/1462] Data 0.001 (0.002) Batch 2.834 (2.878) Remain 101:50:22 loss: 0.3051 Lr: 0.00200
[2025-09-06 13:56:27,641 INFO misc.py line 117 1395871] Train: [13/100][1290/1462] Data 0.001 (0.002) Batch 2.529 (2.878) Remain 101:49:45 loss: 0.4645 Lr: 0.00200
[2025-09-06 13:56:30,262 INFO misc.py line 117 1395871] Train: [13/100][1291/1462] Data 0.002 (0.002) Batch 2.621 (2.878) Remain 101:49:16 loss: 0.7845 Lr: 0.00200
[2025-09-06 13:56:32,938 INFO misc.py line 117 1395871] Train: [13/100][1292/1462] Data 0.001 (0.002) Batch 2.676 (2.878) Remain 101:48:54 loss: 0.4465 Lr: 0.00200
[2025-09-06 13:56:35,867 INFO misc.py line 117 1395871] Train: [13/100][1293/1462] Data 0.002 (0.002) Batch 2.929 (2.878) Remain 101:48:56 loss: 0.5678 Lr: 0.00200
[2025-09-06 13:56:38,680 INFO misc.py line 117 1395871] Train: [13/100][1294/1462] Data 0.002 (0.002) Batch 2.813 (2.878) Remain 101:48:46 loss: 0.4667 Lr: 0.00200
[2025-09-06 13:56:41,700 INFO misc.py line 117 1395871] Train: [13/100][1295/1462] Data 0.001 (0.002) Batch 3.020 (2.878) Remain 101:48:58 loss: 0.2958 Lr: 0.00200
[2025-09-06 13:56:44,616 INFO misc.py line 117 1395871] Train: [13/100][1296/1462] Data 0.002 (0.002) Batch 2.916 (2.878) Remain 101:48:58 loss: 0.5781 Lr: 0.00200
[2025-09-06 13:56:47,671 INFO misc.py line 117 1395871] Train: [13/100][1297/1462] Data 0.002 (0.002) Batch 3.055 (2.878) Remain 101:49:13 loss: 0.4452 Lr: 0.00200
[2025-09-06 13:56:50,727 INFO misc.py line 117 1395871] Train: [13/100][1298/1462] Data 0.002 (0.002) Batch 3.055 (2.878) Remain 101:49:27 loss: 0.5334 Lr: 0.00200
[2025-09-06 13:56:53,438 INFO misc.py line 117 1395871] Train: [13/100][1299/1462] Data 0.002 (0.002) Batch 2.711 (2.878) Remain 101:49:08 loss: 0.4559 Lr: 0.00200
[2025-09-06 13:56:56,452 INFO misc.py line 117 1395871] Train: [13/100][1300/1462] Data 0.001 (0.002) Batch 3.015 (2.878) Remain 101:49:19 loss: 0.2845 Lr: 0.00200
[2025-09-06 13:56:59,463 INFO misc.py line 117 1395871] Train: [13/100][1301/1462] Data 0.001 (0.002) Batch 3.011 (2.878) Remain 101:49:29 loss: 0.3897 Lr: 0.00200
[2025-09-06 13:57:02,463 INFO misc.py line 117 1395871] Train: [13/100][1302/1462] Data 0.001 (0.002) Batch 3.000 (2.878) Remain 101:49:38 loss: 0.2872 Lr: 0.00200
[2025-09-06 13:57:05,087 INFO misc.py line 117 1395871] Train: [13/100][1303/1462] Data 0.001 (0.002) Batch 2.624 (2.878) Remain 101:49:10 loss: 0.2940 Lr: 0.00200
[2025-09-06 13:57:08,249 INFO misc.py line 117 1395871] Train: [13/100][1304/1462] Data 0.001 (0.002) Batch 3.162 (2.878) Remain 101:49:35 loss: 0.4623 Lr: 0.00200
[2025-09-06 13:57:11,161 INFO misc.py line 117 1395871] Train: [13/100][1305/1462] Data 0.001 (0.002) Batch 2.912 (2.878) Remain 101:49:35 loss: 0.4389 Lr: 0.00200
[2025-09-06 13:57:14,118 INFO misc.py line 117 1395871] Train: [13/100][1306/1462] Data 0.002 (0.002) Batch 2.957 (2.879) Remain 101:49:40 loss: 0.3780 Lr: 0.00200
[2025-09-06 13:57:16,935 INFO misc.py line 117 1395871] Train: [13/100][1307/1462] Data 0.002 (0.002) Batch 2.817 (2.878) Remain 101:49:31 loss: 0.3265 Lr: 0.00200
[2025-09-06 13:57:19,669 INFO misc.py line 117 1395871] Train: [13/100][1308/1462] Data 0.001 (0.002) Batch 2.734 (2.878) Remain 101:49:14 loss: 0.3916 Lr: 0.00200
[2025-09-06 13:57:22,256 INFO misc.py line 117 1395871] Train: [13/100][1309/1462] Data 0.001 (0.002) Batch 2.586 (2.878) Remain 101:48:43 loss: 0.2495 Lr: 0.00200
[2025-09-06 13:57:25,272 INFO misc.py line 117 1395871] Train: [13/100][1310/1462] Data 0.001 (0.002) Batch 3.017 (2.878) Remain 101:48:54 loss: 0.5751 Lr: 0.00200
[2025-09-06 13:57:28,266 INFO misc.py line 117 1395871] Train: [13/100][1311/1462] Data 0.001 (0.002) Batch 2.994 (2.878) Remain 101:49:02 loss: 0.3850 Lr: 0.00200
[2025-09-06 13:57:31,078 INFO misc.py line 117 1395871] Train: [13/100][1312/1462] Data 0.001 (0.002) Batch 2.812 (2.878) Remain 101:48:53 loss: 0.3812 Lr: 0.00200
[2025-09-06 13:57:33,789 INFO misc.py line 117 1395871] Train: [13/100][1313/1462] Data 0.001 (0.002) Batch 2.711 (2.878) Remain 101:48:33 loss: 0.3119 Lr: 0.00200
[2025-09-06 13:57:36,636 INFO misc.py line 117 1395871] Train: [13/100][1314/1462] Data 0.001 (0.002) Batch 2.847 (2.878) Remain 101:48:28 loss: 0.3164 Lr: 0.00200
[2025-09-06 13:57:39,803 INFO misc.py line 117 1395871] Train: [13/100][1315/1462] Data 0.001 (0.002) Batch 3.167 (2.878) Remain 101:48:53 loss: 0.9149 Lr: 0.00200
[2025-09-06 13:57:42,762 INFO misc.py line 117 1395871] Train: [13/100][1316/1462] Data 0.001 (0.002) Batch 2.959 (2.878) Remain 101:48:58 loss: 0.4775 Lr: 0.00200
[2025-09-06 13:57:45,904 INFO misc.py line 117 1395871] Train: [13/100][1317/1462] Data 0.001 (0.002) Batch 3.142 (2.879) Remain 101:49:20 loss: 0.5009 Lr: 0.00200
[2025-09-06 13:57:48,631 INFO misc.py line 117 1395871] Train: [13/100][1318/1462] Data 0.002 (0.002) Batch 2.727 (2.879) Remain 101:49:03 loss: 0.2915 Lr: 0.00200
[2025-09-06 13:57:51,433 INFO misc.py line 117 1395871] Train: [13/100][1319/1462] Data 0.001 (0.002) Batch 2.801 (2.878) Remain 101:48:52 loss: 0.2695 Lr: 0.00200
[2025-09-06 13:57:54,253 INFO misc.py line 117 1395871] Train: [13/100][1320/1462] Data 0.003 (0.002) Batch 2.821 (2.878) Remain 101:48:44 loss: 0.3743 Lr: 0.00200
[2025-09-06 13:57:57,328 INFO misc.py line 117 1395871] Train: [13/100][1321/1462] Data 0.002 (0.002) Batch 3.075 (2.879) Remain 101:49:00 loss: 0.5330 Lr: 0.00200
[2025-09-06 13:58:00,059 INFO misc.py line 117 1395871] Train: [13/100][1322/1462] Data 0.002 (0.002) Batch 2.731 (2.878) Remain 101:48:43 loss: 0.4123 Lr: 0.00200
[2025-09-06 13:58:02,690 INFO misc.py line 117 1395871] Train: [13/100][1323/1462] Data 0.001 (0.002) Batch 2.631 (2.878) Remain 101:48:16 loss: 0.3023 Lr: 0.00200
[2025-09-06 13:58:05,618 INFO misc.py line 117 1395871] Train: [13/100][1324/1462] Data 0.002 (0.002) Batch 2.927 (2.878) Remain 101:48:18 loss: 0.4996 Lr: 0.00200
[2025-09-06 13:58:08,511 INFO misc.py line 117 1395871] Train: [13/100][1325/1462] Data 0.001 (0.002) Batch 2.893 (2.878) Remain 101:48:17 loss: 0.6527 Lr: 0.00200
[2025-09-06 13:58:11,147 INFO misc.py line 117 1395871] Train: [13/100][1326/1462] Data 0.001 (0.002) Batch 2.637 (2.878) Remain 101:47:50 loss: 0.3935 Lr: 0.00200
[2025-09-06 13:58:13,913 INFO misc.py line 117 1395871] Train: [13/100][1327/1462] Data 0.001 (0.002) Batch 2.766 (2.878) Remain 101:47:37 loss: 0.5257 Lr: 0.00200
[2025-09-06 13:58:16,690 INFO misc.py line 117 1395871] Train: [13/100][1328/1462] Data 0.001 (0.002) Batch 2.777 (2.878) Remain 101:47:24 loss: 0.6378 Lr: 0.00200
[2025-09-06 13:58:19,509 INFO misc.py line 117 1395871] Train: [13/100][1329/1462] Data 0.001 (0.002) Batch 2.819 (2.878) Remain 101:47:16 loss: 0.6505 Lr: 0.00200
[2025-09-06 13:58:22,444 INFO misc.py line 117 1395871] Train: [13/100][1330/1462] Data 0.001 (0.002) Batch 2.935 (2.878) Remain 101:47:18 loss: 0.4011 Lr: 0.00200
[2025-09-06 13:58:25,441 INFO misc.py line 117 1395871] Train: [13/100][1331/1462] Data 0.001 (0.002) Batch 2.997 (2.878) Remain 101:47:27 loss: 0.5296 Lr: 0.00200
[2025-09-06 13:58:28,142 INFO misc.py line 117 1395871] Train: [13/100][1332/1462] Data 0.002 (0.002) Batch 2.702 (2.878) Remain 101:47:07 loss: 0.3480 Lr: 0.00200
[2025-09-06 13:58:30,907 INFO misc.py line 117 1395871] Train: [13/100][1333/1462] Data 0.001 (0.002) Batch 2.765 (2.878) Remain 101:46:53 loss: 0.2942 Lr: 0.00200
[2025-09-06 13:58:33,950 INFO misc.py line 117 1395871] Train: [13/100][1334/1462] Data 0.002 (0.002) Batch 3.042 (2.878) Remain 101:47:06 loss: 0.4176 Lr: 0.00200
[2025-09-06 13:58:37,076 INFO misc.py line 117 1395871] Train: [13/100][1335/1462] Data 0.002 (0.002) Batch 3.126 (2.878) Remain 101:47:27 loss: 0.5974 Lr: 0.00200
[2025-09-06 13:58:39,922 INFO misc.py line 117 1395871] Train: [13/100][1336/1462] Data 0.002 (0.002) Batch 2.846 (2.878) Remain 101:47:21 loss: 0.7886 Lr: 0.00200
[2025-09-06 13:58:42,844 INFO misc.py line 117 1395871] Train: [13/100][1337/1462] Data 0.001 (0.002) Batch 2.922 (2.878) Remain 101:47:22 loss: 0.3760 Lr: 0.00200
[2025-09-06 13:58:45,836 INFO misc.py line 117 1395871] Train: [13/100][1338/1462] Data 0.002 (0.002) Batch 2.993 (2.878) Remain 101:47:30 loss: 0.4236 Lr: 0.00200
[2025-09-06 13:58:48,606 INFO misc.py line 117 1395871] Train: [13/100][1339/1462] Data 0.001 (0.002) Batch 2.769 (2.878) Remain 101:47:17 loss: 1.0463 Lr: 0.00200
[2025-09-06 13:58:51,479 INFO misc.py line 117 1395871] Train: [13/100][1340/1462] Data 0.001 (0.002) Batch 2.873 (2.878) Remain 101:47:14 loss: 0.5197 Lr: 0.00200
[2025-09-06 13:58:54,524 INFO misc.py line 117 1395871] Train: [13/100][1341/1462] Data 0.001 (0.002) Batch 3.046 (2.878) Remain 101:47:27 loss: 0.3505 Lr: 0.00200
[2025-09-06 13:58:57,100 INFO misc.py line 117 1395871] Train: [13/100][1342/1462] Data 0.002 (0.002) Batch 2.576 (2.878) Remain 101:46:55 loss: 0.2659 Lr: 0.00200
[2025-09-06 13:58:59,940 INFO misc.py line 117 1395871] Train: [13/100][1343/1462] Data 0.001 (0.002) Batch 2.839 (2.878) Remain 101:46:49 loss: 0.3306 Lr: 0.00200
[2025-09-06 13:59:02,449 INFO misc.py line 117 1395871] Train: [13/100][1344/1462] Data 0.002 (0.002) Batch 2.509 (2.878) Remain 101:46:11 loss: 0.5128 Lr: 0.00200
[2025-09-06 13:59:05,612 INFO misc.py line 117 1395871] Train: [13/100][1345/1462] Data 0.002 (0.002) Batch 3.163 (2.878) Remain 101:46:35 loss: 0.2095 Lr: 0.00200
[2025-09-06 13:59:08,419 INFO misc.py line 117 1395871] Train: [13/100][1346/1462] Data 0.002 (0.002) Batch 2.807 (2.878) Remain 101:46:25 loss: 0.4038 Lr: 0.00200
[2025-09-06 13:59:11,231 INFO misc.py line 117 1395871] Train: [13/100][1347/1462] Data 0.002 (0.002) Batch 2.813 (2.878) Remain 101:46:16 loss: 0.5717 Lr: 0.00200
[2025-09-06 13:59:14,193 INFO misc.py line 117 1395871] Train: [13/100][1348/1462] Data 0.001 (0.002) Batch 2.962 (2.878) Remain 101:46:21 loss: 0.5198 Lr: 0.00200
[2025-09-06 13:59:16,968 INFO misc.py line 117 1395871] Train: [13/100][1349/1462] Data 0.001 (0.002) Batch 2.775 (2.878) Remain 101:46:09 loss: 0.5107 Lr: 0.00200
[2025-09-06 13:59:19,973 INFO misc.py line 117 1395871] Train: [13/100][1350/1462] Data 0.001 (0.002) Batch 3.005 (2.878) Remain 101:46:18 loss: 0.5612 Lr: 0.00200
[2025-09-06 13:59:22,846 INFO misc.py line 117 1395871] Train: [13/100][1351/1462] Data 0.001 (0.002) Batch 2.873 (2.878) Remain 101:46:15 loss: 0.3086 Lr: 0.00200
[2025-09-06 13:59:25,845 INFO misc.py line 117 1395871] Train: [13/100][1352/1462] Data 0.001 (0.002) Batch 2.998 (2.878) Remain 101:46:23 loss: 0.5669 Lr: 0.00200
[2025-09-06 13:59:28,926 INFO misc.py line 117 1395871] Train: [13/100][1353/1462] Data 0.001 (0.002) Batch 3.081 (2.878) Remain 101:46:39 loss: 0.5394 Lr: 0.00200
[2025-09-06 13:59:31,529 INFO misc.py line 117 1395871] Train: [13/100][1354/1462] Data 0.001 (0.002) Batch 2.603 (2.878) Remain 101:46:10 loss: 0.3234 Lr: 0.00200
[2025-09-06 13:59:34,467 INFO misc.py line 117 1395871] Train: [13/100][1355/1462] Data 0.001 (0.002) Batch 2.938 (2.878) Remain 101:46:13 loss: 0.3330 Lr: 0.00200
[2025-09-06 13:59:37,199 INFO misc.py line 117 1395871] Train: [13/100][1356/1462] Data 0.002 (0.002) Batch 2.732 (2.878) Remain 101:45:57 loss: 0.5872 Lr: 0.00200
[2025-09-06 13:59:40,185 INFO misc.py line 117 1395871] Train: [13/100][1357/1462] Data 0.002 (0.002) Batch 2.986 (2.878) Remain 101:46:04 loss: 0.4881 Lr: 0.00200
[2025-09-06 13:59:42,858 INFO misc.py line 117 1395871] Train: [13/100][1358/1462] Data 0.001 (0.002) Batch 2.673 (2.878) Remain 101:45:42 loss: 0.5170 Lr: 0.00200
[2025-09-06 13:59:45,644 INFO misc.py line 117 1395871] Train: [13/100][1359/1462] Data 0.001 (0.002) Batch 2.786 (2.878) Remain 101:45:30 loss: 0.7719 Lr: 0.00200
[2025-09-06 13:59:48,234 INFO misc.py line 117 1395871] Train: [13/100][1360/1462] Data 0.002 (0.002) Batch 2.589 (2.878) Remain 101:45:00 loss: 0.4897 Lr: 0.00200
[2025-09-06 13:59:51,057 INFO misc.py line 117 1395871] Train: [13/100][1361/1462] Data 0.001 (0.002) Batch 2.823 (2.878) Remain 101:44:52 loss: 0.8126 Lr: 0.00200
[2025-09-06 13:59:54,130 INFO misc.py line 117 1395871] Train: [13/100][1362/1462] Data 0.001 (0.002) Batch 3.074 (2.878) Remain 101:45:08 loss: 0.4300 Lr: 0.00200
[2025-09-06 13:59:57,054 INFO misc.py line 117 1395871] Train: [13/100][1363/1462] Data 0.001 (0.002) Batch 2.924 (2.878) Remain 101:45:09 loss: 0.4181 Lr: 0.00200
[2025-09-06 14:00:00,121 INFO misc.py line 117 1395871] Train: [13/100][1364/1462] Data 0.001 (0.002) Batch 3.067 (2.878) Remain 101:45:24 loss: 0.4405 Lr: 0.00200
[2025-09-06 14:00:03,119 INFO misc.py line 117 1395871] Train: [13/100][1365/1462] Data 0.001 (0.002) Batch 2.998 (2.878) Remain 101:45:33 loss: 0.2863 Lr: 0.00200
[2025-09-06 14:00:06,060 INFO misc.py line 117 1395871] Train: [13/100][1366/1462] Data 0.002 (0.002) Batch 2.941 (2.878) Remain 101:45:36 loss: 0.2977 Lr: 0.00200
[2025-09-06 14:00:09,025 INFO misc.py line 117 1395871] Train: [13/100][1367/1462] Data 0.001 (0.002) Batch 2.965 (2.878) Remain 101:45:41 loss: 0.2724 Lr: 0.00200
[2025-09-06 14:00:11,879 INFO misc.py line 117 1395871] Train: [13/100][1368/1462] Data 0.001 (0.002) Batch 2.854 (2.878) Remain 101:45:36 loss: 0.4496 Lr: 0.00200
[2025-09-06 14:00:14,770 INFO misc.py line 117 1395871] Train: [13/100][1369/1462] Data 0.002 (0.002) Batch 2.891 (2.878) Remain 101:45:34 loss: 0.5207 Lr: 0.00200
[2025-09-06 14:00:17,618 INFO misc.py line 117 1395871] Train: [13/100][1370/1462] Data 0.001 (0.002) Batch 2.849 (2.878) Remain 101:45:28 loss: 0.3355 Lr: 0.00200
[2025-09-06 14:00:20,957 INFO misc.py line 117 1395871] Train: [13/100][1371/1462] Data 0.001 (0.002) Batch 3.338 (2.878) Remain 101:46:08 loss: 0.5522 Lr: 0.00200
[2025-09-06 14:00:23,930 INFO misc.py line 117 1395871] Train: [13/100][1372/1462] Data 0.002 (0.002) Batch 2.973 (2.878) Remain 101:46:14 loss: 0.2443 Lr: 0.00200
[2025-09-06 14:00:26,970 INFO misc.py line 117 1395871] Train: [13/100][1373/1462] Data 0.001 (0.002) Batch 3.040 (2.879) Remain 101:46:26 loss: 0.2676 Lr: 0.00200
[2025-09-06 14:00:30,040 INFO misc.py line 117 1395871] Train: [13/100][1374/1462] Data 0.002 (0.002) Batch 3.070 (2.879) Remain 101:46:41 loss: 0.4379 Lr: 0.00200
[2025-09-06 14:00:32,565 INFO misc.py line 117 1395871] Train: [13/100][1375/1462] Data 0.001 (0.002) Batch 2.525 (2.878) Remain 101:46:06 loss: 0.6251 Lr: 0.00200
[2025-09-06 14:00:35,605 INFO misc.py line 117 1395871] Train: [13/100][1376/1462] Data 0.002 (0.002) Batch 3.040 (2.879) Remain 101:46:18 loss: 0.5332 Lr: 0.00200
[2025-09-06 14:00:38,846 INFO misc.py line 117 1395871] Train: [13/100][1377/1462] Data 0.002 (0.002) Batch 3.241 (2.879) Remain 101:46:48 loss: 0.2521 Lr: 0.00200
[2025-09-06 14:00:41,767 INFO misc.py line 117 1395871] Train: [13/100][1378/1462] Data 0.001 (0.002) Batch 2.921 (2.879) Remain 101:46:49 loss: 0.3495 Lr: 0.00200
[2025-09-06 14:00:44,594 INFO misc.py line 117 1395871] Train: [13/100][1379/1462] Data 0.002 (0.002) Batch 2.827 (2.879) Remain 101:46:42 loss: 0.2202 Lr: 0.00200
[2025-09-06 14:00:47,607 INFO misc.py line 117 1395871] Train: [13/100][1380/1462] Data 0.001 (0.002) Batch 3.012 (2.879) Remain 101:46:51 loss: 0.6688 Lr: 0.00200
[2025-09-06 14:00:50,527 INFO misc.py line 117 1395871] Train: [13/100][1381/1462] Data 0.001 (0.002) Batch 2.920 (2.879) Remain 101:46:52 loss: 0.3669 Lr: 0.00200
[2025-09-06 14:00:53,458 INFO misc.py line 117 1395871] Train: [13/100][1382/1462] Data 0.001 (0.002) Batch 2.931 (2.879) Remain 101:46:54 loss: 0.5471 Lr: 0.00200
[2025-09-06 14:00:56,504 INFO misc.py line 117 1395871] Train: [13/100][1383/1462] Data 0.002 (0.002) Batch 3.046 (2.879) Remain 101:47:07 loss: 0.3222 Lr: 0.00200
[2025-09-06 14:00:59,218 INFO misc.py line 117 1395871] Train: [13/100][1384/1462] Data 0.002 (0.002) Batch 2.714 (2.879) Remain 101:46:49 loss: 0.4376 Lr: 0.00200
[2025-09-06 14:01:02,358 INFO misc.py line 117 1395871] Train: [13/100][1385/1462] Data 0.002 (0.002) Batch 3.141 (2.879) Remain 101:47:10 loss: 0.3392 Lr: 0.00200
[2025-09-06 14:01:05,837 INFO misc.py line 117 1395871] Train: [13/100][1386/1462] Data 0.002 (0.002) Batch 3.479 (2.880) Remain 101:48:02 loss: 0.4243 Lr: 0.00200
[2025-09-06 14:01:08,550 INFO misc.py line 117 1395871] Train: [13/100][1387/1462] Data 0.002 (0.002) Batch 2.713 (2.879) Remain 101:47:44 loss: 0.4417 Lr: 0.00200
[2025-09-06 14:01:11,384 INFO misc.py line 117 1395871] Train: [13/100][1388/1462] Data 0.002 (0.002) Batch 2.834 (2.879) Remain 101:47:37 loss: 0.6259 Lr: 0.00200
[2025-09-06 14:01:13,953 INFO misc.py line 117 1395871] Train: [13/100][1389/1462] Data 0.002 (0.002) Batch 2.569 (2.879) Remain 101:47:05 loss: 0.2223 Lr: 0.00200
[2025-09-06 14:01:16,810 INFO misc.py line 117 1395871] Train: [13/100][1390/1462] Data 0.002 (0.002) Batch 2.857 (2.879) Remain 101:47:01 loss: 0.3129 Lr: 0.00200
[2025-09-06 14:01:19,578 INFO misc.py line 117 1395871] Train: [13/100][1391/1462] Data 0.001 (0.002) Batch 2.768 (2.879) Remain 101:46:47 loss: 0.3566 Lr: 0.00200
[2025-09-06 14:01:22,613 INFO misc.py line 117 1395871] Train: [13/100][1392/1462] Data 0.002 (0.002) Batch 3.035 (2.879) Remain 101:46:59 loss: 0.4755 Lr: 0.00200
[2025-09-06 14:01:25,475 INFO misc.py line 117 1395871] Train: [13/100][1393/1462] Data 0.001 (0.002) Batch 2.863 (2.879) Remain 101:46:54 loss: 0.2040 Lr: 0.00200
[2025-09-06 14:01:28,134 INFO misc.py line 117 1395871] Train: [13/100][1394/1462] Data 0.002 (0.002) Batch 2.659 (2.879) Remain 101:46:31 loss: 0.3286 Lr: 0.00200
[2025-09-06 14:01:30,850 INFO misc.py line 117 1395871] Train: [13/100][1395/1462] Data 0.002 (0.002) Batch 2.715 (2.879) Remain 101:46:14 loss: 0.7493 Lr: 0.00200
[2025-09-06 14:01:34,290 INFO misc.py line 117 1395871] Train: [13/100][1396/1462] Data 0.002 (0.002) Batch 3.440 (2.879) Remain 101:47:02 loss: 0.3824 Lr: 0.00200
[2025-09-06 14:01:37,042 INFO misc.py line 117 1395871] Train: [13/100][1397/1462] Data 0.002 (0.002) Batch 2.752 (2.879) Remain 101:46:47 loss: 0.2947 Lr: 0.00200
[2025-09-06 14:01:39,835 INFO misc.py line 117 1395871] Train: [13/100][1398/1462] Data 0.001 (0.002) Batch 2.793 (2.879) Remain 101:46:37 loss: 0.3599 Lr: 0.00200
[2025-09-06 14:01:42,689 INFO misc.py line 117 1395871] Train: [13/100][1399/1462] Data 0.002 (0.002) Batch 2.854 (2.879) Remain 101:46:32 loss: 0.2650 Lr: 0.00200
[2025-09-06 14:01:45,597 INFO misc.py line 117 1395871] Train: [13/100][1400/1462] Data 0.002 (0.002) Batch 2.908 (2.879) Remain 101:46:31 loss: 0.5725 Lr: 0.00200
[2025-09-06 14:01:48,613 INFO misc.py line 117 1395871] Train: [13/100][1401/1462] Data 0.002 (0.002) Batch 3.016 (2.879) Remain 101:46:41 loss: 0.5628 Lr: 0.00200
[2025-09-06 14:01:51,599 INFO misc.py line 117 1395871] Train: [13/100][1402/1462] Data 0.001 (0.002) Batch 2.986 (2.879) Remain 101:46:48 loss: 0.7918 Lr: 0.00200
[2025-09-06 14:01:54,519 INFO misc.py line 117 1395871] Train: [13/100][1403/1462] Data 0.001 (0.002) Batch 2.920 (2.879) Remain 101:46:49 loss: 0.3785 Lr: 0.00200
[2025-09-06 14:01:57,526 INFO misc.py line 117 1395871] Train: [13/100][1404/1462] Data 0.002 (0.002) Batch 3.006 (2.879) Remain 101:46:57 loss: 0.2955 Lr: 0.00200
[2025-09-06 14:02:00,083 INFO misc.py line 117 1395871] Train: [13/100][1405/1462] Data 0.001 (0.002) Batch 2.558 (2.879) Remain 101:46:25 loss: 0.4499 Lr: 0.00200
[2025-09-06 14:02:03,098 INFO misc.py line 117 1395871] Train: [13/100][1406/1462] Data 0.002 (0.002) Batch 3.014 (2.879) Remain 101:46:35 loss: 0.4213 Lr: 0.00200
[2025-09-06 14:02:06,045 INFO misc.py line 117 1395871] Train: [13/100][1407/1462] Data 0.002 (0.002) Batch 2.947 (2.879) Remain 101:46:38 loss: 0.5682 Lr: 0.00200
[2025-09-06 14:02:08,725 INFO misc.py line 117 1395871] Train: [13/100][1408/1462] Data 0.001 (0.002) Batch 2.680 (2.879) Remain 101:46:17 loss: 0.2719 Lr: 0.00200
[2025-09-06 14:02:11,671 INFO misc.py line 117 1395871] Train: [13/100][1409/1462] Data 0.001 (0.002) Batch 2.946 (2.879) Remain 101:46:20 loss: 0.2653 Lr: 0.00200
[2025-09-06 14:02:14,692 INFO misc.py line 117 1395871] Train: [13/100][1410/1462] Data 0.001 (0.002) Batch 3.021 (2.879) Remain 101:46:30 loss: 0.4803 Lr: 0.00200
[2025-09-06 14:02:17,794 INFO misc.py line 117 1395871] Train: [13/100][1411/1462] Data 0.002 (0.002) Batch 3.102 (2.880) Remain 101:46:47 loss: 0.4095 Lr: 0.00200
[2025-09-06 14:02:20,734 INFO misc.py line 117 1395871] Train: [13/100][1412/1462] Data 0.001 (0.002) Batch 2.939 (2.880) Remain 101:46:50 loss: 0.3826 Lr: 0.00200
[2025-09-06 14:02:23,402 INFO misc.py line 117 1395871] Train: [13/100][1413/1462] Data 0.002 (0.002) Batch 2.669 (2.879) Remain 101:46:28 loss: 0.5169 Lr: 0.00200
[2025-09-06 14:02:26,223 INFO misc.py line 117 1395871] Train: [13/100][1414/1462] Data 0.001 (0.002) Batch 2.821 (2.879) Remain 101:46:20 loss: 0.3796 Lr: 0.00200
[2025-09-06 14:02:28,903 INFO misc.py line 117 1395871] Train: [13/100][1415/1462] Data 0.002 (0.002) Batch 2.680 (2.879) Remain 101:45:59 loss: 0.3161 Lr: 0.00200
[2025-09-06 14:02:31,590 INFO misc.py line 117 1395871] Train: [13/100][1416/1462] Data 0.002 (0.002) Batch 2.687 (2.879) Remain 101:45:39 loss: 0.3859 Lr: 0.00200
[2025-09-06 14:02:34,373 INFO misc.py line 117 1395871] Train: [13/100][1417/1462] Data 0.002 (0.002) Batch 2.783 (2.879) Remain 101:45:27 loss: 0.3872 Lr: 0.00200
[2025-09-06 14:02:37,627 INFO misc.py line 117 1395871] Train: [13/100][1418/1462] Data 0.002 (0.002) Batch 3.254 (2.879) Remain 101:45:58 loss: 0.6631 Lr: 0.00200
[2025-09-06 14:02:40,426 INFO misc.py line 117 1395871] Train: [13/100][1419/1462] Data 0.002 (0.002) Batch 2.800 (2.879) Remain 101:45:48 loss: 0.3643 Lr: 0.00200
[2025-09-06 14:02:43,058 INFO misc.py line 117 1395871] Train: [13/100][1420/1462] Data 0.001 (0.002) Batch 2.632 (2.879) Remain 101:45:23 loss: 0.6360 Lr: 0.00200
[2025-09-06 14:02:45,864 INFO misc.py line 117 1395871] Train: [13/100][1421/1462] Data 0.002 (0.002) Batch 2.806 (2.879) Remain 101:45:13 loss: 0.5087 Lr: 0.00200
[2025-09-06 14:02:48,854 INFO misc.py line 117 1395871] Train: [13/100][1422/1462] Data 0.001 (0.002) Batch 2.990 (2.879) Remain 101:45:20 loss: 0.3330 Lr: 0.00200
[2025-09-06 14:02:51,707 INFO misc.py line 117 1395871] Train: [13/100][1423/1462] Data 0.002 (0.002) Batch 2.853 (2.879) Remain 101:45:15 loss: 0.7047 Lr: 0.00200
[2025-09-06 14:02:54,243 INFO misc.py line 117 1395871] Train: [13/100][1424/1462] Data 0.001 (0.002) Batch 2.536 (2.879) Remain 101:44:42 loss: 0.3226 Lr: 0.00200
[2025-09-06 14:02:57,163 INFO misc.py line 117 1395871] Train: [13/100][1425/1462] Data 0.001 (0.002) Batch 2.920 (2.879) Remain 101:44:42 loss: 0.3202 Lr: 0.00200
[2025-09-06 14:02:59,988 INFO misc.py line 117 1395871] Train: [13/100][1426/1462] Data 0.002 (0.002) Batch 2.824 (2.879) Remain 101:44:35 loss: 0.5525 Lr: 0.00200
[2025-09-06 14:03:02,720 INFO misc.py line 117 1395871] Train: [13/100][1427/1462] Data 0.002 (0.002) Batch 2.733 (2.879) Remain 101:44:19 loss: 0.2288 Lr: 0.00200
[2025-09-06 14:03:05,546 INFO misc.py line 117 1395871] Train: [13/100][1428/1462] Data 0.001 (0.002) Batch 2.825 (2.879) Remain 101:44:11 loss: 0.2987 Lr: 0.00200
[2025-09-06 14:03:08,496 INFO misc.py line 117 1395871] Train: [13/100][1429/1462] Data 0.001 (0.002) Batch 2.951 (2.879) Remain 101:44:15 loss: 0.6252 Lr: 0.00200
[2025-09-06 14:03:11,359 INFO misc.py line 117 1395871] Train: [13/100][1430/1462] Data 0.001 (0.002) Batch 2.862 (2.879) Remain 101:44:10 loss: 0.3203 Lr: 0.00200
[2025-09-06 14:03:13,995 INFO misc.py line 117 1395871] Train: [13/100][1431/1462] Data 0.002 (0.002) Batch 2.636 (2.879) Remain 101:43:46 loss: 0.4873 Lr: 0.00200
[2025-09-06 14:03:16,891 INFO misc.py line 117 1395871] Train: [13/100][1432/1462] Data 0.001 (0.002) Batch 2.896 (2.879) Remain 101:43:44 loss: 0.4618 Lr: 0.00200
[2025-09-06 14:03:19,576 INFO misc.py line 117 1395871] Train: [13/100][1433/1462] Data 0.002 (0.002) Batch 2.685 (2.878) Remain 101:43:24 loss: 0.3207 Lr: 0.00200
[2025-09-06 14:03:22,480 INFO misc.py line 117 1395871] Train: [13/100][1434/1462] Data 0.002 (0.002) Batch 2.904 (2.878) Remain 101:43:24 loss: 0.5080 Lr: 0.00200
[2025-09-06 14:03:25,580 INFO misc.py line 117 1395871] Train: [13/100][1435/1462] Data 0.001 (0.002) Batch 3.100 (2.879) Remain 101:43:41 loss: 0.4727 Lr: 0.00200
[2025-09-06 14:03:28,737 INFO misc.py line 117 1395871] Train: [13/100][1436/1462] Data 0.001 (0.002) Batch 3.157 (2.879) Remain 101:44:02 loss: 0.5868 Lr: 0.00200
[2025-09-06 14:03:31,647 INFO misc.py line 117 1395871] Train: [13/100][1437/1462] Data 0.001 (0.002) Batch 2.910 (2.879) Remain 101:44:02 loss: 0.3452 Lr: 0.00200
[2025-09-06 14:03:34,881 INFO misc.py line 117 1395871] Train: [13/100][1438/1462] Data 0.002 (0.002) Batch 3.234 (2.879) Remain 101:44:31 loss: 0.6057 Lr: 0.00200
[2025-09-06 14:03:37,726 INFO misc.py line 117 1395871] Train: [13/100][1439/1462] Data 0.001 (0.002) Batch 2.845 (2.879) Remain 101:44:25 loss: 0.2399 Lr: 0.00200
[2025-09-06 14:03:40,807 INFO misc.py line 117 1395871] Train: [13/100][1440/1462] Data 0.001 (0.002) Batch 3.082 (2.879) Remain 101:44:40 loss: 0.4352 Lr: 0.00200
[2025-09-06 14:03:43,575 INFO misc.py line 117 1395871] Train: [13/100][1441/1462] Data 0.001 (0.002) Batch 2.768 (2.879) Remain 101:44:27 loss: 0.5162 Lr: 0.00200
[2025-09-06 14:03:46,893 INFO misc.py line 117 1395871] Train: [13/100][1442/1462] Data 0.002 (0.002) Batch 3.318 (2.879) Remain 101:45:03 loss: 0.3364 Lr: 0.00200
[2025-09-06 14:03:49,845 INFO misc.py line 117 1395871] Train: [13/100][1443/1462] Data 0.001 (0.002) Batch 2.952 (2.879) Remain 101:45:07 loss: 0.3596 Lr: 0.00200
[2025-09-06 14:03:52,775 INFO misc.py line 117 1395871] Train: [13/100][1444/1462] Data 0.002 (0.002) Batch 2.931 (2.880) Remain 101:45:08 loss: 0.3965 Lr: 0.00200
[2025-09-06 14:03:55,903 INFO misc.py line 117 1395871] Train: [13/100][1445/1462] Data 0.002 (0.002) Batch 3.128 (2.880) Remain 101:45:27 loss: 0.4022 Lr: 0.00200
[2025-09-06 14:03:58,845 INFO misc.py line 117 1395871] Train: [13/100][1446/1462] Data 0.001 (0.002) Batch 2.942 (2.880) Remain 101:45:30 loss: 0.3713 Lr: 0.00200
[2025-09-06 14:04:01,929 INFO misc.py line 117 1395871] Train: [13/100][1447/1462] Data 0.001 (0.002) Batch 3.084 (2.880) Remain 101:45:45 loss: 0.2492 Lr: 0.00200
[2025-09-06 14:04:04,544 INFO misc.py line 117 1395871] Train: [13/100][1448/1462] Data 0.001 (0.002) Batch 2.616 (2.880) Remain 101:45:19 loss: 0.5552 Lr: 0.00200
[2025-09-06 14:04:07,453 INFO misc.py line 117 1395871] Train: [13/100][1449/1462] Data 0.001 (0.002) Batch 2.909 (2.880) Remain 101:45:19 loss: 0.3079 Lr: 0.00200
[2025-09-06 14:04:09,997 INFO misc.py line 117 1395871] Train: [13/100][1450/1462] Data 0.001 (0.002) Batch 2.544 (2.879) Remain 101:44:46 loss: 0.5810 Lr: 0.00200
[2025-09-06 14:04:12,621 INFO misc.py line 117 1395871] Train: [13/100][1451/1462] Data 0.001 (0.002) Batch 2.624 (2.879) Remain 101:44:21 loss: 0.3163 Lr: 0.00200
[2025-09-06 14:04:15,851 INFO misc.py line 117 1395871] Train: [13/100][1452/1462] Data 0.001 (0.002) Batch 3.230 (2.880) Remain 101:44:49 loss: 0.3868 Lr: 0.00200
[2025-09-06 14:04:19,019 INFO misc.py line 117 1395871] Train: [13/100][1453/1462] Data 0.001 (0.002) Batch 3.168 (2.880) Remain 101:45:11 loss: 0.3200 Lr: 0.00200
[2025-09-06 14:04:21,922 INFO misc.py line 117 1395871] Train: [13/100][1454/1462] Data 0.001 (0.002) Batch 2.903 (2.880) Remain 101:45:10 loss: 0.8350 Lr: 0.00200
[2025-09-06 14:04:24,690 INFO misc.py line 117 1395871] Train: [13/100][1455/1462] Data 0.001 (0.002) Batch 2.768 (2.880) Remain 101:44:58 loss: 0.2483 Lr: 0.00200
[2025-09-06 14:04:27,689 INFO misc.py line 117 1395871] Train: [13/100][1456/1462] Data 0.001 (0.002) Batch 2.999 (2.880) Remain 101:45:05 loss: 0.6116 Lr: 0.00200
[2025-09-06 14:04:30,261 INFO misc.py line 117 1395871] Train: [13/100][1457/1462] Data 0.001 (0.002) Batch 2.572 (2.880) Remain 101:44:35 loss: 0.1648 Lr: 0.00200
[2025-09-06 14:04:33,165 INFO misc.py line 117 1395871] Train: [13/100][1458/1462] Data 0.001 (0.002) Batch 2.904 (2.880) Remain 101:44:35 loss: 0.3314 Lr: 0.00200
[2025-09-06 14:04:36,050 INFO misc.py line 117 1395871] Train: [13/100][1459/1462] Data 0.001 (0.002) Batch 2.885 (2.880) Remain 101:44:32 loss: 0.3394 Lr: 0.00200
[2025-09-06 14:04:38,963 INFO misc.py line 117 1395871] Train: [13/100][1460/1462] Data 0.001 (0.002) Batch 2.914 (2.880) Remain 101:44:32 loss: 0.3287 Lr: 0.00200
[2025-09-06 14:04:41,821 INFO misc.py line 117 1395871] Train: [13/100][1461/1462] Data 0.001 (0.002) Batch 2.858 (2.880) Remain 101:44:28 loss: 0.6744 Lr: 0.00200
[2025-09-06 14:04:44,468 INFO misc.py line 117 1395871] Train: [13/100][1462/1462] Data 0.001 (0.002) Batch 2.647 (2.879) Remain 101:44:04 loss: 0.3331 Lr: 0.00200
[2025-09-06 14:04:44,469 INFO misc.py line 147 1395871] Train result: loss: 0.4558 
[2025-09-06 14:04:44,469 INFO evaluator.py line 120 1395871] >>>>>>>>>>>>>>>> Start Evaluation >>>>>>>>>>>>>>>>
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/checkpoint.py:87: UserWarning: None of the inputs have requires_grad=True. Gradients will be None
  warnings.warn(
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/checkpoint.py:87: UserWarning: None of the inputs have requires_grad=True. Gradients will be None
  warnings.warn(
[2025-09-06 14:04:55,780 INFO evaluator.py line 161 1395871] Test: [1/40] Loss 0.5317 
[2025-09-06 14:04:56,124 INFO evaluator.py line 161 1395871] Test: [2/40] Loss 0.2661 
[2025-09-06 14:04:56,413 INFO evaluator.py line 161 1395871] Test: [3/40] Loss 0.3121 
[2025-09-06 14:04:56,722 INFO evaluator.py line 161 1395871] Test: [4/40] Loss 0.6448 
[2025-09-06 14:04:57,005 INFO evaluator.py line 161 1395871] Test: [5/40] Loss 0.2835 
[2025-09-06 14:04:57,304 INFO evaluator.py line 161 1395871] Test: [6/40] Loss 0.2141 
[2025-09-06 14:04:57,601 INFO evaluator.py line 161 1395871] Test: [7/40] Loss 0.4819 
[2025-09-06 14:04:57,930 INFO evaluator.py line 161 1395871] Test: [8/40] Loss 0.1573 
[2025-09-06 14:04:58,227 INFO evaluator.py line 161 1395871] Test: [9/40] Loss 0.3023 
[2025-09-06 14:04:58,541 INFO evaluator.py line 161 1395871] Test: [10/40] Loss 0.3246 
[2025-09-06 14:04:58,816 INFO evaluator.py line 161 1395871] Test: [11/40] Loss 0.5629 
[2025-09-06 14:04:59,126 INFO evaluator.py line 161 1395871] Test: [12/40] Loss 0.3499 
[2025-09-06 14:04:59,422 INFO evaluator.py line 161 1395871] Test: [13/40] Loss 0.2386 
[2025-09-06 14:04:59,717 INFO evaluator.py line 161 1395871] Test: [14/40] Loss 0.3731 
[2025-09-06 14:05:00,014 INFO evaluator.py line 161 1395871] Test: [15/40] Loss 2.3764 
[2025-09-06 14:05:00,317 INFO evaluator.py line 161 1395871] Test: [16/40] Loss 0.4275 
[2025-09-06 14:05:00,597 INFO evaluator.py line 161 1395871] Test: [17/40] Loss 0.2907 
[2025-09-06 14:05:00,891 INFO evaluator.py line 161 1395871] Test: [18/40] Loss 0.4000 
[2025-09-06 14:05:01,200 INFO evaluator.py line 161 1395871] Test: [19/40] Loss 0.6101 
[2025-09-06 14:05:01,484 INFO evaluator.py line 161 1395871] Test: [20/40] Loss 2.4592 
[2025-09-06 14:05:01,790 INFO evaluator.py line 161 1395871] Test: [21/40] Loss 0.2963 
[2025-09-06 14:05:02,084 INFO evaluator.py line 161 1395871] Test: [22/40] Loss 0.5662 
[2025-09-06 14:05:02,386 INFO evaluator.py line 161 1395871] Test: [23/40] Loss 0.5274 
[2025-09-06 14:05:02,670 INFO evaluator.py line 161 1395871] Test: [24/40] Loss 0.2203 
[2025-09-06 14:05:02,979 INFO evaluator.py line 161 1395871] Test: [25/40] Loss 0.7397 
[2025-09-06 14:05:03,275 INFO evaluator.py line 161 1395871] Test: [26/40] Loss 0.7158 
[2025-09-06 14:05:03,646 INFO evaluator.py line 161 1395871] Test: [27/40] Loss 0.6441 
[2025-09-06 14:05:03,976 INFO evaluator.py line 161 1395871] Test: [28/40] Loss 0.2074 
[2025-09-06 14:05:04,252 INFO evaluator.py line 161 1395871] Test: [29/40] Loss 0.5357 
[2025-09-06 14:05:04,549 INFO evaluator.py line 161 1395871] Test: [30/40] Loss 0.5095 
[2025-09-06 14:05:04,877 INFO evaluator.py line 161 1395871] Test: [31/40] Loss 0.2631 
[2025-09-06 14:05:05,179 INFO evaluator.py line 161 1395871] Test: [32/40] Loss 0.9819 
[2025-09-06 14:05:05,455 INFO evaluator.py line 161 1395871] Test: [33/40] Loss 0.6424 
[2025-09-06 14:05:05,718 INFO evaluator.py line 161 1395871] Test: [34/40] Loss 0.4387 
[2025-09-06 14:05:06,007 INFO evaluator.py line 161 1395871] Test: [35/40] Loss 0.3509 
[2025-09-06 14:05:06,296 INFO evaluator.py line 161 1395871] Test: [36/40] Loss 0.2257 
[2025-09-06 14:05:06,561 INFO evaluator.py line 161 1395871] Test: [37/40] Loss 0.5982 
[2025-09-06 14:05:06,821 INFO evaluator.py line 161 1395871] Test: [38/40] Loss 0.3325 
[2025-09-06 14:05:07,136 INFO evaluator.py line 161 1395871] Test: [39/40] Loss 0.3066 
[2025-09-06 14:05:07,452 INFO evaluator.py line 161 1395871] Test: [40/40] Loss 0.3244 
[2025-09-06 14:05:08,609 INFO evaluator.py line 176 1395871] Val result: mIoU/mAcc/allAcc 0.4709/0.6335/0.9372.
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_0-unlabeled Result: iou/accuracy 1.0000/1.0000
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_1-铁塔 Result: iou/accuracy 0.4791/0.4933
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_2-中等植被点 Result: iou/accuracy 0.9110/0.9193
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_3-公路 Result: iou/accuracy 0.8201/0.9650
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_4-临时建筑物 Result: iou/accuracy 0.0000/0.0000
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_5-建筑物点 Result: iou/accuracy 0.1752/0.6150
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_6-交叉跨越下 Result: iou/accuracy 0.0587/1.0000
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_7-其他线路 Result: iou/accuracy 0.6268/0.7165
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_8-地面点 Result: iou/accuracy 0.6315/0.9183
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_9-导线 Result: iou/accuracy 0.9480/0.9748
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_10-其他 Result: iou/accuracy 0.0000/0.0000
[2025-09-06 14:05:08,610 INFO evaluator.py line 182 1395871] Class_11-交叉跨越上 Result: iou/accuracy 0.0000/0.0000
[2025-09-06 14:05:08,610 INFO evaluator.py line 225 1395871] <<<<<<<<<<<<<<<<< End Evaluation <<<<<<<<<<<<<<<<<
[2025-09-06 14:05:08,611 INFO misc.py line 187 1395871] Currently Best mIoU: 0.5271
[2025-09-06 14:05:08,611 INFO misc.py line 196 1395871] Saving checkpoint to: exp/powscan/splitSections/model/model_last.pth
[2025-09-06 14:05:14,374 INFO misc.py line 117 1395871] Train: [14/100][1/1462] Data 2.389 (2.389) Batch 5.499 (5.499) Remain 194:17:29 loss: 0.6775 Lr: 0.00200
[2025-09-06 14:05:17,766 INFO misc.py line 117 1395871] Train: [14/100][2/1462] Data 0.002 (0.002) Batch 3.392 (3.392) Remain 119:51:07 loss: 0.5428 Lr: 0.00200
[2025-09-06 14:05:20,817 INFO misc.py line 117 1395871] Train: [14/100][3/1462] Data 0.002 (0.002) Batch 3.051 (3.051) Remain 107:47:19 loss: 0.3109 Lr: 0.00200
[2025-09-06 14:05:23,527 INFO misc.py line 117 1395871] Train: [14/100][4/1462] Data 0.002 (0.002) Batch 2.710 (2.710) Remain 95:44:11 loss: 0.5321 Lr: 0.00200
[2025-09-06 14:05:26,270 INFO misc.py line 117 1395871] Train: [14/100][5/1462] Data 0.002 (0.002) Batch 2.744 (2.727) Remain 96:19:56 loss: 0.3932 Lr: 0.00200
[2025-09-06 14:05:29,425 INFO misc.py line 117 1395871] Train: [14/100][6/1462] Data 0.002 (0.002) Batch 3.155 (2.869) Remain 101:22:23 loss: 0.4971 Lr: 0.00200
[2025-09-06 14:05:32,350 INFO misc.py line 117 1395871] Train: [14/100][7/1462] Data 0.002 (0.002) Batch 2.925 (2.883) Remain 101:51:54 loss: 0.3125 Lr: 0.00200
[2025-09-06 14:05:34,964 INFO misc.py line 117 1395871] Train: [14/100][8/1462] Data 0.002 (0.002) Batch 2.614 (2.829) Remain 99:57:42 loss: 0.5162 Lr: 0.00200
[2025-09-06 14:05:37,739 INFO misc.py line 117 1395871] Train: [14/100][9/1462] Data 0.001 (0.002) Batch 2.774 (2.820) Remain 99:38:13 loss: 0.3590 Lr: 0.00200
[2025-09-06 14:05:40,779 INFO misc.py line 117 1395871] Train: [14/100][10/1462] Data 0.002 (0.002) Batch 3.041 (2.852) Remain 100:44:57 loss: 0.4204 Lr: 0.00200
[2025-09-06 14:05:43,781 INFO misc.py line 117 1395871] Train: [14/100][11/1462] Data 0.002 (0.002) Batch 3.002 (2.870) Remain 101:24:35 loss: 0.4827 Lr: 0.00200
[2025-09-06 14:05:46,677 INFO misc.py line 117 1395871] Train: [14/100][12/1462] Data 0.002 (0.002) Batch 2.896 (2.873) Remain 101:30:34 loss: 0.3688 Lr: 0.00200
[2025-09-06 14:05:49,493 INFO misc.py line 117 1395871] Train: [14/100][13/1462] Data 0.002 (0.002) Batch 2.816 (2.868) Remain 101:18:27 loss: 0.3151 Lr: 0.00200
[2025-09-06 14:05:52,054 INFO misc.py line 117 1395871] Train: [14/100][14/1462] Data 0.001 (0.002) Batch 2.561 (2.840) Remain 100:19:16 loss: 0.3883 Lr: 0.00200
[2025-09-06 14:05:55,016 INFO misc.py line 117 1395871] Train: [14/100][15/1462] Data 0.002 (0.002) Batch 2.962 (2.850) Remain 100:40:49 loss: 0.4343 Lr: 0.00200
[2025-09-06 14:05:58,052 INFO misc.py line 117 1395871] Train: [14/100][16/1462] Data 0.001 (0.002) Batch 3.036 (2.864) Remain 101:11:06 loss: 0.3908 Lr: 0.00200
[2025-09-06 14:06:01,185 INFO misc.py line 117 1395871] Train: [14/100][17/1462] Data 0.002 (0.002) Batch 3.133 (2.883) Remain 101:51:43 loss: 0.4969 Lr: 0.00200
[2025-09-06 14:06:04,005 INFO misc.py line 117 1395871] Train: [14/100][18/1462] Data 0.001 (0.002) Batch 2.820 (2.879) Remain 101:42:42 loss: 0.4974 Lr: 0.00200
[2025-09-06 14:06:06,880 INFO misc.py line 117 1395871] Train: [14/100][19/1462] Data 0.001 (0.002) Batch 2.875 (2.879) Remain 101:42:05 loss: 0.4374 Lr: 0.00200
[2025-09-06 14:06:09,757 INFO misc.py line 117 1395871] Train: [14/100][20/1462] Data 0.001 (0.002) Batch 2.877 (2.879) Remain 101:41:47 loss: 0.1898 Lr: 0.00200
[2025-09-06 14:06:12,540 INFO misc.py line 117 1395871] Train: [14/100][21/1462] Data 0.001 (0.002) Batch 2.783 (2.873) Remain 101:30:29 loss: 0.3860 Lr: 0.00200
[2025-09-06 14:06:15,544 INFO misc.py line 117 1395871] Train: [14/100][22/1462] Data 0.001 (0.002) Batch 3.004 (2.880) Remain 101:45:01 loss: 0.3425 Lr: 0.00200
[2025-09-06 14:06:18,359 INFO misc.py line 117 1395871] Train: [14/100][23/1462] Data 0.001 (0.002) Batch 2.815 (2.877) Remain 101:38:03 loss: 0.3641 Lr: 0.00200
[2025-09-06 14:06:21,008 INFO misc.py line 117 1395871] Train: [14/100][24/1462] Data 0.001 (0.002) Batch 2.648 (2.866) Remain 101:14:55 loss: 0.3090 Lr: 0.00200
[2025-09-06 14:06:23,899 INFO misc.py line 117 1395871] Train: [14/100][25/1462] Data 0.001 (0.002) Batch 2.892 (2.867) Remain 101:17:19 loss: 0.3683 Lr: 0.00200
[2025-09-06 14:06:26,751 INFO misc.py line 117 1395871] Train: [14/100][26/1462] Data 0.001 (0.002) Batch 2.852 (2.867) Remain 101:15:49 loss: 0.3857 Lr: 0.00200
[2025-09-06 14:06:29,835 INFO misc.py line 117 1395871] Train: [14/100][27/1462] Data 0.001 (0.002) Batch 3.085 (2.876) Remain 101:35:01 loss: 0.2868 Lr: 0.00200
[2025-09-06 14:06:32,454 INFO misc.py line 117 1395871] Train: [14/100][28/1462] Data 0.002 (0.002) Batch 2.618 (2.865) Remain 101:13:09 loss: 0.4059 Lr: 0.00200
[2025-09-06 14:06:35,624 INFO misc.py line 117 1395871] Train: [14/100][29/1462] Data 0.001 (0.002) Batch 3.171 (2.877) Remain 101:37:59 loss: 0.3781 Lr: 0.00200
[2025-09-06 14:06:38,126 INFO misc.py line 117 1395871] Train: [14/100][30/1462] Data 0.001 (0.002) Batch 2.501 (2.863) Remain 101:08:26 loss: 0.2246 Lr: 0.00200
[2025-09-06 14:06:41,031 INFO misc.py line 117 1395871] Train: [14/100][31/1462] Data 0.001 (0.002) Batch 2.905 (2.865) Remain 101:11:34 loss: 0.3728 Lr: 0.00200
[2025-09-06 14:06:43,907 INFO misc.py line 117 1395871] Train: [14/100][32/1462] Data 0.002 (0.002) Batch 2.876 (2.865) Remain 101:12:20 loss: 0.4074 Lr: 0.00200
[2025-09-06 14:06:46,683 INFO misc.py line 117 1395871] Train: [14/100][33/1462] Data 0.001 (0.002) Batch 2.776 (2.862) Remain 101:05:58 loss: 0.2248 Lr: 0.00200
[2025-09-06 14:06:49,384 INFO misc.py line 117 1395871] Train: [14/100][34/1462] Data 0.001 (0.002) Batch 2.701 (2.857) Remain 100:54:55 loss: 0.4902 Lr: 0.00200
[2025-09-06 14:06:52,537 INFO misc.py line 117 1395871] Train: [14/100][35/1462] Data 0.002 (0.002) Batch 3.153 (2.866) Remain 101:14:27 loss: 0.2980 Lr: 0.00200
[2025-09-06 14:06:55,330 INFO misc.py line 117 1395871] Train: [14/100][36/1462] Data 0.001 (0.002) Batch 2.793 (2.864) Remain 101:09:44 loss: 0.5089 Lr: 0.00200
[2025-09-06 14:06:58,264 INFO misc.py line 117 1395871] Train: [14/100][37/1462] Data 0.002 (0.002) Batch 2.933 (2.866) Remain 101:14:00 loss: 0.3298 Lr: 0.00200
[2025-09-06 14:07:01,616 INFO misc.py line 117 1395871] Train: [14/100][38/1462] Data 0.002 (0.002) Batch 3.352 (2.880) Remain 101:43:23 loss: 0.4294 Lr: 0.00200
[2025-09-06 14:07:04,686 INFO misc.py line 117 1395871] Train: [14/100][39/1462] Data 0.002 (0.002) Batch 3.070 (2.885) Remain 101:54:33 loss: 0.3217 Lr: 0.00200
[2025-09-06 14:07:07,455 INFO misc.py line 117 1395871] Train: [14/100][40/1462] Data 0.001 (0.002) Batch 2.769 (2.882) Remain 101:47:50 loss: 0.3535 Lr: 0.00200
[2025-09-06 14:07:10,210 INFO misc.py line 117 1395871] Train: [14/100][41/1462] Data 0.001 (0.002) Batch 2.755 (2.879) Remain 101:40:41 loss: 0.4721 Lr: 0.00200
[2025-09-06 14:07:13,033 INFO misc.py line 117 1395871] Train: [14/100][42/1462] Data 0.001 (0.002) Batch 2.824 (2.877) Remain 101:37:38 loss: 0.4200 Lr: 0.00200
[2025-09-06 14:07:16,090 INFO misc.py line 117 1395871] Train: [14/100][43/1462] Data 0.001 (0.002) Batch 3.056 (2.882) Remain 101:47:05 loss: 0.2446 Lr: 0.00200
[2025-09-06 14:07:18,743 INFO misc.py line 117 1395871] Train: [14/100][44/1462] Data 0.002 (0.002) Batch 2.653 (2.876) Remain 101:35:13 loss: 0.3613 Lr: 0.00200
[2025-09-06 14:07:21,867 INFO misc.py line 117 1395871] Train: [14/100][45/1462] Data 0.002 (0.002) Batch 3.124 (2.882) Remain 101:47:40 loss: 0.3079 Lr: 0.00200
[2025-09-06 14:07:24,818 INFO misc.py line 117 1395871] Train: [14/100][46/1462] Data 0.002 (0.002) Batch 2.951 (2.884) Remain 101:51:01 loss: 0.6324 Lr: 0.00200
[2025-09-06 14:07:27,476 INFO misc.py line 117 1395871] Train: [14/100][47/1462] Data 0.001 (0.002) Batch 2.658 (2.879) Remain 101:40:07 loss: 0.3261 Lr: 0.00200
[2025-09-06 14:07:30,420 INFO misc.py line 117 1395871] Train: [14/100][48/1462] Data 0.002 (0.002) Batch 2.944 (2.880) Remain 101:43:09 loss: 0.4569 Lr: 0.00200
[2025-09-06 14:07:33,258 INFO misc.py line 117 1395871] Train: [14/100][49/1462] Data 0.002 (0.002) Batch 2.838 (2.879) Remain 101:41:10 loss: 0.4691 Lr: 0.00200
[2025-09-06 14:07:36,342 INFO misc.py line 117 1395871] Train: [14/100][50/1462] Data 0.002 (0.002) Batch 3.083 (2.883) Remain 101:50:19 loss: 0.3789 Lr: 0.00200
[2025-09-06 14:07:39,000 INFO misc.py line 117 1395871] Train: [14/100][51/1462] Data 0.001 (0.002) Batch 2.658 (2.879) Remain 101:40:19 loss: 0.2842 Lr: 0.00200
[2025-09-06 14:07:41,741 INFO misc.py line 117 1395871] Train: [14/100][52/1462] Data 0.001 (0.002) Batch 2.741 (2.876) Remain 101:34:19 loss: 0.2935 Lr: 0.00200
[2025-09-06 14:07:44,518 INFO misc.py line 117 1395871] Train: [14/100][53/1462] Data 0.001 (0.002) Batch 2.777 (2.874) Remain 101:30:05 loss: 0.5484 Lr: 0.00200
[2025-09-06 14:07:47,658 INFO misc.py line 117 1395871] Train: [14/100][54/1462] Data 0.001 (0.002) Batch 3.140 (2.879) Remain 101:41:06 loss: 0.4422 Lr: 0.00200
[2025-09-06 14:07:50,378 INFO misc.py line 117 1395871] Train: [14/100][55/1462] Data 0.001 (0.002) Batch 2.720 (2.876) Remain 101:34:33 loss: 0.5025 Lr: 0.00200
[2025-09-06 14:07:53,034 INFO misc.py line 117 1395871] Train: [14/100][56/1462] Data 0.001 (0.002) Batch 2.656 (2.872) Remain 101:25:42 loss: 0.4708 Lr: 0.00200
[2025-09-06 14:07:55,803 INFO misc.py line 117 1395871] Train: [14/100][57/1462] Data 0.001 (0.002) Batch 2.769 (2.870) Remain 101:21:35 loss: 0.4338 Lr: 0.00200
[2025-09-06 14:07:58,689 INFO misc.py line 117 1395871] Train: [14/100][58/1462] Data 0.001 (0.002) Batch 2.887 (2.870) Remain 101:22:11 loss: 0.3217 Lr: 0.00200
[2025-09-06 14:08:01,616 INFO misc.py line 117 1395871] Train: [14/100][59/1462] Data 0.002 (0.002) Batch 2.926 (2.871) Remain 101:24:15 loss: 0.3921 Lr: 0.00200
[2025-09-06 14:08:04,347 INFO misc.py line 117 1395871] Train: [14/100][60/1462] Data 0.002 (0.002) Batch 2.731 (2.869) Remain 101:18:59 loss: 0.2218 Lr: 0.00200
[2025-09-06 14:08:06,909 INFO misc.py line 117 1395871] Train: [14/100][61/1462] Data 0.001 (0.002) Batch 2.562 (2.864) Remain 101:07:44 loss: 0.3255 Lr: 0.00200
[2025-09-06 14:08:10,264 INFO misc.py line 117 1395871] Train: [14/100][62/1462] Data 0.001 (0.002) Batch 3.355 (2.872) Remain 101:25:19 loss: 0.4339 Lr: 0.00200
[2025-09-06 14:08:12,907 INFO misc.py line 117 1395871] Train: [14/100][63/1462] Data 0.001 (0.002) Batch 2.643 (2.868) Remain 101:17:12 loss: 0.4569 Lr: 0.00200
[2025-09-06 14:08:15,755 INFO misc.py line 117 1395871] Train: [14/100][64/1462] Data 0.001 (0.002) Batch 2.848 (2.868) Remain 101:16:27 loss: 0.6362 Lr: 0.00200
[2025-09-06 14:08:18,381 INFO misc.py line 117 1395871] Train: [14/100][65/1462] Data 0.001 (0.002) Batch 2.626 (2.864) Remain 101:08:07 loss: 0.4256 Lr: 0.00200
[2025-09-06 14:08:21,155 INFO misc.py line 117 1395871] Train: [14/100][66/1462] Data 0.001 (0.002) Batch 2.774 (2.863) Remain 101:05:04 loss: 0.3813 Lr: 0.00200
[2025-09-06 14:08:23,993 INFO misc.py line 117 1395871] Train: [14/100][67/1462] Data 0.001 (0.002) Batch 2.838 (2.862) Remain 101:04:12 loss: 0.4161 Lr: 0.00200
[2025-09-06 14:08:26,870 INFO misc.py line 117 1395871] Train: [14/100][68/1462] Data 0.001 (0.002) Batch 2.877 (2.862) Remain 101:04:38 loss: 0.5245 Lr: 0.00200
[2025-09-06 14:08:29,946 INFO misc.py line 117 1395871] Train: [14/100][69/1462] Data 0.002 (0.002) Batch 3.077 (2.866) Remain 101:11:28 loss: 0.5155 Lr: 0.00200
[2025-09-06 14:08:32,740 INFO misc.py line 117 1395871] Train: [14/100][70/1462] Data 0.001 (0.002) Batch 2.793 (2.865) Remain 101:09:08 loss: 0.2956 Lr: 0.00200
[2025-09-06 14:08:35,640 INFO misc.py line 117 1395871] Train: [14/100][71/1462] Data 0.002 (0.002) Batch 2.900 (2.865) Remain 101:10:12 loss: 0.5714 Lr: 0.00200
[2025-09-06 14:08:38,696 INFO misc.py line 117 1395871] Train: [14/100][72/1462] Data 0.002 (0.002) Batch 3.056 (2.868) Remain 101:16:01 loss: 0.2529 Lr: 0.00200
[2025-09-06 14:08:41,279 INFO misc.py line 117 1395871] Train: [14/100][73/1462] Data 0.002 (0.002) Batch 2.582 (2.864) Remain 101:07:20 loss: 0.3802 Lr: 0.00200
[2025-09-06 14:08:43,977 INFO misc.py line 117 1395871] Train: [14/100][74/1462] Data 0.001 (0.002) Batch 2.698 (2.861) Remain 101:02:21 loss: 0.6258 Lr: 0.00200
[2025-09-06 14:08:46,562 INFO misc.py line 117 1395871] Train: [14/100][75/1462] Data 0.001 (0.002) Batch 2.585 (2.858) Remain 100:54:11 loss: 0.2746 Lr: 0.00200
[2025-09-06 14:08:49,178 INFO misc.py line 117 1395871] Train: [14/100][76/1462] Data 0.001 (0.002) Batch 2.615 (2.854) Remain 100:47:07 loss: 0.6790 Lr: 0.00200
[2025-09-06 14:08:51,912 INFO misc.py line 117 1395871] Train: [14/100][77/1462] Data 0.002 (0.002) Batch 2.734 (2.853) Remain 100:43:37 loss: 0.2832 Lr: 0.00200
[2025-09-06 14:08:55,010 INFO misc.py line 117 1395871] Train: [14/100][78/1462] Data 0.002 (0.002) Batch 3.098 (2.856) Remain 100:50:30 loss: 0.4581 Lr: 0.00200
[2025-09-06 14:08:58,202 INFO misc.py line 117 1395871] Train: [14/100][79/1462] Data 0.002 (0.002) Batch 3.193 (2.860) Remain 100:59:50 loss: 0.3244 Lr: 0.00200
[2025-09-06 14:09:00,971 INFO misc.py line 117 1395871] Train: [14/100][80/1462] Data 0.001 (0.002) Batch 2.769 (2.859) Remain 100:57:15 loss: 0.8594 Lr: 0.00200
[2025-09-06 14:09:04,257 INFO misc.py line 117 1395871] Train: [14/100][81/1462] Data 0.002 (0.002) Batch 3.286 (2.865) Remain 101:08:49 loss: 0.5252 Lr: 0.00200
[2025-09-06 14:09:07,035 INFO misc.py line 117 1395871] Train: [14/100][82/1462] Data 0.001 (0.002) Batch 2.778 (2.864) Remain 101:06:26 loss: 0.4741 Lr: 0.00200
[2025-09-06 14:09:09,880 INFO misc.py line 117 1395871] Train: [14/100][83/1462] Data 0.001 (0.002) Batch 2.846 (2.863) Remain 101:05:55 loss: 0.3526 Lr: 0.00200
[2025-09-06 14:09:12,908 INFO misc.py line 117 1395871] Train: [14/100][84/1462] Data 0.001 (0.002) Batch 3.028 (2.865) Remain 101:10:10 loss: 0.3284 Lr: 0.00200
[2025-09-06 14:09:15,698 INFO misc.py line 117 1395871] Train: [14/100][85/1462] Data 0.002 (0.002) Batch 2.790 (2.864) Remain 101:08:10 loss: 0.2803 Lr: 0.00200
[2025-09-06 14:09:18,399 INFO misc.py line 117 1395871] Train: [14/100][86/1462] Data 0.001 (0.001) Batch 2.701 (2.862) Remain 101:03:57 loss: 0.2345 Lr: 0.00200
[2025-09-06 14:09:21,258 INFO misc.py line 117 1395871] Train: [14/100][87/1462] Data 0.001 (0.001) Batch 2.859 (2.862) Remain 101:03:49 loss: 0.4181 Lr: 0.00200
[2025-09-06 14:09:23,938 INFO misc.py line 117 1395871] Train: [14/100][88/1462] Data 0.001 (0.001) Batch 2.680 (2.860) Remain 100:59:13 loss: 0.1935 Lr: 0.00200
[2025-09-06 14:09:26,533 INFO misc.py line 117 1395871] Train: [14/100][89/1462] Data 0.001 (0.001) Batch 2.595 (2.857) Remain 100:52:39 loss: 0.3579 Lr: 0.00200
[2025-09-06 14:09:29,411 INFO misc.py line 117 1395871] Train: [14/100][90/1462] Data 0.002 (0.001) Batch 2.878 (2.857) Remain 100:53:07 loss: 0.5983 Lr: 0.00200
[2025-09-06 14:09:32,350 INFO misc.py line 117 1395871] Train: [14/100][91/1462] Data 0.001 (0.001) Batch 2.939 (2.858) Remain 100:55:01 loss: 0.4775 Lr: 0.00200
[2025-09-06 14:09:35,338 INFO misc.py line 117 1395871] Train: [14/100][92/1462] Data 0.001 (0.001) Batch 2.988 (2.860) Remain 100:58:04 loss: 0.4266 Lr: 0.00200
[2025-09-06 14:09:38,211 INFO misc.py line 117 1395871] Train: [14/100][93/1462] Data 0.001 (0.001) Batch 2.873 (2.860) Remain 100:58:20 loss: 0.3730 Lr: 0.00200
[2025-09-06 14:09:40,949 INFO misc.py line 117 1395871] Train: [14/100][94/1462] Data 0.002 (0.001) Batch 2.738 (2.859) Remain 100:55:27 loss: 0.8589 Lr: 0.00200
[2025-09-06 14:09:43,955 INFO misc.py line 117 1395871] Train: [14/100][95/1462] Data 0.002 (0.001) Batch 3.005 (2.860) Remain 100:58:46 loss: 0.3564 Lr: 0.00200
[2025-09-06 14:09:46,729 INFO misc.py line 117 1395871] Train: [14/100][96/1462] Data 0.002 (0.002) Batch 2.774 (2.859) Remain 100:56:46 loss: 0.2543 Lr: 0.00200
[2025-09-06 14:09:49,776 INFO misc.py line 117 1395871] Train: [14/100][97/1462] Data 0.001 (0.002) Batch 3.047 (2.861) Remain 101:00:57 loss: 0.3871 Lr: 0.00200
[2025-09-06 14:09:52,837 INFO misc.py line 117 1395871] Train: [14/100][98/1462] Data 0.001 (0.002) Batch 3.061 (2.863) Remain 101:05:22 loss: 0.3379 Lr: 0.00200
[2025-09-06 14:09:55,660 INFO misc.py line 117 1395871] Train: [14/100][99/1462] Data 0.001 (0.001) Batch 2.823 (2.863) Remain 101:04:25 loss: 0.7071 Lr: 0.00200
[2025-09-06 14:09:58,278 INFO misc.py line 117 1395871] Train: [14/100][100/1462] Data 0.001 (0.001) Batch 2.618 (2.860) Remain 100:59:01 loss: 0.3977 Lr: 0.00200
[2025-09-06 14:10:01,306 INFO misc.py line 117 1395871] Train: [14/100][101/1462] Data 0.001 (0.001) Batch 3.028 (2.862) Remain 101:02:36 loss: 0.5239 Lr: 0.00200
[2025-09-06 14:10:04,289 INFO misc.py line 117 1395871] Train: [14/100][102/1462] Data 0.001 (0.001) Batch 2.983 (2.863) Remain 101:05:08 loss: 0.8313 Lr: 0.00200
[2025-09-06 14:10:07,400 INFO misc.py line 117 1395871] Train: [14/100][103/1462] Data 0.001 (0.001) Batch 3.111 (2.866) Remain 101:10:20 loss: 0.5225 Lr: 0.00200
[2025-09-06 14:10:10,003 INFO misc.py line 117 1395871] Train: [14/100][104/1462] Data 0.001 (0.001) Batch 2.602 (2.863) Remain 101:04:46 loss: 0.1770 Lr: 0.00200
[2025-09-06 14:10:12,600 INFO misc.py line 117 1395871] Train: [14/100][105/1462] Data 0.001 (0.001) Batch 2.598 (2.861) Remain 100:59:13 loss: 0.5164 Lr: 0.00200
[2025-09-06 14:10:15,212 INFO misc.py line 117 1395871] Train: [14/100][106/1462] Data 0.001 (0.001) Batch 2.611 (2.858) Remain 100:54:02 loss: 0.7395 Lr: 0.00200
[2025-09-06 14:10:18,295 INFO misc.py line 117 1395871] Train: [14/100][107/1462] Data 0.002 (0.001) Batch 3.083 (2.860) Remain 100:58:35 loss: 0.5784 Lr: 0.00200
[2025-09-06 14:10:21,227 INFO misc.py line 117 1395871] Train: [14/100][108/1462] Data 0.001 (0.001) Batch 2.932 (2.861) Remain 100:59:58 loss: 0.2577 Lr: 0.00200
[2025-09-06 14:10:24,350 INFO misc.py line 117 1395871] Train: [14/100][109/1462] Data 0.002 (0.001) Batch 3.122 (2.864) Remain 101:05:08 loss: 0.2183 Lr: 0.00200
[2025-09-06 14:10:27,158 INFO misc.py line 117 1395871] Train: [14/100][110/1462] Data 0.002 (0.001) Batch 2.809 (2.863) Remain 101:04:00 loss: 0.5847 Lr: 0.00200
[2025-09-06 14:10:30,042 INFO misc.py line 117 1395871] Train: [14/100][111/1462] Data 0.001 (0.001) Batch 2.884 (2.863) Remain 101:04:22 loss: 0.3626 Lr: 0.00200
[2025-09-06 14:10:33,318 INFO misc.py line 117 1395871] Train: [14/100][112/1462] Data 0.001 (0.001) Batch 3.276 (2.867) Remain 101:12:21 loss: 0.4528 Lr: 0.00200
[2025-09-06 14:10:36,131 INFO misc.py line 117 1395871] Train: [14/100][113/1462] Data 0.002 (0.001) Batch 2.813 (2.866) Remain 101:11:16 loss: 0.3324 Lr: 0.00200
[2025-09-06 14:10:39,018 INFO misc.py line 117 1395871] Train: [14/100][114/1462] Data 0.002 (0.002) Batch 2.887 (2.867) Remain 101:11:36 loss: 0.5099 Lr: 0.00200
[2025-09-06 14:10:42,171 INFO misc.py line 117 1395871] Train: [14/100][115/1462] Data 0.001 (0.001) Batch 3.154 (2.869) Remain 101:16:59 loss: 0.5263 Lr: 0.00200
[2025-09-06 14:10:45,019 INFO misc.py line 117 1395871] Train: [14/100][116/1462] Data 0.001 (0.001) Batch 2.848 (2.869) Remain 101:16:32 loss: 0.5377 Lr: 0.00200
[2025-09-06 14:10:48,173 INFO misc.py line 117 1395871] Train: [14/100][117/1462] Data 0.002 (0.001) Batch 3.154 (2.872) Remain 101:21:46 loss: 0.2222 Lr: 0.00200
[2025-09-06 14:10:51,148 INFO misc.py line 117 1395871] Train: [14/100][118/1462] Data 0.001 (0.001) Batch 2.975 (2.872) Remain 101:23:37 loss: 0.3233 Lr: 0.00200
[2025-09-06 14:10:53,794 INFO misc.py line 117 1395871] Train: [14/100][119/1462] Data 0.002 (0.001) Batch 2.646 (2.870) Remain 101:19:26 loss: 0.4385 Lr: 0.00200
[2025-09-06 14:10:56,630 INFO misc.py line 117 1395871] Train: [14/100][120/1462] Data 0.001 (0.001) Batch 2.836 (2.870) Remain 101:18:46 loss: 0.3371 Lr: 0.00200
[2025-09-06 14:10:59,389 INFO misc.py line 117 1395871] Train: [14/100][121/1462] Data 0.001 (0.001) Batch 2.760 (2.869) Remain 101:16:44 loss: 0.6257 Lr: 0.00200
[2025-09-06 14:11:02,103 INFO misc.py line 117 1395871] Train: [14/100][122/1462] Data 0.001 (0.001) Batch 2.714 (2.868) Remain 101:13:56 loss: 0.4155 Lr: 0.00200
[2025-09-06 14:11:04,796 INFO misc.py line 117 1395871] Train: [14/100][123/1462] Data 0.002 (0.001) Batch 2.693 (2.866) Remain 101:10:47 loss: 0.4902 Lr: 0.00200
[2025-09-06 14:11:07,723 INFO misc.py line 117 1395871] Train: [14/100][124/1462] Data 0.001 (0.001) Batch 2.927 (2.867) Remain 101:11:48 loss: 0.7915 Lr: 0.00200
[2025-09-06 14:11:10,686 INFO misc.py line 117 1395871] Train: [14/100][125/1462] Data 0.002 (0.001) Batch 2.963 (2.868) Remain 101:13:25 loss: 0.4471 Lr: 0.00200
[2025-09-06 14:11:13,441 INFO misc.py line 117 1395871] Train: [14/100][126/1462] Data 0.001 (0.001) Batch 2.755 (2.867) Remain 101:11:26 loss: 0.4871 Lr: 0.00200
[2025-09-06 14:11:16,233 INFO misc.py line 117 1395871] Train: [14/100][127/1462] Data 0.002 (0.001) Batch 2.792 (2.866) Remain 101:10:06 loss: 0.3971 Lr: 0.00200
[2025-09-06 14:11:19,056 INFO misc.py line 117 1395871] Train: [14/100][128/1462] Data 0.001 (0.001) Batch 2.822 (2.866) Remain 101:09:19 loss: 0.3588 Lr: 0.00200
[2025-09-06 14:11:21,663 INFO misc.py line 117 1395871] Train: [14/100][129/1462] Data 0.001 (0.001) Batch 2.607 (2.864) Remain 101:04:55 loss: 0.4848 Lr: 0.00200
[2025-09-06 14:11:24,252 INFO misc.py line 117 1395871] Train: [14/100][130/1462] Data 0.001 (0.001) Batch 2.589 (2.862) Remain 101:00:17 loss: 0.3708 Lr: 0.00200
[2025-09-06 14:11:27,414 INFO misc.py line 117 1395871] Train: [14/100][131/1462] Data 0.002 (0.001) Batch 3.162 (2.864) Remain 101:05:13 loss: 0.7080 Lr: 0.00200
[2025-09-06 14:11:30,373 INFO misc.py line 117 1395871] Train: [14/100][132/1462] Data 0.001 (0.001) Batch 2.959 (2.865) Remain 101:06:44 loss: 0.5775 Lr: 0.00200
[2025-09-06 14:11:33,269 INFO misc.py line 117 1395871] Train: [14/100][133/1462] Data 0.001 (0.001) Batch 2.896 (2.865) Remain 101:07:11 loss: 0.3658 Lr: 0.00200
[2025-09-06 14:11:36,396 INFO misc.py line 117 1395871] Train: [14/100][134/1462] Data 0.001 (0.001) Batch 3.127 (2.867) Remain 101:11:23 loss: 0.4864 Lr: 0.00200
[2025-09-06 14:11:39,913 INFO misc.py line 117 1395871] Train: [14/100][135/1462] Data 0.001 (0.001) Batch 3.516 (2.872) Remain 101:21:45 loss: 0.5311 Lr: 0.00200
[2025-09-06 14:11:42,693 INFO misc.py line 117 1395871] Train: [14/100][136/1462] Data 0.002 (0.001) Batch 2.780 (2.871) Remain 101:20:15 loss: 0.5057 Lr: 0.00200
[2025-09-06 14:11:45,263 INFO misc.py line 117 1395871] Train: [14/100][137/1462] Data 0.001 (0.001) Batch 2.570 (2.869) Remain 101:15:26 loss: 0.6523 Lr: 0.00200
[2025-09-06 14:11:48,159 INFO misc.py line 117 1395871] Train: [14/100][138/1462] Data 0.001 (0.001) Batch 2.895 (2.869) Remain 101:15:48 loss: 0.1985 Lr: 0.00200
[2025-09-06 14:11:51,029 INFO misc.py line 117 1395871] Train: [14/100][139/1462] Data 0.001 (0.001) Batch 2.870 (2.869) Remain 101:15:46 loss: 0.3022 Lr: 0.00200
[2025-09-06 14:11:53,778 INFO misc.py line 117 1395871] Train: [14/100][140/1462] Data 0.001 (0.001) Batch 2.750 (2.868) Remain 101:13:52 loss: 0.5207 Lr: 0.00200
[2025-09-06 14:11:56,474 INFO misc.py line 117 1395871] Train: [14/100][141/1462] Data 0.001 (0.001) Batch 2.696 (2.867) Remain 101:11:10 loss: 0.3188 Lr: 0.00200
[2025-09-06 14:11:59,231 INFO misc.py line 117 1395871] Train: [14/100][142/1462] Data 0.001 (0.001) Batch 2.757 (2.866) Remain 101:09:26 loss: 0.6395 Lr: 0.00200
[2025-09-06 14:12:01,903 INFO misc.py line 117 1395871] Train: [14/100][143/1462] Data 0.001 (0.001) Batch 2.672 (2.865) Remain 101:06:27 loss: 0.4247 Lr: 0.00200
[2025-09-06 14:12:04,578 INFO misc.py line 117 1395871] Train: [14/100][144/1462] Data 0.001 (0.001) Batch 2.676 (2.864) Remain 101:03:34 loss: 0.5157 Lr: 0.00200
[2025-09-06 14:12:07,258 INFO misc.py line 117 1395871] Train: [14/100][145/1462] Data 0.002 (0.001) Batch 2.680 (2.862) Remain 101:00:47 loss: 0.2823 Lr: 0.00200
[2025-09-06 14:12:10,303 INFO misc.py line 117 1395871] Train: [14/100][146/1462] Data 0.001 (0.001) Batch 3.045 (2.864) Remain 101:03:26 loss: 0.3697 Lr: 0.00200
[2025-09-06 14:12:13,027 INFO misc.py line 117 1395871] Train: [14/100][147/1462] Data 0.001 (0.001) Batch 2.723 (2.863) Remain 101:01:20 loss: 0.7776 Lr: 0.00200
[2025-09-06 14:12:15,619 INFO misc.py line 117 1395871] Train: [14/100][148/1462] Data 0.001 (0.001) Batch 2.593 (2.861) Remain 100:57:20 loss: 0.4006 Lr: 0.00200
[2025-09-06 14:12:18,587 INFO misc.py line 117 1395871] Train: [14/100][149/1462] Data 0.002 (0.001) Batch 2.967 (2.861) Remain 100:58:50 loss: 0.5367 Lr: 0.00200
[2025-09-06 14:12:21,421 INFO misc.py line 117 1395871] Train: [14/100][150/1462] Data 0.001 (0.001) Batch 2.834 (2.861) Remain 100:58:24 loss: 0.6358 Lr: 0.00200
[2025-09-06 14:12:24,290 INFO misc.py line 117 1395871] Train: [14/100][151/1462] Data 0.001 (0.001) Batch 2.869 (2.861) Remain 100:58:28 loss: 0.3045 Lr: 0.00200
[2025-09-06 14:12:27,063 INFO misc.py line 117 1395871] Train: [14/100][152/1462] Data 0.001 (0.001) Batch 2.774 (2.861) Remain 100:57:10 loss: 0.3801 Lr: 0.00200
[2025-09-06 14:12:29,786 INFO misc.py line 117 1395871] Train: [14/100][153/1462] Data 0.001 (0.001) Batch 2.723 (2.860) Remain 100:55:10 loss: 0.8015 Lr: 0.00200
[2025-09-06 14:12:32,655 INFO misc.py line 117 1395871] Train: [14/100][154/1462] Data 0.001 (0.001) Batch 2.869 (2.860) Remain 100:55:15 loss: 0.2309 Lr: 0.00200
[2025-09-06 14:12:35,621 INFO misc.py line 117 1395871] Train: [14/100][155/1462] Data 0.001 (0.001) Batch 2.966 (2.861) Remain 100:56:41 loss: 0.2229 Lr: 0.00200
[2025-09-06 14:12:38,286 INFO misc.py line 117 1395871] Train: [14/100][156/1462] Data 0.001 (0.001) Batch 2.665 (2.859) Remain 100:53:55 loss: 0.4896 Lr: 0.00200
[2025-09-06 14:12:41,310 INFO misc.py line 117 1395871] Train: [14/100][157/1462] Data 0.001 (0.001) Batch 3.024 (2.860) Remain 100:56:09 loss: 0.2593 Lr: 0.00200
[2025-09-06 14:12:44,241 INFO misc.py line 117 1395871] Train: [14/100][158/1462] Data 0.002 (0.001) Batch 2.931 (2.861) Remain 100:57:04 loss: 0.5278 Lr: 0.00200
[2025-09-06 14:12:47,529 INFO misc.py line 117 1395871] Train: [14/100][159/1462] Data 0.001 (0.001) Batch 3.288 (2.864) Remain 101:02:49 loss: 0.3805 Lr: 0.00200
[2025-09-06 14:12:50,507 INFO misc.py line 117 1395871] Train: [14/100][160/1462] Data 0.001 (0.001) Batch 2.978 (2.864) Remain 101:04:19 loss: 0.3155 Lr: 0.00200
[2025-09-06 14:12:53,243 INFO misc.py line 117 1395871] Train: [14/100][161/1462] Data 0.001 (0.001) Batch 2.736 (2.863) Remain 101:02:32 loss: 0.3076 Lr: 0.00200
[2025-09-06 14:12:55,920 INFO misc.py line 117 1395871] Train: [14/100][162/1462] Data 0.001 (0.001) Batch 2.677 (2.862) Remain 101:00:01 loss: 0.5209 Lr: 0.00200
[2025-09-06 14:12:59,198 INFO misc.py line 117 1395871] Train: [14/100][163/1462] Data 0.001 (0.001) Batch 3.278 (2.865) Remain 101:05:28 loss: 0.2904 Lr: 0.00200
[2025-09-06 14:13:02,153 INFO misc.py line 117 1395871] Train: [14/100][164/1462] Data 0.001 (0.001) Batch 2.955 (2.865) Remain 101:06:37 loss: 0.3489 Lr: 0.00200
[2025-09-06 14:13:05,503 INFO misc.py line 117 1395871] Train: [14/100][165/1462] Data 0.001 (0.001) Batch 3.349 (2.868) Remain 101:12:53 loss: 0.4776 Lr: 0.00200
[2025-09-06 14:13:09,129 INFO misc.py line 117 1395871] Train: [14/100][166/1462] Data 0.001 (0.001) Batch 3.627 (2.873) Remain 101:22:41 loss: 0.2976 Lr: 0.00200
[2025-09-06 14:13:12,146 INFO misc.py line 117 1395871] Train: [14/100][167/1462] Data 0.001 (0.001) Batch 3.017 (2.874) Remain 101:24:30 loss: 0.3780 Lr: 0.00200
[2025-09-06 14:13:15,032 INFO misc.py line 117 1395871] Train: [14/100][168/1462] Data 0.001 (0.001) Batch 2.886 (2.874) Remain 101:24:36 loss: 0.3539 Lr: 0.00200
[2025-09-06 14:13:17,945 INFO misc.py line 117 1395871] Train: [14/100][169/1462] Data 0.001 (0.001) Batch 2.913 (2.874) Remain 101:25:03 loss: 0.2826 Lr: 0.00200
[2025-09-06 14:13:20,671 INFO misc.py line 117 1395871] Train: [14/100][170/1462] Data 0.001 (0.001) Batch 2.726 (2.873) Remain 101:23:07 loss: 0.3983 Lr: 0.00200
[2025-09-06 14:13:23,602 INFO misc.py line 117 1395871] Train: [14/100][171/1462] Data 0.001 (0.001) Batch 2.931 (2.874) Remain 101:23:48 loss: 0.3970 Lr: 0.00200
[2025-09-06 14:13:26,551 INFO misc.py line 117 1395871] Train: [14/100][172/1462] Data 0.001 (0.001) Batch 2.949 (2.874) Remain 101:24:42 loss: 0.5000 Lr: 0.00200
[2025-09-06 14:13:29,656 INFO misc.py line 117 1395871] Train: [14/100][173/1462] Data 0.001 (0.001) Batch 3.105 (2.876) Remain 101:27:31 loss: 0.6674 Lr: 0.00200
[2025-09-06 14:13:32,866 INFO misc.py line 117 1395871] Train: [14/100][174/1462] Data 0.001 (0.001) Batch 3.210 (2.877) Remain 101:31:37 loss: 0.3840 Lr: 0.00200
[2025-09-06 14:13:35,612 INFO misc.py line 117 1395871] Train: [14/100][175/1462] Data 0.001 (0.001) Batch 2.746 (2.877) Remain 101:29:57 loss: 0.3016 Lr: 0.00200
[2025-09-06 14:13:38,329 INFO misc.py line 117 1395871] Train: [14/100][176/1462] Data 0.001 (0.001) Batch 2.717 (2.876) Remain 101:27:57 loss: 0.4762 Lr: 0.00200
[2025-09-06 14:13:41,417 INFO misc.py line 117 1395871] Train: [14/100][177/1462] Data 0.001 (0.001) Batch 3.088 (2.877) Remain 101:30:29 loss: 0.4175 Lr: 0.00200
[2025-09-06 14:13:44,280 INFO misc.py line 117 1395871] Train: [14/100][178/1462] Data 0.001 (0.001) Batch 2.863 (2.877) Remain 101:30:16 loss: 0.2369 Lr: 0.00200
[2025-09-06 14:13:47,384 INFO misc.py line 117 1395871] Train: [14/100][179/1462] Data 0.001 (0.001) Batch 3.103 (2.878) Remain 101:32:56 loss: 0.6076 Lr: 0.00200
[2025-09-06 14:13:50,072 INFO misc.py line 117 1395871] Train: [14/100][180/1462] Data 0.002 (0.001) Batch 2.689 (2.877) Remain 101:30:37 loss: 0.3404 Lr: 0.00200
[2025-09-06 14:13:53,024 INFO misc.py line 117 1395871] Train: [14/100][181/1462] Data 0.002 (0.001) Batch 2.952 (2.878) Remain 101:31:27 loss: 0.3313 Lr: 0.00200
[2025-09-06 14:13:56,062 INFO misc.py line 117 1395871] Train: [14/100][182/1462] Data 0.001 (0.001) Batch 3.038 (2.878) Remain 101:33:18 loss: 0.4512 Lr: 0.00200
[2025-09-06 14:13:58,567 INFO misc.py line 117 1395871] Train: [14/100][183/1462] Data 0.002 (0.001) Batch 2.506 (2.876) Remain 101:28:52 loss: 0.6419 Lr: 0.00200
[2025-09-06 14:14:01,246 INFO misc.py line 117 1395871] Train: [14/100][184/1462] Data 0.002 (0.001) Batch 2.679 (2.875) Remain 101:26:31 loss: 0.4679 Lr: 0.00200
[2025-09-06 14:14:04,335 INFO misc.py line 117 1395871] Train: [14/100][185/1462] Data 0.001 (0.001) Batch 3.089 (2.876) Remain 101:28:57 loss: 0.3022 Lr: 0.00200
[2025-09-06 14:14:07,379 INFO misc.py line 117 1395871] Train: [14/100][186/1462] Data 0.002 (0.001) Batch 3.044 (2.877) Remain 101:30:51 loss: 0.3887 Lr: 0.00200
[2025-09-06 14:14:09,941 INFO misc.py line 117 1395871] Train: [14/100][187/1462] Data 0.002 (0.001) Batch 2.562 (2.876) Remain 101:27:10 loss: 0.5963 Lr: 0.00200
[2025-09-06 14:14:13,054 INFO misc.py line 117 1395871] Train: [14/100][188/1462] Data 0.001 (0.001) Batch 3.113 (2.877) Remain 101:29:50 loss: 0.2946 Lr: 0.00200
[2025-09-06 14:14:16,123 INFO misc.py line 117 1395871] Train: [14/100][189/1462] Data 0.002 (0.001) Batch 3.068 (2.878) Remain 101:31:58 loss: 0.4463 Lr: 0.00200
[2025-09-06 14:14:18,753 INFO misc.py line 117 1395871] Train: [14/100][190/1462] Data 0.001 (0.001) Batch 2.631 (2.877) Remain 101:29:07 loss: 0.3661 Lr: 0.00200
[2025-09-06 14:14:21,562 INFO misc.py line 117 1395871] Train: [14/100][191/1462] Data 0.001 (0.001) Batch 2.808 (2.876) Remain 101:28:18 loss: 0.3072 Lr: 0.00200
[2025-09-06 14:14:24,333 INFO misc.py line 117 1395871] Train: [14/100][192/1462] Data 0.001 (0.001) Batch 2.771 (2.876) Remain 101:27:04 loss: 0.3874 Lr: 0.00200
[2025-09-06 14:14:27,270 INFO misc.py line 117 1395871] Train: [14/100][193/1462] Data 0.002 (0.001) Batch 2.938 (2.876) Remain 101:27:43 loss: 0.2967 Lr: 0.00200
[2025-09-06 14:14:30,288 INFO misc.py line 117 1395871] Train: [14/100][194/1462] Data 0.002 (0.001) Batch 3.018 (2.877) Remain 101:29:14 loss: 0.4745 Lr: 0.00200
[2025-09-06 14:14:33,243 INFO misc.py line 117 1395871] Train: [14/100][195/1462] Data 0.001 (0.001) Batch 2.955 (2.877) Remain 101:30:03 loss: 0.5000 Lr: 0.00200
[2025-09-06 14:14:36,008 INFO misc.py line 117 1395871] Train: [14/100][196/1462] Data 0.001 (0.001) Batch 2.764 (2.877) Remain 101:28:46 loss: 0.4526 Lr: 0.00200
[2025-09-06 14:14:39,006 INFO misc.py line 117 1395871] Train: [14/100][197/1462] Data 0.002 (0.001) Batch 2.998 (2.877) Remain 101:30:03 loss: 0.4497 Lr: 0.00200
[2025-09-06 14:14:41,741 INFO misc.py line 117 1395871] Train: [14/100][198/1462] Data 0.001 (0.001) Batch 2.735 (2.877) Remain 101:28:27 loss: 0.4971 Lr: 0.00200
[2025-09-06 14:14:44,549 INFO misc.py line 117 1395871] Train: [14/100][199/1462] Data 0.002 (0.001) Batch 2.808 (2.876) Remain 101:27:40 loss: 0.2942 Lr: 0.00200
[2025-09-06 14:14:47,683 INFO misc.py line 117 1395871] Train: [14/100][200/1462] Data 0.001 (0.001) Batch 3.134 (2.877) Remain 101:30:23 loss: 0.6307 Lr: 0.00200
[2025-09-06 14:14:50,391 INFO misc.py line 117 1395871] Train: [14/100][201/1462] Data 0.001 (0.001) Batch 2.708 (2.877) Remain 101:28:32 loss: 1.0852 Lr: 0.00200
[2025-09-06 14:14:53,261 INFO misc.py line 117 1395871] Train: [14/100][202/1462] Data 0.002 (0.001) Batch 2.871 (2.877) Remain 101:28:25 loss: 0.5734 Lr: 0.00200
[2025-09-06 14:14:56,034 INFO misc.py line 117 1395871] Train: [14/100][203/1462] Data 0.001 (0.001) Batch 2.773 (2.876) Remain 101:27:16 loss: 0.2650 Lr: 0.00200
[2025-09-06 14:14:58,880 INFO misc.py line 117 1395871] Train: [14/100][204/1462] Data 0.001 (0.001) Batch 2.846 (2.876) Remain 101:26:54 loss: 0.8309 Lr: 0.00200
[2025-09-06 14:15:02,084 INFO misc.py line 117 1395871] Train: [14/100][205/1462] Data 0.001 (0.001) Batch 3.204 (2.878) Remain 101:30:17 loss: 0.4498 Lr: 0.00200
[2025-09-06 14:15:04,872 INFO misc.py line 117 1395871] Train: [14/100][206/1462] Data 0.002 (0.001) Batch 2.788 (2.877) Remain 101:29:18 loss: 0.3220 Lr: 0.00200
[2025-09-06 14:15:07,752 INFO misc.py line 117 1395871] Train: [14/100][207/1462] Data 0.001 (0.001) Batch 2.880 (2.877) Remain 101:29:17 loss: 0.4698 Lr: 0.00200
[2025-09-06 14:15:10,643 INFO misc.py line 117 1395871] Train: [14/100][208/1462] Data 0.001 (0.001) Batch 2.892 (2.877) Remain 101:29:23 loss: 0.3478 Lr: 0.00200
[2025-09-06 14:15:13,287 INFO misc.py line 117 1395871] Train: [14/100][209/1462] Data 0.001 (0.001) Batch 2.643 (2.876) Remain 101:26:56 loss: 0.5381 Lr: 0.00200
[2025-09-06 14:15:16,047 INFO misc.py line 117 1395871] Train: [14/100][210/1462] Data 0.001 (0.001) Batch 2.761 (2.876) Remain 101:25:43 loss: 0.4592 Lr: 0.00200
[2025-09-06 14:15:18,647 INFO misc.py line 117 1395871] Train: [14/100][211/1462] Data 0.001 (0.001) Batch 2.599 (2.874) Remain 101:22:51 loss: 0.2723 Lr: 0.00200
[2025-09-06 14:15:21,577 INFO misc.py line 117 1395871] Train: [14/100][212/1462] Data 0.001 (0.001) Batch 2.930 (2.874) Remain 101:23:22 loss: 0.5962 Lr: 0.00200
[2025-09-06 14:15:24,307 INFO misc.py line 117 1395871] Train: [14/100][213/1462] Data 0.001 (0.001) Batch 2.730 (2.874) Remain 101:21:52 loss: 0.5817 Lr: 0.00200
[2025-09-06 14:15:27,284 INFO misc.py line 117 1395871] Train: [14/100][214/1462] Data 0.001 (0.001) Batch 2.978 (2.874) Remain 101:22:52 loss: 0.3398 Lr: 0.00200
[2025-09-06 14:15:29,865 INFO misc.py line 117 1395871] Train: [14/100][215/1462] Data 0.002 (0.001) Batch 2.580 (2.873) Remain 101:19:53 loss: 0.2828 Lr: 0.00200
[2025-09-06 14:15:32,756 INFO misc.py line 117 1395871] Train: [14/100][216/1462] Data 0.001 (0.001) Batch 2.891 (2.873) Remain 101:20:01 loss: 0.4736 Lr: 0.00200
[2025-09-06 14:15:35,378 INFO misc.py line 117 1395871] Train: [14/100][217/1462] Data 0.001 (0.001) Batch 2.622 (2.872) Remain 101:17:29 loss: 0.3115 Lr: 0.00200
[2025-09-06 14:15:38,178 INFO misc.py line 117 1395871] Train: [14/100][218/1462] Data 0.001 (0.001) Batch 2.800 (2.871) Remain 101:16:44 loss: 0.8070 Lr: 0.00200
[2025-09-06 14:15:41,061 INFO misc.py line 117 1395871] Train: [14/100][219/1462] Data 0.001 (0.001) Batch 2.883 (2.871) Remain 101:16:48 loss: 0.3029 Lr: 0.00200
[2025-09-06 14:15:44,004 INFO misc.py line 117 1395871] Train: [14/100][220/1462] Data 0.001 (0.001) Batch 2.943 (2.872) Remain 101:17:27 loss: 0.4154 Lr: 0.00200
[2025-09-06 14:15:46,751 INFO misc.py line 117 1395871] Train: [14/100][221/1462] Data 0.002 (0.001) Batch 2.747 (2.871) Remain 101:16:12 loss: 0.3287 Lr: 0.00200
[2025-09-06 14:15:50,022 INFO misc.py line 117 1395871] Train: [14/100][222/1462] Data 0.001 (0.001) Batch 3.271 (2.873) Remain 101:20:00 loss: 0.3101 Lr: 0.00200
[2025-09-06 14:15:52,867 INFO misc.py line 117 1395871] Train: [14/100][223/1462] Data 0.001 (0.001) Batch 2.845 (2.873) Remain 101:19:41 loss: 0.6195 Lr: 0.00200
[2025-09-06 14:15:55,328 INFO misc.py line 117 1395871] Train: [14/100][224/1462] Data 0.001 (0.001) Batch 2.461 (2.871) Remain 101:15:42 loss: 0.6244 Lr: 0.00200
[2025-09-06 14:15:58,109 INFO misc.py line 117 1395871] Train: [14/100][225/1462] Data 0.002 (0.001) Batch 2.781 (2.871) Remain 101:14:47 loss: 0.4329 Lr: 0.00200
[2025-09-06 14:16:00,995 INFO misc.py line 117 1395871] Train: [14/100][226/1462] Data 0.002 (0.001) Batch 2.886 (2.871) Remain 101:14:53 loss: 0.5005 Lr: 0.00200
[2025-09-06 14:16:03,731 INFO misc.py line 117 1395871] Train: [14/100][227/1462] Data 0.002 (0.001) Batch 2.735 (2.870) Remain 101:13:33 loss: 0.2802 Lr: 0.00200
[2025-09-06 14:16:07,090 INFO misc.py line 117 1395871] Train: [14/100][228/1462] Data 0.001 (0.001) Batch 3.360 (2.872) Remain 101:18:07 loss: 0.2716 Lr: 0.00200
[2025-09-06 14:16:09,983 INFO misc.py line 117 1395871] Train: [14/100][229/1462] Data 0.002 (0.001) Batch 2.893 (2.872) Remain 101:18:16 loss: 0.2620 Lr: 0.00200
[2025-09-06 14:16:13,078 INFO misc.py line 117 1395871] Train: [14/100][230/1462] Data 0.002 (0.001) Batch 3.095 (2.873) Remain 101:20:17 loss: 0.7073 Lr: 0.00200
[2025-09-06 14:16:15,884 INFO misc.py line 117 1395871] Train: [14/100][231/1462] Data 0.002 (0.001) Batch 2.806 (2.873) Remain 101:19:37 loss: 0.4481 Lr: 0.00200
[2025-09-06 14:16:18,955 INFO misc.py line 117 1395871] Train: [14/100][232/1462] Data 0.002 (0.001) Batch 3.071 (2.874) Remain 101:21:24 loss: 0.6326 Lr: 0.00200
[2025-09-06 14:16:21,820 INFO misc.py line 117 1395871] Train: [14/100][233/1462] Data 0.001 (0.001) Batch 2.865 (2.874) Remain 101:21:16 loss: 0.3006 Lr: 0.00200
[2025-09-06 14:16:24,935 INFO misc.py line 117 1395871] Train: [14/100][234/1462] Data 0.002 (0.001) Batch 3.115 (2.875) Remain 101:23:25 loss: 0.4710 Lr: 0.00200
[2025-09-06 14:16:28,122 INFO misc.py line 117 1395871] Train: [14/100][235/1462] Data 0.001 (0.001) Batch 3.187 (2.876) Remain 101:26:13 loss: 0.2674 Lr: 0.00200
[2025-09-06 14:16:31,021 INFO misc.py line 117 1395871] Train: [14/100][236/1462] Data 0.001 (0.001) Batch 2.899 (2.876) Remain 101:26:23 loss: 0.3719 Lr: 0.00200
[2025-09-06 14:16:34,188 INFO misc.py line 117 1395871] Train: [14/100][237/1462] Data 0.001 (0.001) Batch 3.166 (2.878) Remain 101:28:57 loss: 0.2998 Lr: 0.00200
[2025-09-06 14:16:37,277 INFO misc.py line 117 1395871] Train: [14/100][238/1462] Data 0.002 (0.001) Batch 3.090 (2.879) Remain 101:30:49 loss: 0.3632 Lr: 0.00200
[2025-09-06 14:16:40,082 INFO misc.py line 117 1395871] Train: [14/100][239/1462] Data 0.001 (0.001) Batch 2.805 (2.878) Remain 101:30:06 loss: 0.5244 Lr: 0.00200
[2025-09-06 14:16:42,970 INFO misc.py line 117 1395871] Train: [14/100][240/1462] Data 0.001 (0.001) Batch 2.888 (2.878) Remain 101:30:09 loss: 0.2146 Lr: 0.00200
[2025-09-06 14:16:46,072 INFO misc.py line 117 1395871] Train: [14/100][241/1462] Data 0.001 (0.001) Batch 3.102 (2.879) Remain 101:32:05 loss: 0.3168 Lr: 0.00200
[2025-09-06 14:16:48,877 INFO misc.py line 117 1395871] Train: [14/100][242/1462] Data 0.001 (0.001) Batch 2.805 (2.879) Remain 101:31:23 loss: 0.6488 Lr: 0.00200
[2025-09-06 14:16:51,817 INFO misc.py line 117 1395871] Train: [14/100][243/1462] Data 0.001 (0.001) Batch 2.939 (2.879) Remain 101:31:52 loss: 0.3990 Lr: 0.00200
[2025-09-06 14:16:55,245 INFO misc.py line 117 1395871] Train: [14/100][244/1462] Data 0.001 (0.001) Batch 3.429 (2.881) Remain 101:36:39 loss: 0.5213 Lr: 0.00200
[2025-09-06 14:16:58,191 INFO misc.py line 117 1395871] Train: [14/100][245/1462] Data 0.001 (0.001) Batch 2.946 (2.882) Remain 101:37:10 loss: 0.5052 Lr: 0.00200
[2025-09-06 14:17:01,014 INFO misc.py line 117 1395871] Train: [14/100][246/1462] Data 0.002 (0.001) Batch 2.823 (2.881) Remain 101:36:36 loss: 0.3650 Lr: 0.00200
[2025-09-06 14:17:03,738 INFO misc.py line 117 1395871] Train: [14/100][247/1462] Data 0.001 (0.001) Batch 2.724 (2.881) Remain 101:35:11 loss: 0.3642 Lr: 0.00200
[2025-09-06 14:17:06,829 INFO misc.py line 117 1395871] Train: [14/100][248/1462] Data 0.001 (0.001) Batch 3.091 (2.882) Remain 101:36:57 loss: 0.4015 Lr: 0.00200
[2025-09-06 14:17:09,909 INFO misc.py line 117 1395871] Train: [14/100][249/1462] Data 0.001 (0.001) Batch 3.080 (2.882) Remain 101:38:36 loss: 0.3145 Lr: 0.00200
[2025-09-06 14:17:13,053 INFO misc.py line 117 1395871] Train: [14/100][250/1462] Data 0.002 (0.001) Batch 3.144 (2.884) Remain 101:40:48 loss: 0.4878 Lr: 0.00200
[2025-09-06 14:17:15,693 INFO misc.py line 117 1395871] Train: [14/100][251/1462] Data 0.001 (0.001) Batch 2.640 (2.883) Remain 101:38:41 loss: 0.2219 Lr: 0.00200
[2025-09-06 14:17:18,919 INFO misc.py line 117 1395871] Train: [14/100][252/1462] Data 0.001 (0.001) Batch 3.226 (2.884) Remain 101:41:33 loss: 0.2945 Lr: 0.00200
[2025-09-06 14:17:21,801 INFO misc.py line 117 1395871] Train: [14/100][253/1462] Data 0.001 (0.001) Batch 2.882 (2.884) Remain 101:41:29 loss: 0.4924 Lr: 0.00200
[2025-09-06 14:17:25,022 INFO misc.py line 117 1395871] Train: [14/100][254/1462] Data 0.001 (0.001) Batch 3.221 (2.885) Remain 101:44:17 loss: 0.4288 Lr: 0.00200
[2025-09-06 14:17:27,891 INFO misc.py line 117 1395871] Train: [14/100][255/1462] Data 0.001 (0.001) Batch 2.869 (2.885) Remain 101:44:06 loss: 0.3139 Lr: 0.00200
[2025-09-06 14:17:30,429 INFO misc.py line 117 1395871] Train: [14/100][256/1462] Data 0.002 (0.001) Batch 2.538 (2.884) Remain 101:41:09 loss: 0.5239 Lr: 0.00200
[2025-09-06 14:17:33,393 INFO misc.py line 117 1395871] Train: [14/100][257/1462] Data 0.001 (0.001) Batch 2.964 (2.884) Remain 101:41:46 loss: 0.4528 Lr: 0.00200
[2025-09-06 14:17:35,993 INFO misc.py line 117 1395871] Train: [14/100][258/1462] Data 0.002 (0.001) Batch 2.599 (2.883) Remain 101:39:21 loss: 0.3482 Lr: 0.00200
[2025-09-06 14:17:39,125 INFO misc.py line 117 1395871] Train: [14/100][259/1462] Data 0.002 (0.001) Batch 3.132 (2.884) Remain 101:41:22 loss: 0.6783 Lr: 0.00200
[2025-09-06 14:17:41,860 INFO misc.py line 117 1395871] Train: [14/100][260/1462] Data 0.002 (0.001) Batch 2.735 (2.883) Remain 101:40:05 loss: 0.3572 Lr: 0.00200
[2025-09-06 14:17:44,546 INFO misc.py line 117 1395871] Train: [14/100][261/1462] Data 0.001 (0.001) Batch 2.687 (2.883) Remain 101:38:25 loss: 0.4173 Lr: 0.00200
[2025-09-06 14:17:47,011 INFO misc.py line 117 1395871] Train: [14/100][262/1462] Data 0.001 (0.001) Batch 2.464 (2.881) Remain 101:34:57 loss: 0.5060 Lr: 0.00200
[2025-09-06 14:17:49,894 INFO misc.py line 117 1395871] Train: [14/100][263/1462] Data 0.001 (0.001) Batch 2.883 (2.881) Remain 101:34:56 loss: 0.4563 Lr: 0.00200
[2025-09-06 14:17:52,881 INFO misc.py line 117 1395871] Train: [14/100][264/1462] Data 0.002 (0.001) Batch 2.987 (2.881) Remain 101:35:44 loss: 0.5390 Lr: 0.00200
[2025-09-06 14:17:55,552 INFO misc.py line 117 1395871] Train: [14/100][265/1462] Data 0.001 (0.001) Batch 2.671 (2.881) Remain 101:33:59 loss: 0.2962 Lr: 0.00200
[2025-09-06 14:17:58,685 INFO misc.py line 117 1395871] Train: [14/100][266/1462] Data 0.001 (0.001) Batch 3.133 (2.882) Remain 101:35:58 loss: 0.3063 Lr: 0.00200
[2025-09-06 14:18:01,338 INFO misc.py line 117 1395871] Train: [14/100][267/1462] Data 0.001 (0.001) Batch 2.653 (2.881) Remain 101:34:06 loss: 0.4413 Lr: 0.00200
[2025-09-06 14:18:03,944 INFO misc.py line 117 1395871] Train: [14/100][268/1462] Data 0.001 (0.001) Batch 2.606 (2.880) Remain 101:31:51 loss: 0.2753 Lr: 0.00200
[2025-09-06 14:18:06,975 INFO misc.py line 117 1395871] Train: [14/100][269/1462] Data 0.001 (0.001) Batch 3.031 (2.880) Remain 101:33:00 loss: 0.3729 Lr: 0.00200
[2025-09-06 14:18:10,434 INFO misc.py line 117 1395871] Train: [14/100][270/1462] Data 0.001 (0.001) Batch 3.460 (2.882) Remain 101:37:33 loss: 0.3277 Lr: 0.00200
[2025-09-06 14:18:13,746 INFO misc.py line 117 1395871] Train: [14/100][271/1462] Data 0.002 (0.001) Batch 3.311 (2.884) Remain 101:40:53 loss: 0.3831 Lr: 0.00200
[2025-09-06 14:18:16,467 INFO misc.py line 117 1395871] Train: [14/100][272/1462] Data 0.002 (0.001) Batch 2.721 (2.883) Remain 101:39:33 loss: 0.3023 Lr: 0.00200
[2025-09-06 14:18:19,387 INFO misc.py line 117 1395871] Train: [14/100][273/1462] Data 0.002 (0.001) Batch 2.920 (2.884) Remain 101:39:48 loss: 0.3792 Lr: 0.00200
[2025-09-06 14:18:22,217 INFO misc.py line 117 1395871] Train: [14/100][274/1462] Data 0.001 (0.001) Batch 2.830 (2.883) Remain 101:39:20 loss: 0.2142 Lr: 0.00200
[2025-09-06 14:18:24,799 INFO misc.py line 117 1395871] Train: [14/100][275/1462] Data 0.001 (0.001) Batch 2.582 (2.882) Remain 101:36:56 loss: 0.4481 Lr: 0.00200
[2025-09-06 14:18:27,378 INFO misc.py line 117 1395871] Train: [14/100][276/1462] Data 0.001 (0.001) Batch 2.579 (2.881) Remain 101:34:32 loss: 0.3921 Lr: 0.00200
[2025-09-06 14:18:30,516 INFO misc.py line 117 1395871] Train: [14/100][277/1462] Data 0.002 (0.001) Batch 3.138 (2.882) Remain 101:36:28 loss: 0.3776 Lr: 0.00200
[2025-09-06 14:18:33,445 INFO misc.py line 117 1395871] Train: [14/100][278/1462] Data 0.002 (0.001) Batch 2.929 (2.882) Remain 101:36:47 loss: 1.0525 Lr: 0.00200
[2025-09-06 14:18:36,060 INFO misc.py line 117 1395871] Train: [14/100][279/1462] Data 0.002 (0.001) Batch 2.615 (2.881) Remain 101:34:41 loss: 0.8236 Lr: 0.00200
[2025-09-06 14:18:38,910 INFO misc.py line 117 1395871] Train: [14/100][280/1462] Data 0.001 (0.001) Batch 2.850 (2.881) Remain 101:34:24 loss: 0.3620 Lr: 0.00200
[2025-09-06 14:18:41,538 INFO misc.py line 117 1395871] Train: [14/100][281/1462] Data 0.001 (0.001) Batch 2.628 (2.880) Remain 101:32:25 loss: 0.3500 Lr: 0.00200
[2025-09-06 14:18:44,388 INFO misc.py line 117 1395871] Train: [14/100][282/1462] Data 0.001 (0.001) Batch 2.850 (2.880) Remain 101:32:09 loss: 0.2618 Lr: 0.00200
[2025-09-06 14:18:47,266 INFO misc.py line 117 1395871] Train: [14/100][283/1462] Data 0.001 (0.001) Batch 2.878 (2.880) Remain 101:32:05 loss: 0.4090 Lr: 0.00200
[2025-09-06 14:18:50,108 INFO misc.py line 117 1395871] Train: [14/100][284/1462] Data 0.001 (0.001) Batch 2.842 (2.880) Remain 101:31:45 loss: 0.4103 Lr: 0.00200
[2025-09-06 14:18:53,262 INFO misc.py line 117 1395871] Train: [14/100][285/1462] Data 0.002 (0.001) Batch 3.154 (2.881) Remain 101:33:45 loss: 0.1890 Lr: 0.00200
[2025-09-06 14:18:56,168 INFO misc.py line 117 1395871] Train: [14/100][286/1462] Data 0.001 (0.001) Batch 2.906 (2.881) Remain 101:33:54 loss: 0.5389 Lr: 0.00200
[2025-09-06 14:18:58,897 INFO misc.py line 117 1395871] Train: [14/100][287/1462] Data 0.001 (0.001) Batch 2.729 (2.881) Remain 101:32:43 loss: 0.3390 Lr: 0.00200
[2025-09-06 14:19:01,827 INFO misc.py line 117 1395871] Train: [14/100][288/1462] Data 0.001 (0.001) Batch 2.930 (2.881) Remain 101:33:02 loss: 0.3575 Lr: 0.00200
[2025-09-06 14:19:04,474 INFO misc.py line 117 1395871] Train: [14/100][289/1462] Data 0.001 (0.001) Batch 2.646 (2.880) Remain 101:31:15 loss: 0.3216 Lr: 0.00200
[2025-09-06 14:19:07,109 INFO misc.py line 117 1395871] Train: [14/100][290/1462] Data 0.001 (0.001) Batch 2.635 (2.879) Remain 101:29:24 loss: 0.5041 Lr: 0.00200
[2025-09-06 14:19:09,927 INFO misc.py line 117 1395871] Train: [14/100][291/1462] Data 0.002 (0.001) Batch 2.818 (2.879) Remain 101:28:54 loss: 0.5574 Lr: 0.00200
[2025-09-06 14:19:12,540 INFO misc.py line 117 1395871] Train: [14/100][292/1462] Data 0.001 (0.001) Batch 2.613 (2.878) Remain 101:26:55 loss: 0.4001 Lr: 0.00200
[2025-09-06 14:19:15,231 INFO misc.py line 117 1395871] Train: [14/100][293/1462] Data 0.001 (0.001) Batch 2.691 (2.877) Remain 101:25:30 loss: 0.4653 Lr: 0.00200
[2025-09-06 14:19:18,316 INFO misc.py line 117 1395871] Train: [14/100][294/1462] Data 0.001 (0.001) Batch 3.084 (2.878) Remain 101:26:58 loss: 0.5859 Lr: 0.00200
[2025-09-06 14:19:21,183 INFO misc.py line 117 1395871] Train: [14/100][295/1462] Data 0.002 (0.001) Batch 2.868 (2.878) Remain 101:26:50 loss: 0.8264 Lr: 0.00200
[2025-09-06 14:19:23,985 INFO misc.py line 117 1395871] Train: [14/100][296/1462] Data 0.002 (0.001) Batch 2.801 (2.878) Remain 101:26:14 loss: 0.5718 Lr: 0.00200
[2025-09-06 14:19:26,694 INFO misc.py line 117 1395871] Train: [14/100][297/1462] Data 0.001 (0.001) Batch 2.710 (2.877) Remain 101:24:59 loss: 0.7768 Lr: 0.00200
[2025-09-06 14:19:29,579 INFO misc.py line 117 1395871] Train: [14/100][298/1462] Data 0.001 (0.001) Batch 2.885 (2.877) Remain 101:24:59 loss: 0.5476 Lr: 0.00200
[2025-09-06 14:19:32,822 INFO misc.py line 117 1395871] Train: [14/100][299/1462] Data 0.001 (0.001) Batch 3.243 (2.878) Remain 101:27:33 loss: 0.6346 Lr: 0.00200
[2025-09-06 14:19:35,819 INFO misc.py line 117 1395871] Train: [14/100][300/1462] Data 0.001 (0.001) Batch 2.997 (2.879) Remain 101:28:21 loss: 0.3459 Lr: 0.00200
[2025-09-06 14:19:38,618 INFO misc.py line 117 1395871] Train: [14/100][301/1462] Data 0.001 (0.001) Batch 2.799 (2.879) Remain 101:27:44 loss: 0.3571 Lr: 0.00200
[2025-09-06 14:19:41,459 INFO misc.py line 117 1395871] Train: [14/100][302/1462] Data 0.002 (0.001) Batch 2.841 (2.878) Remain 101:27:25 loss: 0.2072 Lr: 0.00200
[2025-09-06 14:19:44,085 INFO misc.py line 117 1395871] Train: [14/100][303/1462] Data 0.002 (0.001) Batch 2.625 (2.878) Remain 101:25:35 loss: 0.4698 Lr: 0.00200
[2025-09-06 14:19:46,865 INFO misc.py line 117 1395871] Train: [14/100][304/1462] Data 0.002 (0.001) Batch 2.780 (2.877) Remain 101:24:51 loss: 0.5145 Lr: 0.00200
[2025-09-06 14:19:49,797 INFO misc.py line 117 1395871] Train: [14/100][305/1462] Data 0.002 (0.001) Batch 2.932 (2.877) Remain 101:25:12 loss: 0.4073 Lr: 0.00200
[2025-09-06 14:19:52,698 INFO misc.py line 117 1395871] Train: [14/100][306/1462] Data 0.002 (0.001) Batch 2.901 (2.877) Remain 101:25:19 loss: 0.2876 Lr: 0.00200
[2025-09-06 14:19:55,304 INFO misc.py line 117 1395871] Train: [14/100][307/1462] Data 0.002 (0.001) Batch 2.606 (2.877) Remain 101:23:22 loss: 0.5324 Lr: 0.00200
[2025-09-06 14:19:58,447 INFO misc.py line 117 1395871] Train: [14/100][308/1462] Data 0.001 (0.001) Batch 3.143 (2.877) Remain 101:25:10 loss: 0.2197 Lr: 0.00200
[2025-09-06 14:20:01,612 INFO misc.py line 117 1395871] Train: [14/100][309/1462] Data 0.001 (0.001) Batch 3.165 (2.878) Remain 101:27:07 loss: 0.4524 Lr: 0.00200
[2025-09-06 14:20:04,445 INFO misc.py line 117 1395871] Train: [14/100][310/1462] Data 0.001 (0.001) Batch 2.833 (2.878) Remain 101:26:45 loss: 0.5053 Lr: 0.00200
[2025-09-06 14:20:07,150 INFO misc.py line 117 1395871] Train: [14/100][311/1462] Data 0.002 (0.001) Batch 2.706 (2.878) Remain 101:25:31 loss: 0.3680 Lr: 0.00200
[2025-09-06 14:20:10,152 INFO misc.py line 117 1395871] Train: [14/100][312/1462] Data 0.002 (0.001) Batch 3.002 (2.878) Remain 101:26:19 loss: 0.6512 Lr: 0.00200
[2025-09-06 14:20:13,156 INFO misc.py line 117 1395871] Train: [14/100][313/1462] Data 0.002 (0.001) Batch 3.003 (2.879) Remain 101:27:08 loss: 0.3516 Lr: 0.00200
[2025-09-06 14:20:15,740 INFO misc.py line 117 1395871] Train: [14/100][314/1462] Data 0.002 (0.001) Batch 2.585 (2.878) Remain 101:25:05 loss: 0.4722 Lr: 0.00200
[2025-09-06 14:20:18,867 INFO misc.py line 117 1395871] Train: [14/100][315/1462] Data 0.002 (0.001) Batch 3.126 (2.878) Remain 101:26:43 loss: 0.3673 Lr: 0.00200
[2025-09-06 14:20:21,527 INFO misc.py line 117 1395871] Train: [14/100][316/1462] Data 0.002 (0.001) Batch 2.660 (2.878) Remain 101:25:12 loss: 0.4419 Lr: 0.00200
[2025-09-06 14:20:24,416 INFO misc.py line 117 1395871] Train: [14/100][317/1462] Data 0.001 (0.001) Batch 2.889 (2.878) Remain 101:25:13 loss: 0.2314 Lr: 0.00200
[2025-09-06 14:20:27,065 INFO misc.py line 117 1395871] Train: [14/100][318/1462] Data 0.001 (0.001) Batch 2.649 (2.877) Remain 101:23:39 loss: 0.3231 Lr: 0.00200
[2025-09-06 14:20:29,787 INFO misc.py line 117 1395871] Train: [14/100][319/1462] Data 0.001 (0.001) Batch 2.722 (2.876) Remain 101:22:33 loss: 0.6125 Lr: 0.00200
[2025-09-06 14:20:32,645 INFO misc.py line 117 1395871] Train: [14/100][320/1462] Data 0.002 (0.001) Batch 2.859 (2.876) Remain 101:22:23 loss: 0.4897 Lr: 0.00200
[2025-09-06 14:20:35,591 INFO misc.py line 117 1395871] Train: [14/100][321/1462] Data 0.001 (0.001) Batch 2.945 (2.877) Remain 101:22:48 loss: 0.7675 Lr: 0.00200
[2025-09-06 14:20:38,309 INFO misc.py line 117 1395871] Train: [14/100][322/1462] Data 0.001 (0.001) Batch 2.718 (2.876) Remain 101:21:42 loss: 0.3552 Lr: 0.00200
[2025-09-06 14:20:41,636 INFO misc.py line 117 1395871] Train: [14/100][323/1462] Data 0.002 (0.001) Batch 3.327 (2.878) Remain 101:24:38 loss: 0.5519 Lr: 0.00200
[2025-09-06 14:20:44,437 INFO misc.py line 117 1395871] Train: [14/100][324/1462] Data 0.002 (0.001) Batch 2.801 (2.877) Remain 101:24:05 loss: 0.4262 Lr: 0.00200
[2025-09-06 14:20:47,132 INFO misc.py line 117 1395871] Train: [14/100][325/1462] Data 0.002 (0.001) Batch 2.695 (2.877) Remain 101:22:50 loss: 0.4532 Lr: 0.00200
[2025-09-06 14:20:50,324 INFO misc.py line 117 1395871] Train: [14/100][326/1462] Data 0.002 (0.001) Batch 3.192 (2.878) Remain 101:24:51 loss: 0.4364 Lr: 0.00200
[2025-09-06 14:20:53,466 INFO misc.py line 117 1395871] Train: [14/100][327/1462] Data 0.002 (0.001) Batch 3.143 (2.879) Remain 101:26:32 loss: 0.4211 Lr: 0.00200
[2025-09-06 14:20:56,439 INFO misc.py line 117 1395871] Train: [14/100][328/1462] Data 0.002 (0.001) Batch 2.972 (2.879) Remain 101:27:06 loss: 0.3842 Lr: 0.00200
[2025-09-06 14:20:59,262 INFO misc.py line 117 1395871] Train: [14/100][329/1462] Data 0.002 (0.001) Batch 2.824 (2.879) Remain 101:26:41 loss: 0.3931 Lr: 0.00200
[2025-09-06 14:21:01,842 INFO misc.py line 117 1395871] Train: [14/100][330/1462] Data 0.001 (0.001) Batch 2.580 (2.878) Remain 101:24:42 loss: 0.3209 Lr: 0.00200
[2025-09-06 14:21:04,705 INFO misc.py line 117 1395871] Train: [14/100][331/1462] Data 0.002 (0.001) Batch 2.863 (2.878) Remain 101:24:34 loss: 0.5872 Lr: 0.00200
[2025-09-06 14:21:07,690 INFO misc.py line 117 1395871] Train: [14/100][332/1462] Data 0.002 (0.001) Batch 2.985 (2.878) Remain 101:25:12 loss: 0.6183 Lr: 0.00200
[2025-09-06 14:21:10,648 INFO misc.py line 117 1395871] Train: [14/100][333/1462] Data 0.001 (0.001) Batch 2.958 (2.878) Remain 101:25:40 loss: 0.4692 Lr: 0.00200
[2025-09-06 14:21:13,172 INFO misc.py line 117 1395871] Train: [14/100][334/1462] Data 0.002 (0.001) Batch 2.523 (2.877) Remain 101:23:21 loss: 0.2141 Lr: 0.00200
[2025-09-06 14:21:16,235 INFO misc.py line 117 1395871] Train: [14/100][335/1462] Data 0.002 (0.001) Batch 3.063 (2.878) Remain 101:24:30 loss: 0.1413 Lr: 0.00200
[2025-09-06 14:21:19,186 INFO misc.py line 117 1395871] Train: [14/100][336/1462] Data 0.001 (0.001) Batch 2.951 (2.878) Remain 101:24:55 loss: 0.2630 Lr: 0.00200
[2025-09-06 14:21:22,061 INFO misc.py line 117 1395871] Train: [14/100][337/1462] Data 0.001 (0.001) Batch 2.875 (2.878) Remain 101:24:50 loss: 0.4374 Lr: 0.00200
[2025-09-06 14:21:24,802 INFO misc.py line 117 1395871] Train: [14/100][338/1462] Data 0.001 (0.001) Batch 2.741 (2.878) Remain 101:23:56 loss: 0.3178 Lr: 0.00200
[2025-09-06 14:21:27,550 INFO misc.py line 117 1395871] Train: [14/100][339/1462] Data 0.002 (0.001) Batch 2.748 (2.877) Remain 101:23:04 loss: 0.6267 Lr: 0.00200
[2025-09-06 14:21:30,378 INFO misc.py line 117 1395871] Train: [14/100][340/1462] Data 0.002 (0.001) Batch 2.828 (2.877) Remain 101:22:43 loss: 0.4853 Lr: 0.00200
[2025-09-06 14:21:33,416 INFO misc.py line 117 1395871] Train: [14/100][341/1462] Data 0.001 (0.001) Batch 3.038 (2.878) Remain 101:23:40 loss: 0.6278 Lr: 0.00200
[2025-09-06 14:21:36,201 INFO misc.py line 117 1395871] Train: [14/100][342/1462] Data 0.001 (0.001) Batch 2.785 (2.877) Remain 101:23:03 loss: 0.6813 Lr: 0.00200
[2025-09-06 14:21:38,661 INFO misc.py line 117 1395871] Train: [14/100][343/1462] Data 0.002 (0.001) Batch 2.460 (2.876) Remain 101:20:24 loss: 0.4174 Lr: 0.00200
[2025-09-06 14:21:41,341 INFO misc.py line 117 1395871] Train: [14/100][344/1462] Data 0.001 (0.001) Batch 2.680 (2.875) Remain 101:19:08 loss: 0.2898 Lr: 0.00200
[2025-09-06 14:21:44,221 INFO misc.py line 117 1395871] Train: [14/100][345/1462] Data 0.001 (0.001) Batch 2.880 (2.875) Remain 101:19:07 loss: 0.5914 Lr: 0.00200
[2025-09-06 14:21:47,433 INFO misc.py line 117 1395871] Train: [14/100][346/1462] Data 0.002 (0.001) Batch 3.212 (2.876) Remain 101:21:09 loss: 0.4818 Lr: 0.00200
[2025-09-06 14:21:50,428 INFO misc.py line 117 1395871] Train: [14/100][347/1462] Data 0.001 (0.001) Batch 2.996 (2.877) Remain 101:21:50 loss: 0.3166 Lr: 0.00200
[2025-09-06 14:21:53,234 INFO misc.py line 117 1395871] Train: [14/100][348/1462] Data 0.002 (0.001) Batch 2.806 (2.877) Remain 101:21:21 loss: 0.4273 Lr: 0.00200
[2025-09-06 14:21:56,050 INFO misc.py line 117 1395871] Train: [14/100][349/1462] Data 0.002 (0.001) Batch 2.816 (2.876) Remain 101:20:55 loss: 0.5472 Lr: 0.00200
[2025-09-06 14:21:58,953 INFO misc.py line 117 1395871] Train: [14/100][350/1462] Data 0.001 (0.001) Batch 2.904 (2.876) Remain 101:21:03 loss: 0.3063 Lr: 0.00200
[2025-09-06 14:22:01,756 INFO misc.py line 117 1395871] Train: [14/100][351/1462] Data 0.002 (0.001) Batch 2.802 (2.876) Remain 101:20:33 loss: 0.3851 Lr: 0.00200
[2025-09-06 14:22:04,474 INFO misc.py line 117 1395871] Train: [14/100][352/1462] Data 0.002 (0.001) Batch 2.719 (2.876) Remain 101:19:33 loss: 0.4892 Lr: 0.00200
[2025-09-06 14:22:07,240 INFO misc.py line 117 1395871] Train: [14/100][353/1462] Data 0.001 (0.001) Batch 2.766 (2.875) Remain 101:18:50 loss: 0.2884 Lr: 0.00200
[2025-09-06 14:22:09,856 INFO misc.py line 117 1395871] Train: [14/100][354/1462] Data 0.001 (0.001) Batch 2.616 (2.875) Remain 101:17:13 loss: 0.3087 Lr: 0.00200
[2025-09-06 14:22:12,781 INFO misc.py line 117 1395871] Train: [14/100][355/1462] Data 0.001 (0.001) Batch 2.924 (2.875) Remain 101:17:28 loss: 0.4428 Lr: 0.00200
[2025-09-06 14:22:15,942 INFO misc.py line 117 1395871] Train: [14/100][356/1462] Data 0.002 (0.001) Batch 3.161 (2.876) Remain 101:19:08 loss: 0.6083 Lr: 0.00200
[2025-09-06 14:22:18,637 INFO misc.py line 117 1395871] Train: [14/100][357/1462] Data 0.001 (0.001) Batch 2.696 (2.875) Remain 101:18:01 loss: 0.3584 Lr: 0.00200
[2025-09-06 14:22:21,704 INFO misc.py line 117 1395871] Train: [14/100][358/1462] Data 0.001 (0.001) Batch 3.066 (2.876) Remain 101:19:06 loss: 0.3617 Lr: 0.00200
[2025-09-06 14:22:24,591 INFO misc.py line 117 1395871] Train: [14/100][359/1462] Data 0.002 (0.001) Batch 2.887 (2.876) Remain 101:19:07 loss: 0.5616 Lr: 0.00200
[2025-09-06 14:22:27,449 INFO misc.py line 117 1395871] Train: [14/100][360/1462] Data 0.002 (0.001) Batch 2.858 (2.876) Remain 101:18:58 loss: 0.5414 Lr: 0.00200
[2025-09-06 14:22:30,468 INFO misc.py line 117 1395871] Train: [14/100][361/1462] Data 0.002 (0.001) Batch 3.018 (2.876) Remain 101:19:46 loss: 0.6932 Lr: 0.00200
[2025-09-06 14:22:33,122 INFO misc.py line 117 1395871] Train: [14/100][362/1462] Data 0.002 (0.001) Batch 2.655 (2.875) Remain 101:18:25 loss: 0.4163 Lr: 0.00200
[2025-09-06 14:22:36,021 INFO misc.py line 117 1395871] Train: [14/100][363/1462] Data 0.002 (0.001) Batch 2.899 (2.876) Remain 101:18:30 loss: 0.2857 Lr: 0.00200
[2025-09-06 14:22:38,878 INFO misc.py line 117 1395871] Train: [14/100][364/1462] Data 0.002 (0.001) Batch 2.856 (2.876) Remain 101:18:21 loss: 0.4941 Lr: 0.00200
[2025-09-06 14:22:41,452 INFO misc.py line 117 1395871] Train: [14/100][365/1462] Data 0.002 (0.001) Batch 2.574 (2.875) Remain 101:16:32 loss: 0.2998 Lr: 0.00200
[2025-09-06 14:22:44,259 INFO misc.py line 117 1395871] Train: [14/100][366/1462] Data 0.002 (0.001) Batch 2.808 (2.874) Remain 101:16:06 loss: 0.5316 Lr: 0.00200
[2025-09-06 14:22:47,181 INFO misc.py line 117 1395871] Train: [14/100][367/1462] Data 0.001 (0.001) Batch 2.921 (2.875) Remain 101:16:19 loss: 0.3116 Lr: 0.00200
[2025-09-06 14:22:50,077 INFO misc.py line 117 1395871] Train: [14/100][368/1462] Data 0.001 (0.001) Batch 2.896 (2.875) Remain 101:16:24 loss: 0.3236 Lr: 0.00200
[2025-09-06 14:22:53,154 INFO misc.py line 117 1395871] Train: [14/100][369/1462] Data 0.002 (0.001) Batch 3.078 (2.875) Remain 101:17:31 loss: 0.4795 Lr: 0.00200
[2025-09-06 14:22:56,400 INFO misc.py line 117 1395871] Train: [14/100][370/1462] Data 0.001 (0.001) Batch 3.245 (2.876) Remain 101:19:36 loss: 0.2582 Lr: 0.00200
[2025-09-06 14:22:59,357 INFO misc.py line 117 1395871] Train: [14/100][371/1462] Data 0.002 (0.001) Batch 2.958 (2.876) Remain 101:20:02 loss: 0.3715 Lr: 0.00200
[2025-09-06 14:23:02,617 INFO misc.py line 117 1395871] Train: [14/100][372/1462] Data 0.001 (0.001) Batch 3.260 (2.878) Remain 101:22:10 loss: 0.4994 Lr: 0.00200
[2025-09-06 14:23:05,748 INFO misc.py line 117 1395871] Train: [14/100][373/1462] Data 0.002 (0.001) Batch 3.130 (2.878) Remain 101:23:34 loss: 0.2631 Lr: 0.00200
[2025-09-06 14:23:08,685 INFO misc.py line 117 1395871] Train: [14/100][374/1462] Data 0.002 (0.001) Batch 2.937 (2.878) Remain 101:23:52 loss: 0.7392 Lr: 0.00200
[2025-09-06 14:23:11,310 INFO misc.py line 117 1395871] Train: [14/100][375/1462] Data 0.002 (0.001) Batch 2.625 (2.878) Remain 101:22:22 loss: 0.4977 Lr: 0.00200
[2025-09-06 14:23:14,419 INFO misc.py line 117 1395871] Train: [14/100][376/1462] Data 0.002 (0.001) Batch 3.109 (2.878) Remain 101:23:38 loss: 0.3310 Lr: 0.00200
[2025-09-06 14:23:17,022 INFO misc.py line 117 1395871] Train: [14/100][377/1462] Data 0.002 (0.001) Batch 2.603 (2.878) Remain 101:22:02 loss: 0.2527 Lr: 0.00200
[2025-09-06 14:23:19,859 INFO misc.py line 117 1395871] Train: [14/100][378/1462] Data 0.001 (0.001) Batch 2.837 (2.877) Remain 101:21:45 loss: 0.3537 Lr: 0.00200
[2025-09-06 14:23:22,715 INFO misc.py line 117 1395871] Train: [14/100][379/1462] Data 0.001 (0.001) Batch 2.857 (2.877) Remain 101:21:35 loss: 0.6819 Lr: 0.00200
[2025-09-06 14:23:25,483 INFO misc.py line 117 1395871] Train: [14/100][380/1462] Data 0.001 (0.001) Batch 2.767 (2.877) Remain 101:20:55 loss: 0.2918 Lr: 0.00200
[2025-09-06 14:23:28,282 INFO misc.py line 117 1395871] Train: [14/100][381/1462] Data 0.002 (0.001) Batch 2.799 (2.877) Remain 101:20:26 loss: 0.4894 Lr: 0.00200
[2025-09-06 14:23:31,139 INFO misc.py line 117 1395871] Train: [14/100][382/1462] Data 0.001 (0.001) Batch 2.857 (2.877) Remain 101:20:17 loss: 0.3217 Lr: 0.00200
[2025-09-06 14:23:33,798 INFO misc.py line 117 1395871] Train: [14/100][383/1462] Data 0.002 (0.001) Batch 2.660 (2.876) Remain 101:19:01 loss: 0.3279 Lr: 0.00200
[2025-09-06 14:23:37,157 INFO misc.py line 117 1395871] Train: [14/100][384/1462] Data 0.001 (0.001) Batch 3.358 (2.878) Remain 101:21:39 loss: 0.4952 Lr: 0.00200
[2025-09-06 14:23:39,747 INFO misc.py line 117 1395871] Train: [14/100][385/1462] Data 0.001 (0.001) Batch 2.590 (2.877) Remain 101:20:01 loss: 0.4212 Lr: 0.00200
[2025-09-06 14:23:42,661 INFO misc.py line 117 1395871] Train: [14/100][386/1462] Data 0.002 (0.001) Batch 2.914 (2.877) Remain 101:20:10 loss: 0.4591 Lr: 0.00200
[2025-09-06 14:23:45,758 INFO misc.py line 117 1395871] Train: [14/100][387/1462] Data 0.002 (0.001) Batch 3.097 (2.877) Remain 101:21:20 loss: 0.3450 Lr: 0.00200
[2025-09-06 14:23:48,571 INFO misc.py line 117 1395871] Train: [14/100][388/1462] Data 0.002 (0.001) Batch 2.813 (2.877) Remain 101:20:56 loss: 0.6369 Lr: 0.00200
[2025-09-06 14:23:51,384 INFO misc.py line 117 1395871] Train: [14/100][389/1462] Data 0.002 (0.001) Batch 2.814 (2.877) Remain 101:20:32 loss: 0.2529 Lr: 0.00200
[2025-09-06 14:23:54,492 INFO misc.py line 117 1395871] Train: [14/100][390/1462] Data 0.002 (0.001) Batch 3.107 (2.878) Remain 101:21:45 loss: 0.4761 Lr: 0.00200
[2025-09-06 14:23:57,218 INFO misc.py line 117 1395871] Train: [14/100][391/1462] Data 0.002 (0.001) Batch 2.726 (2.877) Remain 101:20:52 loss: 0.3823 Lr: 0.00200
[2025-09-06 14:23:59,954 INFO misc.py line 117 1395871] Train: [14/100][392/1462] Data 0.001 (0.001) Batch 2.736 (2.877) Remain 101:20:03 loss: 0.3729 Lr: 0.00200
[2025-09-06 14:24:02,854 INFO misc.py line 117 1395871] Train: [14/100][393/1462] Data 0.002 (0.001) Batch 2.900 (2.877) Remain 101:20:08 loss: 0.6687 Lr: 0.00200
[2025-09-06 14:24:05,684 INFO misc.py line 117 1395871] Train: [14/100][394/1462] Data 0.002 (0.001) Batch 2.830 (2.877) Remain 101:19:50 loss: 0.3443 Lr: 0.00200
[2025-09-06 14:24:08,777 INFO misc.py line 117 1395871] Train: [14/100][395/1462] Data 0.002 (0.001) Batch 3.093 (2.877) Remain 101:20:57 loss: 0.6076 Lr: 0.00200
[2025-09-06 14:24:11,630 INFO misc.py line 117 1395871] Train: [14/100][396/1462] Data 0.002 (0.001) Batch 2.853 (2.877) Remain 101:20:46 loss: 0.3333 Lr: 0.00200
[2025-09-06 14:24:14,529 INFO misc.py line 117 1395871] Train: [14/100][397/1462] Data 0.001 (0.001) Batch 2.899 (2.877) Remain 101:20:50 loss: 0.4819 Lr: 0.00200
[2025-09-06 14:24:17,298 INFO misc.py line 117 1395871] Train: [14/100][398/1462] Data 0.001 (0.001) Batch 2.769 (2.877) Remain 101:20:12 loss: 0.4325 Lr: 0.00200
[2025-09-06 14:24:20,525 INFO misc.py line 117 1395871] Train: [14/100][399/1462] Data 0.002 (0.001) Batch 3.226 (2.878) Remain 101:22:01 loss: 0.4564 Lr: 0.00200
[2025-09-06 14:24:23,514 INFO misc.py line 117 1395871] Train: [14/100][400/1462] Data 0.002 (0.001) Batch 2.990 (2.878) Remain 101:22:34 loss: 0.2920 Lr: 0.00200
[2025-09-06 14:24:26,523 INFO misc.py line 117 1395871] Train: [14/100][401/1462] Data 0.002 (0.001) Batch 3.009 (2.879) Remain 101:23:13 loss: 0.4453 Lr: 0.00200
[2025-09-06 14:24:29,565 INFO misc.py line 117 1395871] Train: [14/100][402/1462] Data 0.002 (0.001) Batch 3.042 (2.879) Remain 101:24:02 loss: 0.5434 Lr: 0.00200
[2025-09-06 14:24:32,431 INFO misc.py line 117 1395871] Train: [14/100][403/1462] Data 0.002 (0.001) Batch 2.866 (2.879) Remain 101:23:55 loss: 0.4861 Lr: 0.00200
[2025-09-06 14:24:35,194 INFO misc.py line 117 1395871] Train: [14/100][404/1462] Data 0.002 (0.001) Batch 2.762 (2.879) Remain 101:23:15 loss: 0.3825 Lr: 0.00200
[2025-09-06 14:24:38,425 INFO misc.py line 117 1395871] Train: [14/100][405/1462] Data 0.002 (0.001) Batch 3.231 (2.880) Remain 101:25:04 loss: 0.3824 Lr: 0.00200
[2025-09-06 14:24:41,528 INFO misc.py line 117 1395871] Train: [14/100][406/1462] Data 0.001 (0.001) Batch 3.103 (2.880) Remain 101:26:11 loss: 0.6092 Lr: 0.00200
[2025-09-06 14:24:44,181 INFO misc.py line 117 1395871] Train: [14/100][407/1462] Data 0.001 (0.001) Batch 2.653 (2.880) Remain 101:24:57 loss: 0.3037 Lr: 0.00200
[2025-09-06 14:24:47,107 INFO misc.py line 117 1395871] Train: [14/100][408/1462] Data 0.002 (0.001) Batch 2.926 (2.880) Remain 101:25:08 loss: 0.2421 Lr: 0.00200
[2025-09-06 14:24:50,053 INFO misc.py line 117 1395871] Train: [14/100][409/1462] Data 0.002 (0.001) Batch 2.947 (2.880) Remain 101:25:26 loss: 0.4137 Lr: 0.00200
[2025-09-06 14:24:53,021 INFO misc.py line 117 1395871] Train: [14/100][410/1462] Data 0.001 (0.001) Batch 2.968 (2.880) Remain 101:25:51 loss: 0.6929 Lr: 0.00200
[2025-09-06 14:24:55,615 INFO misc.py line 117 1395871] Train: [14/100][411/1462] Data 0.001 (0.001) Batch 2.594 (2.879) Remain 101:24:19 loss: 0.5242 Lr: 0.00200
[2025-09-06 14:24:58,218 INFO misc.py line 117 1395871] Train: [14/100][412/1462] Data 0.001 (0.001) Batch 2.603 (2.879) Remain 101:22:50 loss: 0.5253 Lr: 0.00200
[2025-09-06 14:25:01,170 INFO misc.py line 117 1395871] Train: [14/100][413/1462] Data 0.002 (0.001) Batch 2.952 (2.879) Remain 101:23:10 loss: 0.5185 Lr: 0.00200
[2025-09-06 14:25:04,240 INFO misc.py line 117 1395871] Train: [14/100][414/1462] Data 0.002 (0.001) Batch 3.070 (2.879) Remain 101:24:06 loss: 0.5300 Lr: 0.00200
[2025-09-06 14:25:07,172 INFO misc.py line 117 1395871] Train: [14/100][415/1462] Data 0.002 (0.001) Batch 2.932 (2.879) Remain 101:24:19 loss: 0.5537 Lr: 0.00200
[2025-09-06 14:25:10,342 INFO misc.py line 117 1395871] Train: [14/100][416/1462] Data 0.002 (0.001) Batch 3.170 (2.880) Remain 101:25:46 loss: 0.7209 Lr: 0.00200
[2025-09-06 14:25:13,299 INFO misc.py line 117 1395871] Train: [14/100][417/1462] Data 0.001 (0.001) Batch 2.957 (2.880) Remain 101:26:06 loss: 0.5231 Lr: 0.00200
[2025-09-06 14:25:16,176 INFO misc.py line 117 1395871] Train: [14/100][418/1462] Data 0.001 (0.001) Batch 2.878 (2.880) Remain 101:26:03 loss: 0.3754 Lr: 0.00200
[2025-09-06 14:25:19,114 INFO misc.py line 117 1395871] Train: [14/100][419/1462] Data 0.002 (0.001) Batch 2.937 (2.881) Remain 101:26:17 loss: 0.5073 Lr: 0.00200
[2025-09-06 14:25:22,053 INFO misc.py line 117 1395871] Train: [14/100][420/1462] Data 0.001 (0.001) Batch 2.940 (2.881) Remain 101:26:32 loss: 0.2523 Lr: 0.00200
[2025-09-06 14:25:24,739 INFO misc.py line 117 1395871] Train: [14/100][421/1462] Data 0.002 (0.001) Batch 2.686 (2.880) Remain 101:25:30 loss: 0.5997 Lr: 0.00200
[2025-09-06 14:25:27,442 INFO misc.py line 117 1395871] Train: [14/100][422/1462] Data 0.002 (0.001) Batch 2.703 (2.880) Remain 101:24:34 loss: 0.4605 Lr: 0.00200
[2025-09-06 14:25:30,300 INFO misc.py line 117 1395871] Train: [14/100][423/1462] Data 0.002 (0.001) Batch 2.859 (2.880) Remain 101:24:24 loss: 0.3531 Lr: 0.00200
[2025-09-06 14:25:32,913 INFO misc.py line 117 1395871] Train: [14/100][424/1462] Data 0.001 (0.001) Batch 2.612 (2.879) Remain 101:23:01 loss: 0.4335 Lr: 0.00200
[2025-09-06 14:25:35,889 INFO misc.py line 117 1395871] Train: [14/100][425/1462] Data 0.001 (0.001) Batch 2.976 (2.879) Remain 101:23:27 loss: 0.6874 Lr: 0.00200
[2025-09-06 14:25:38,871 INFO misc.py line 117 1395871] Train: [14/100][426/1462] Data 0.001 (0.001) Batch 2.982 (2.880) Remain 101:23:55 loss: 0.3676 Lr: 0.00200
[2025-09-06 14:25:41,999 INFO misc.py line 117 1395871] Train: [14/100][427/1462] Data 0.001 (0.001) Batch 3.129 (2.880) Remain 101:25:07 loss: 0.1978 Lr: 0.00200
[2025-09-06 14:25:44,687 INFO misc.py line 117 1395871] Train: [14/100][428/1462] Data 0.002 (0.001) Batch 2.688 (2.880) Remain 101:24:06 loss: 0.4082 Lr: 0.00200
[2025-09-06 14:25:47,888 INFO misc.py line 117 1395871] Train: [14/100][429/1462] Data 0.001 (0.001) Batch 3.201 (2.880) Remain 101:25:39 loss: 0.4641 Lr: 0.00200
[2025-09-06 14:25:50,556 INFO misc.py line 117 1395871] Train: [14/100][430/1462] Data 0.001 (0.001) Batch 2.668 (2.880) Remain 101:24:33 loss: 0.4012 Lr: 0.00200
[2025-09-06 14:25:53,273 INFO misc.py line 117 1395871] Train: [14/100][431/1462] Data 0.001 (0.001) Batch 2.718 (2.880) Remain 101:23:42 loss: 0.4500 Lr: 0.00200
[2025-09-06 14:25:56,200 INFO misc.py line 117 1395871] Train: [14/100][432/1462] Data 0.001 (0.001) Batch 2.926 (2.880) Remain 101:23:53 loss: 0.5458 Lr: 0.00200
[2025-09-06 14:25:59,197 INFO misc.py line 117 1395871] Train: [14/100][433/1462] Data 0.002 (0.001) Batch 2.997 (2.880) Remain 101:24:25 loss: 0.4105 Lr: 0.00200
[2025-09-06 14:26:01,970 INFO misc.py line 117 1395871] Train: [14/100][434/1462] Data 0.002 (0.001) Batch 2.773 (2.880) Remain 101:23:51 loss: 0.3941 Lr: 0.00200
[2025-09-06 14:26:04,707 INFO misc.py line 117 1395871] Train: [14/100][435/1462] Data 0.001 (0.001) Batch 2.737 (2.879) Remain 101:23:06 loss: 0.5397 Lr: 0.00200
[2025-09-06 14:26:07,401 INFO misc.py line 117 1395871] Train: [14/100][436/1462] Data 0.002 (0.001) Batch 2.694 (2.879) Remain 101:22:09 loss: 0.5682 Lr: 0.00200
[2025-09-06 14:26:10,533 INFO misc.py line 117 1395871] Train: [14/100][437/1462] Data 0.001 (0.001) Batch 3.132 (2.880) Remain 101:23:20 loss: 0.4375 Lr: 0.00200
[2025-09-06 14:26:13,707 INFO misc.py line 117 1395871] Train: [14/100][438/1462] Data 0.001 (0.001) Batch 3.174 (2.880) Remain 101:24:43 loss: 0.7999 Lr: 0.00200
[2025-09-06 14:26:16,211 INFO misc.py line 117 1395871] Train: [14/100][439/1462] Data 0.001 (0.001) Batch 2.504 (2.879) Remain 101:22:51 loss: 0.4704 Lr: 0.00200
[2025-09-06 14:26:19,157 INFO misc.py line 117 1395871] Train: [14/100][440/1462] Data 0.001 (0.001) Batch 2.946 (2.879) Remain 101:23:07 loss: 0.3097 Lr: 0.00200
[2025-09-06 14:26:22,365 INFO misc.py line 117 1395871] Train: [14/100][441/1462] Data 0.001 (0.001) Batch 3.208 (2.880) Remain 101:24:39 loss: 0.5444 Lr: 0.00200
[2025-09-06 14:26:25,352 INFO misc.py line 117 1395871] Train: [14/100][442/1462] Data 0.001 (0.001) Batch 2.987 (2.880) Remain 101:25:07 loss: 0.3015 Lr: 0.00200
[2025-09-06 14:26:28,682 INFO misc.py line 117 1395871] Train: [14/100][443/1462] Data 0.001 (0.001) Batch 3.330 (2.882) Remain 101:27:14 loss: 0.3249 Lr: 0.00200
[2025-09-06 14:26:31,369 INFO misc.py line 117 1395871] Train: [14/100][444/1462] Data 0.001 (0.001) Batch 2.687 (2.881) Remain 101:26:15 loss: 0.3247 Lr: 0.00200
[2025-09-06 14:26:34,090 INFO misc.py line 117 1395871] Train: [14/100][445/1462] Data 0.001 (0.001) Batch 2.721 (2.881) Remain 101:25:26 loss: 0.2878 Lr: 0.00200
[2025-09-06 14:26:36,602 INFO misc.py line 117 1395871] Train: [14/100][446/1462] Data 0.001 (0.001) Batch 2.512 (2.880) Remain 101:23:38 loss: 0.5156 Lr: 0.00200
[2025-09-06 14:26:39,206 INFO misc.py line 117 1395871] Train: [14/100][447/1462] Data 0.001 (0.001) Batch 2.604 (2.879) Remain 101:22:16 loss: 0.3561 Lr: 0.00200
[2025-09-06 14:26:41,817 INFO misc.py line 117 1395871] Train: [14/100][448/1462] Data 0.001 (0.001) Batch 2.611 (2.879) Remain 101:20:57 loss: 0.2933 Lr: 0.00200
[2025-09-06 14:26:44,603 INFO misc.py line 117 1395871] Train: [14/100][449/1462] Data 0.001 (0.001) Batch 2.786 (2.878) Remain 101:20:28 loss: 0.3312 Lr: 0.00200
[2025-09-06 14:26:47,293 INFO misc.py line 117 1395871] Train: [14/100][450/1462] Data 0.001 (0.001) Batch 2.690 (2.878) Remain 101:19:31 loss: 0.4629 Lr: 0.00200
[2025-09-06 14:26:50,107 INFO misc.py line 117 1395871] Train: [14/100][451/1462] Data 0.001 (0.001) Batch 2.814 (2.878) Remain 101:19:10 loss: 0.2455 Lr: 0.00200
[2025-09-06 14:26:52,851 INFO misc.py line 117 1395871] Train: [14/100][452/1462] Data 0.002 (0.001) Batch 2.744 (2.878) Remain 101:18:30 loss: 0.5102 Lr: 0.00200
[2025-09-06 14:26:55,850 INFO misc.py line 117 1395871] Train: [14/100][453/1462] Data 0.001 (0.001) Batch 2.998 (2.878) Remain 101:19:01 loss: 0.4419 Lr: 0.00200
[2025-09-06 14:26:58,595 INFO misc.py line 117 1395871] Train: [14/100][454/1462] Data 0.001 (0.001) Batch 2.745 (2.878) Remain 101:18:21 loss: 0.2636 Lr: 0.00200
[2025-09-06 14:27:01,659 INFO misc.py line 117 1395871] Train: [14/100][455/1462] Data 0.002 (0.001) Batch 3.064 (2.878) Remain 101:19:10 loss: 0.3898 Lr: 0.00200
[2025-09-06 14:27:04,611 INFO misc.py line 117 1395871] Train: [14/100][456/1462] Data 0.002 (0.001) Batch 2.952 (2.878) Remain 101:19:28 loss: 0.3175 Lr: 0.00200
[2025-09-06 14:27:07,712 INFO misc.py line 117 1395871] Train: [14/100][457/1462] Data 0.002 (0.001) Batch 3.101 (2.879) Remain 101:20:27 loss: 0.2291 Lr: 0.00200
[2025-09-06 14:27:10,406 INFO misc.py line 117 1395871] Train: [14/100][458/1462] Data 0.001 (0.001) Batch 2.693 (2.878) Remain 101:19:33 loss: 0.6296 Lr: 0.00200
[2025-09-06 14:27:13,362 INFO misc.py line 117 1395871] Train: [14/100][459/1462] Data 0.001 (0.001) Batch 2.956 (2.878) Remain 101:19:52 loss: 0.4788 Lr: 0.00200
[2025-09-06 14:27:16,629 INFO misc.py line 117 1395871] Train: [14/100][460/1462] Data 0.001 (0.001) Batch 3.267 (2.879) Remain 101:21:37 loss: 0.4066 Lr: 0.00200
[2025-09-06 14:27:19,743 INFO misc.py line 117 1395871] Train: [14/100][461/1462] Data 0.002 (0.001) Batch 3.114 (2.880) Remain 101:22:39 loss: 0.3241 Lr: 0.00200
[2025-09-06 14:27:22,698 INFO misc.py line 117 1395871] Train: [14/100][462/1462] Data 0.001 (0.001) Batch 2.955 (2.880) Remain 101:22:57 loss: 0.5203 Lr: 0.00200
[2025-09-06 14:27:25,603 INFO misc.py line 117 1395871] Train: [14/100][463/1462] Data 0.002 (0.001) Batch 2.905 (2.880) Remain 101:23:01 loss: 0.4157 Lr: 0.00200
[2025-09-06 14:27:28,654 INFO misc.py line 117 1395871] Train: [14/100][464/1462] Data 0.002 (0.001) Batch 3.051 (2.880) Remain 101:23:45 loss: 0.4463 Lr: 0.00200
[2025-09-06 14:27:31,860 INFO misc.py line 117 1395871] Train: [14/100][465/1462] Data 0.002 (0.001) Batch 3.206 (2.881) Remain 101:25:11 loss: 0.2132 Lr: 0.00200
[2025-09-06 14:27:34,945 INFO misc.py line 117 1395871] Train: [14/100][466/1462] Data 0.002 (0.001) Batch 3.086 (2.881) Remain 101:26:04 loss: 0.3505 Lr: 0.00200
[2025-09-06 14:27:37,720 INFO misc.py line 117 1395871] Train: [14/100][467/1462] Data 0.001 (0.001) Batch 2.775 (2.881) Remain 101:25:32 loss: 0.4084 Lr: 0.00200
[2025-09-06 14:27:40,701 INFO misc.py line 117 1395871] Train: [14/100][468/1462] Data 0.002 (0.001) Batch 2.981 (2.881) Remain 101:25:56 loss: 0.3826 Lr: 0.00200
[2025-09-06 14:27:43,622 INFO misc.py line 117 1395871] Train: [14/100][469/1462] Data 0.002 (0.001) Batch 2.921 (2.882) Remain 101:26:04 loss: 0.3540 Lr: 0.00200
[2025-09-06 14:27:46,483 INFO misc.py line 117 1395871] Train: [14/100][470/1462] Data 0.002 (0.001) Batch 2.861 (2.882) Remain 101:25:56 loss: 0.2715 Lr: 0.00200
[2025-09-06 14:27:49,382 INFO misc.py line 117 1395871] Train: [14/100][471/1462] Data 0.002 (0.001) Batch 2.899 (2.882) Remain 101:25:58 loss: 0.4660 Lr: 0.00200
[2025-09-06 14:27:52,296 INFO misc.py line 117 1395871] Train: [14/100][472/1462] Data 0.002 (0.001) Batch 2.914 (2.882) Remain 101:26:04 loss: 0.3263 Lr: 0.00200
[2025-09-06 14:27:55,568 INFO misc.py line 117 1395871] Train: [14/100][473/1462] Data 0.002 (0.001) Batch 3.272 (2.882) Remain 101:27:46 loss: 0.3548 Lr: 0.00200
[2025-09-06 14:27:58,463 INFO misc.py line 117 1395871] Train: [14/100][474/1462] Data 0.001 (0.001) Batch 2.896 (2.882) Remain 101:27:47 loss: 0.8003 Lr: 0.00200
[2025-09-06 14:28:01,217 INFO misc.py line 117 1395871] Train: [14/100][475/1462] Data 0.002 (0.001) Batch 2.754 (2.882) Remain 101:27:09 loss: 0.4734 Lr: 0.00200
[2025-09-06 14:28:04,133 INFO misc.py line 117 1395871] Train: [14/100][476/1462] Data 0.002 (0.001) Batch 2.916 (2.882) Remain 101:27:15 loss: 0.3945 Lr: 0.00200
[2025-09-06 14:28:07,067 INFO misc.py line 117 1395871] Train: [14/100][477/1462] Data 0.001 (0.001) Batch 2.934 (2.882) Remain 101:27:26 loss: 0.5508 Lr: 0.00200
[2025-09-06 14:28:10,067 INFO misc.py line 117 1395871] Train: [14/100][478/1462] Data 0.001 (0.001) Batch 3.000 (2.883) Remain 101:27:55 loss: 0.6116 Lr: 0.00200
[2025-09-06 14:28:12,836 INFO misc.py line 117 1395871] Train: [14/100][479/1462] Data 0.001 (0.001) Batch 2.770 (2.882) Remain 101:27:22 loss: 0.5598 Lr: 0.00200
[2025-09-06 14:28:15,924 INFO misc.py line 117 1395871] Train: [14/100][480/1462] Data 0.002 (0.001) Batch 3.088 (2.883) Remain 101:28:13 loss: 0.3911 Lr: 0.00200
[2025-09-06 14:28:19,376 INFO misc.py line 117 1395871] Train: [14/100][481/1462] Data 0.002 (0.001) Batch 3.452 (2.884) Remain 101:30:41 loss: 0.3997 Lr: 0.00200
[2025-09-06 14:28:22,446 INFO misc.py line 117 1395871] Train: [14/100][482/1462] Data 0.002 (0.001) Batch 3.069 (2.884) Remain 101:31:28 loss: 0.2913 Lr: 0.00200
[2025-09-06 14:28:25,165 INFO misc.py line 117 1395871] Train: [14/100][483/1462] Data 0.002 (0.001) Batch 2.719 (2.884) Remain 101:30:41 loss: 0.3390 Lr: 0.00200
[2025-09-06 14:28:28,007 INFO misc.py line 117 1395871] Train: [14/100][484/1462] Data 0.002 (0.001) Batch 2.842 (2.884) Remain 101:30:27 loss: 0.3969 Lr: 0.00200
[2025-09-06 14:28:30,968 INFO misc.py line 117 1395871] Train: [14/100][485/1462] Data 0.001 (0.001) Batch 2.961 (2.884) Remain 101:30:44 loss: 0.2628 Lr: 0.00200
[2025-09-06 14:28:33,687 INFO misc.py line 117 1395871] Train: [14/100][486/1462] Data 0.001 (0.001) Batch 2.719 (2.884) Remain 101:29:58 loss: 0.5610 Lr: 0.00200
[2025-09-06 14:28:36,750 INFO misc.py line 117 1395871] Train: [14/100][487/1462] Data 0.002 (0.001) Batch 3.063 (2.884) Remain 101:30:42 loss: 0.4982 Lr: 0.00200
[2025-09-06 14:28:39,818 INFO misc.py line 117 1395871] Train: [14/100][488/1462] Data 0.001 (0.001) Batch 3.068 (2.885) Remain 101:31:27 loss: 0.3425 Lr: 0.00200
[2025-09-06 14:28:42,752 INFO misc.py line 117 1395871] Train: [14/100][489/1462] Data 0.001 (0.001) Batch 2.934 (2.885) Remain 101:31:38 loss: 0.2527 Lr: 0.00200
[2025-09-06 14:28:45,794 INFO misc.py line 117 1395871] Train: [14/100][490/1462] Data 0.001 (0.001) Batch 3.042 (2.885) Remain 101:32:16 loss: 0.3422 Lr: 0.00200
[2025-09-06 14:28:48,935 INFO misc.py line 117 1395871] Train: [14/100][491/1462] Data 0.001 (0.001) Batch 3.141 (2.885) Remain 101:33:19 loss: 0.4649 Lr: 0.00200
[2025-09-06 14:28:51,766 INFO misc.py line 117 1395871] Train: [14/100][492/1462] Data 0.001 (0.001) Batch 2.832 (2.885) Remain 101:33:02 loss: 0.5636 Lr: 0.00200
[2025-09-06 14:28:54,662 INFO misc.py line 117 1395871] Train: [14/100][493/1462] Data 0.001 (0.001) Batch 2.896 (2.885) Remain 101:33:02 loss: 0.4707 Lr: 0.00200
[2025-09-06 14:28:57,468 INFO misc.py line 117 1395871] Train: [14/100][494/1462] Data 0.001 (0.001) Batch 2.806 (2.885) Remain 101:32:39 loss: 0.7423 Lr: 0.00200
[2025-09-06 14:29:00,069 INFO misc.py line 117 1395871] Train: [14/100][495/1462] Data 0.001 (0.001) Batch 2.601 (2.885) Remain 101:31:22 loss: 0.3109 Lr: 0.00200
[2025-09-06 14:29:02,849 INFO misc.py line 117 1395871] Train: [14/100][496/1462] Data 0.002 (0.001) Batch 2.780 (2.884) Remain 101:30:53 loss: 0.3967 Lr: 0.00200
[2025-09-06 14:29:05,448 INFO misc.py line 117 1395871] Train: [14/100][497/1462] Data 0.001 (0.001) Batch 2.600 (2.884) Remain 101:29:37 loss: 0.6235 Lr: 0.00200
[2025-09-06 14:29:08,228 INFO misc.py line 117 1395871] Train: [14/100][498/1462] Data 0.001 (0.001) Batch 2.780 (2.884) Remain 101:29:07 loss: 0.4526 Lr: 0.00200
[2025-09-06 14:29:10,653 INFO misc.py line 117 1395871] Train: [14/100][499/1462] Data 0.001 (0.001) Batch 2.425 (2.883) Remain 101:27:07 loss: 0.3947 Lr: 0.00200
[2025-09-06 14:29:13,587 INFO misc.py line 117 1395871] Train: [14/100][500/1462] Data 0.001 (0.001) Batch 2.934 (2.883) Remain 101:27:17 loss: 1.2213 Lr: 0.00200
[2025-09-06 14:29:16,622 INFO misc.py line 117 1395871] Train: [14/100][501/1462] Data 0.001 (0.001) Batch 3.035 (2.883) Remain 101:27:53 loss: 0.3344 Lr: 0.00200
[2025-09-06 14:29:19,597 INFO misc.py line 117 1395871] Train: [14/100][502/1462] Data 0.001 (0.001) Batch 2.974 (2.883) Remain 101:28:14 loss: 0.7477 Lr: 0.00200
[2025-09-06 14:29:22,172 INFO misc.py line 117 1395871] Train: [14/100][503/1462] Data 0.001 (0.001) Batch 2.575 (2.883) Remain 101:26:53 loss: 0.3171 Lr: 0.00200
[2025-09-06 14:29:25,160 INFO misc.py line 117 1395871] Train: [14/100][504/1462] Data 0.001 (0.001) Batch 2.988 (2.883) Remain 101:27:16 loss: 0.5137 Lr: 0.00200
[2025-09-06 14:29:28,235 INFO misc.py line 117 1395871] Train: [14/100][505/1462] Data 0.001 (0.001) Batch 3.075 (2.883) Remain 101:28:02 loss: 0.4246 Lr: 0.00200
[2025-09-06 14:29:30,896 INFO misc.py line 117 1395871] Train: [14/100][506/1462] Data 0.001 (0.001) Batch 2.661 (2.883) Remain 101:27:03 loss: 0.3843 Lr: 0.00200
[2025-09-06 14:29:33,613 INFO misc.py line 117 1395871] Train: [14/100][507/1462] Data 0.001 (0.001) Batch 2.717 (2.883) Remain 101:26:19 loss: 0.4444 Lr: 0.00200
[2025-09-06 14:29:36,434 INFO misc.py line 117 1395871] Train: [14/100][508/1462] Data 0.001 (0.001) Batch 2.821 (2.882) Remain 101:26:00 loss: 0.4519 Lr: 0.00200
[2025-09-06 14:29:38,890 INFO misc.py line 117 1395871] Train: [14/100][509/1462] Data 0.001 (0.001) Batch 2.456 (2.882) Remain 101:24:11 loss: 0.3079 Lr: 0.00200
[2025-09-06 14:29:41,856 INFO misc.py line 117 1395871] Train: [14/100][510/1462] Data 0.001 (0.001) Batch 2.965 (2.882) Remain 101:24:29 loss: 0.5455 Lr: 0.00200
[2025-09-06 14:29:44,854 INFO misc.py line 117 1395871] Train: [14/100][511/1462] Data 0.002 (0.001) Batch 2.999 (2.882) Remain 101:24:55 loss: 0.3745 Lr: 0.00200
[2025-09-06 14:29:47,537 INFO misc.py line 117 1395871] Train: [14/100][512/1462] Data 0.001 (0.001) Batch 2.682 (2.882) Remain 101:24:02 loss: 0.3131 Lr: 0.00200
[2025-09-06 14:29:50,084 INFO misc.py line 117 1395871] Train: [14/100][513/1462] Data 0.001 (0.001) Batch 2.548 (2.881) Remain 101:22:36 loss: 0.4998 Lr: 0.00200
[2025-09-06 14:29:52,777 INFO misc.py line 117 1395871] Train: [14/100][514/1462] Data 0.001 (0.001) Batch 2.693 (2.881) Remain 101:21:47 loss: 0.2930 Lr: 0.00200
[2025-09-06 14:29:55,228 INFO misc.py line 117 1395871] Train: [14/100][515/1462] Data 0.001 (0.001) Batch 2.450 (2.880) Remain 101:19:58 loss: 0.4767 Lr: 0.00200
[2025-09-06 14:29:58,304 INFO misc.py line 117 1395871] Train: [14/100][516/1462] Data 0.001 (0.001) Batch 3.076 (2.880) Remain 101:20:43 loss: 0.2687 Lr: 0.00200
[2025-09-06 14:30:01,060 INFO misc.py line 117 1395871] Train: [14/100][517/1462] Data 0.001 (0.001) Batch 2.756 (2.880) Remain 101:20:10 loss: 0.4433 Lr: 0.00200
[2025-09-06 14:30:03,855 INFO misc.py line 117 1395871] Train: [14/100][518/1462] Data 0.001 (0.001) Batch 2.795 (2.880) Remain 101:19:46 loss: 0.4135 Lr: 0.00200
[2025-09-06 14:30:06,640 INFO misc.py line 117 1395871] Train: [14/100][519/1462] Data 0.001 (0.001) Batch 2.785 (2.880) Remain 101:19:20 loss: 0.3470 Lr: 0.00200
[2025-09-06 14:30:09,649 INFO misc.py line 117 1395871] Train: [14/100][520/1462] Data 0.001 (0.001) Batch 3.009 (2.880) Remain 101:19:49 loss: 0.6496 Lr: 0.00200
[2025-09-06 14:30:12,168 INFO misc.py line 117 1395871] Train: [14/100][521/1462] Data 0.001 (0.001) Batch 2.518 (2.879) Remain 101:18:18 loss: 0.3774 Lr: 0.00200
[2025-09-06 14:30:15,293 INFO misc.py line 117 1395871] Train: [14/100][522/1462] Data 0.002 (0.001) Batch 3.125 (2.880) Remain 101:19:15 loss: 0.5614 Lr: 0.00200
[2025-09-06 14:30:18,020 INFO misc.py line 117 1395871] Train: [14/100][523/1462] Data 0.001 (0.001) Batch 2.727 (2.879) Remain 101:18:35 loss: 0.3720 Lr: 0.00200
[2025-09-06 14:30:20,950 INFO misc.py line 117 1395871] Train: [14/100][524/1462] Data 0.001 (0.001) Batch 2.930 (2.879) Remain 101:18:44 loss: 0.2570 Lr: 0.00200
[2025-09-06 14:30:24,320 INFO misc.py line 117 1395871] Train: [14/100][525/1462] Data 0.001 (0.001) Batch 3.370 (2.880) Remain 101:20:41 loss: 0.4030 Lr: 0.00200
[2025-09-06 14:30:27,411 INFO misc.py line 117 1395871] Train: [14/100][526/1462] Data 0.001 (0.001) Batch 3.091 (2.881) Remain 101:21:29 loss: 0.2629 Lr: 0.00200
[2025-09-06 14:30:30,544 INFO misc.py line 117 1395871] Train: [14/100][527/1462] Data 0.001 (0.001) Batch 3.133 (2.881) Remain 101:22:27 loss: 0.4262 Lr: 0.00200
[2025-09-06 14:30:33,454 INFO misc.py line 117 1395871] Train: [14/100][528/1462] Data 0.001 (0.001) Batch 2.910 (2.881) Remain 101:22:31 loss: 0.4098 Lr: 0.00200
[2025-09-06 14:30:36,624 INFO misc.py line 117 1395871] Train: [14/100][529/1462] Data 0.001 (0.001) Batch 3.170 (2.882) Remain 101:23:38 loss: 0.6186 Lr: 0.00200
[2025-09-06 14:30:39,372 INFO misc.py line 117 1395871] Train: [14/100][530/1462] Data 0.001 (0.001) Batch 2.748 (2.882) Remain 101:23:03 loss: 0.3977 Lr: 0.00200
[2025-09-06 14:30:42,694 INFO misc.py line 117 1395871] Train: [14/100][531/1462] Data 0.001 (0.001) Batch 3.322 (2.882) Remain 101:24:45 loss: 0.3265 Lr: 0.00200
[2025-09-06 14:30:45,712 INFO misc.py line 117 1395871] Train: [14/100][532/1462] Data 0.001 (0.001) Batch 3.018 (2.883) Remain 101:25:15 loss: 0.4651 Lr: 0.00200
[2025-09-06 14:30:48,601 INFO misc.py line 117 1395871] Train: [14/100][533/1462] Data 0.001 (0.001) Batch 2.889 (2.883) Remain 101:25:14 loss: 0.3790 Lr: 0.00200
[2025-09-06 14:30:51,361 INFO misc.py line 117 1395871] Train: [14/100][534/1462] Data 0.001 (0.001) Batch 2.760 (2.882) Remain 101:24:42 loss: 0.4224 Lr: 0.00200
[2025-09-06 14:30:54,004 INFO misc.py line 117 1395871] Train: [14/100][535/1462] Data 0.001 (0.001) Batch 2.642 (2.882) Remain 101:23:42 loss: 0.3287 Lr: 0.00200
[2025-09-06 14:30:56,752 INFO misc.py line 117 1395871] Train: [14/100][536/1462] Data 0.001 (0.001) Batch 2.748 (2.882) Remain 101:23:07 loss: 0.7638 Lr: 0.00200
[2025-09-06 14:30:59,389 INFO misc.py line 117 1395871] Train: [14/100][537/1462] Data 0.002 (0.001) Batch 2.637 (2.881) Remain 101:22:06 loss: 0.4774 Lr: 0.00200
[2025-09-06 14:31:01,995 INFO misc.py line 117 1395871] Train: [14/100][538/1462] Data 0.001 (0.001) Batch 2.606 (2.881) Remain 101:20:58 loss: 0.4006 Lr: 0.00200
[2025-09-06 14:31:04,668 INFO misc.py line 117 1395871] Train: [14/100][539/1462] Data 0.001 (0.001) Batch 2.673 (2.880) Remain 101:20:06 loss: 0.3606 Lr: 0.00200
[2025-09-06 14:31:07,406 INFO misc.py line 117 1395871] Train: [14/100][540/1462] Data 0.002 (0.001) Batch 2.737 (2.880) Remain 101:19:29 loss: 0.2109 Lr: 0.00200
[2025-09-06 14:31:10,227 INFO misc.py line 117 1395871] Train: [14/100][541/1462] Data 0.001 (0.001) Batch 2.822 (2.880) Remain 101:19:13 loss: 0.4432 Lr: 0.00200
[2025-09-06 14:31:12,931 INFO misc.py line 117 1395871] Train: [14/100][542/1462] Data 0.001 (0.001) Batch 2.704 (2.880) Remain 101:18:28 loss: 0.4110 Lr: 0.00200
[2025-09-06 14:31:15,703 INFO misc.py line 117 1395871] Train: [14/100][543/1462] Data 0.002 (0.001) Batch 2.772 (2.879) Remain 101:18:00 loss: 0.4999 Lr: 0.00200
[2025-09-06 14:31:18,698 INFO misc.py line 117 1395871] Train: [14/100][544/1462] Data 0.002 (0.001) Batch 2.995 (2.880) Remain 101:18:24 loss: 0.5987 Lr: 0.00200
[2025-09-06 14:31:21,751 INFO misc.py line 117 1395871] Train: [14/100][545/1462] Data 0.001 (0.001) Batch 3.054 (2.880) Remain 101:19:02 loss: 0.2325 Lr: 0.00200
[2025-09-06 14:31:25,209 INFO misc.py line 117 1395871] Train: [14/100][546/1462] Data 0.002 (0.001) Batch 3.458 (2.881) Remain 101:21:14 loss: 0.5095 Lr: 0.00200
[2025-09-06 14:31:28,091 INFO misc.py line 117 1395871] Train: [14/100][547/1462] Data 0.002 (0.001) Batch 2.882 (2.881) Remain 101:21:12 loss: 0.3608 Lr: 0.00200
[2025-09-06 14:31:31,180 INFO misc.py line 117 1395871] Train: [14/100][548/1462] Data 0.001 (0.001) Batch 3.088 (2.881) Remain 101:21:57 loss: 0.3493 Lr: 0.00200
[2025-09-06 14:31:34,090 INFO misc.py line 117 1395871] Train: [14/100][549/1462] Data 0.001 (0.001) Batch 2.911 (2.881) Remain 101:22:01 loss: 0.2564 Lr: 0.00200
[2025-09-06 14:31:36,671 INFO misc.py line 117 1395871] Train: [14/100][550/1462] Data 0.001 (0.001) Batch 2.581 (2.881) Remain 101:20:48 loss: 0.4645 Lr: 0.00200
[2025-09-06 14:31:39,442 INFO misc.py line 117 1395871] Train: [14/100][551/1462] Data 0.002 (0.001) Batch 2.771 (2.881) Remain 101:20:20 loss: 0.3181 Lr: 0.00200
[2025-09-06 14:31:42,357 INFO misc.py line 117 1395871] Train: [14/100][552/1462] Data 0.001 (0.001) Batch 2.915 (2.881) Remain 101:20:25 loss: 0.3046 Lr: 0.00200
[2025-09-06 14:31:45,176 INFO misc.py line 117 1395871] Train: [14/100][553/1462] Data 0.001 (0.001) Batch 2.819 (2.881) Remain 101:20:08 loss: 0.3252 Lr: 0.00200
[2025-09-06 14:31:48,174 INFO misc.py line 117 1395871] Train: [14/100][554/1462] Data 0.001 (0.001) Batch 2.999 (2.881) Remain 101:20:32 loss: 0.4661 Lr: 0.00200
[2025-09-06 14:31:51,138 INFO misc.py line 117 1395871] Train: [14/100][555/1462] Data 0.001 (0.001) Batch 2.964 (2.881) Remain 101:20:48 loss: 0.5380 Lr: 0.00200
[2025-09-06 14:31:53,943 INFO misc.py line 117 1395871] Train: [14/100][556/1462] Data 0.001 (0.001) Batch 2.805 (2.881) Remain 101:20:28 loss: 0.8301 Lr: 0.00200
[2025-09-06 14:31:56,728 INFO misc.py line 117 1395871] Train: [14/100][557/1462] Data 0.001 (0.001) Batch 2.785 (2.881) Remain 101:20:03 loss: 0.6864 Lr: 0.00200
[2025-09-06 14:32:00,071 INFO misc.py line 117 1395871] Train: [14/100][558/1462] Data 0.002 (0.001) Batch 3.344 (2.882) Remain 101:21:46 loss: 0.3474 Lr: 0.00200
[2025-09-06 14:32:02,845 INFO misc.py line 117 1395871] Train: [14/100][559/1462] Data 0.002 (0.001) Batch 2.773 (2.881) Remain 101:21:18 loss: 0.4259 Lr: 0.00200
[2025-09-06 14:32:05,683 INFO misc.py line 117 1395871] Train: [14/100][560/1462] Data 0.001 (0.001) Batch 2.838 (2.881) Remain 101:21:06 loss: 0.2992 Lr: 0.00200
[2025-09-06 14:32:08,667 INFO misc.py line 117 1395871] Train: [14/100][561/1462] Data 0.001 (0.001) Batch 2.984 (2.881) Remain 101:21:26 loss: 0.3957 Lr: 0.00200
[2025-09-06 14:32:11,787 INFO misc.py line 117 1395871] Train: [14/100][562/1462] Data 0.001 (0.001) Batch 3.120 (2.882) Remain 101:22:17 loss: 0.4321 Lr: 0.00200
[2025-09-06 14:32:14,670 INFO misc.py line 117 1395871] Train: [14/100][563/1462] Data 0.001 (0.001) Batch 2.883 (2.882) Remain 101:22:15 loss: 0.4760 Lr: 0.00200
[2025-09-06 14:32:17,585 INFO misc.py line 117 1395871] Train: [14/100][564/1462] Data 0.001 (0.001) Batch 2.915 (2.882) Remain 101:22:19 loss: 0.3290 Lr: 0.00200
[2025-09-06 14:32:20,539 INFO misc.py line 117 1395871] Train: [14/100][565/1462] Data 0.001 (0.001) Batch 2.953 (2.882) Remain 101:22:32 loss: 0.4770 Lr: 0.00200
[2025-09-06 14:32:23,165 INFO misc.py line 117 1395871] Train: [14/100][566/1462] Data 0.002 (0.001) Batch 2.626 (2.882) Remain 101:21:32 loss: 0.4476 Lr: 0.00200
[2025-09-06 14:32:25,893 INFO misc.py line 117 1395871] Train: [14/100][567/1462] Data 0.001 (0.001) Batch 2.729 (2.881) Remain 101:20:55 loss: 0.3408 Lr: 0.00200
[2025-09-06 14:32:28,622 INFO misc.py line 117 1395871] Train: [14/100][568/1462] Data 0.001 (0.001) Batch 2.729 (2.881) Remain 101:20:18 loss: 0.3415 Lr: 0.00200
[2025-09-06 14:32:31,573 INFO misc.py line 117 1395871] Train: [14/100][569/1462] Data 0.001 (0.001) Batch 2.951 (2.881) Remain 101:20:30 loss: 0.3476 Lr: 0.00200
[2025-09-06 14:32:34,376 INFO misc.py line 117 1395871] Train: [14/100][570/1462] Data 0.001 (0.001) Batch 2.803 (2.881) Remain 101:20:10 loss: 0.6461 Lr: 0.00200
[2025-09-06 14:32:37,303 INFO misc.py line 117 1395871] Train: [14/100][571/1462] Data 0.002 (0.001) Batch 2.927 (2.881) Remain 101:20:17 loss: 0.5752 Lr: 0.00200
[2025-09-06 14:32:40,232 INFO misc.py line 117 1395871] Train: [14/100][572/1462] Data 0.002 (0.001) Batch 2.929 (2.881) Remain 101:20:25 loss: 0.2050 Lr: 0.00200
[2025-09-06 14:32:43,072 INFO misc.py line 117 1395871] Train: [14/100][573/1462] Data 0.001 (0.001) Batch 2.840 (2.881) Remain 101:20:13 loss: 0.6267 Lr: 0.00200
[2025-09-06 14:32:46,044 INFO misc.py line 117 1395871] Train: [14/100][574/1462] Data 0.001 (0.001) Batch 2.972 (2.881) Remain 101:20:30 loss: 0.2107 Lr: 0.00200
[2025-09-06 14:32:49,185 INFO misc.py line 117 1395871] Train: [14/100][575/1462] Data 0.001 (0.001) Batch 3.141 (2.882) Remain 101:21:25 loss: 0.4778 Lr: 0.00200
[2025-09-06 14:32:52,203 INFO misc.py line 117 1395871] Train: [14/100][576/1462] Data 0.002 (0.001) Batch 3.018 (2.882) Remain 101:21:52 loss: 0.4567 Lr: 0.00200
[2025-09-06 14:32:55,370 INFO misc.py line 117 1395871] Train: [14/100][577/1462] Data 0.001 (0.001) Batch 3.167 (2.882) Remain 101:22:52 loss: 0.2677 Lr: 0.00200
[2025-09-06 14:32:58,742 INFO misc.py line 117 1395871] Train: [14/100][578/1462] Data 0.002 (0.001) Batch 3.373 (2.883) Remain 101:24:37 loss: 0.4454 Lr: 0.00200
[2025-09-06 14:33:01,726 INFO misc.py line 117 1395871] Train: [14/100][579/1462] Data 0.002 (0.001) Batch 2.984 (2.884) Remain 101:24:56 loss: 0.6108 Lr: 0.00200
[2025-09-06 14:33:04,260 INFO misc.py line 117 1395871] Train: [14/100][580/1462] Data 0.001 (0.001) Batch 2.535 (2.883) Remain 101:23:37 loss: 0.5752 Lr: 0.00200
[2025-09-06 14:33:07,313 INFO misc.py line 117 1395871] Train: [14/100][581/1462] Data 0.002 (0.001) Batch 3.053 (2.883) Remain 101:24:11 loss: 0.3592 Lr: 0.00200
[2025-09-06 14:33:10,310 INFO misc.py line 117 1395871] Train: [14/100][582/1462] Data 0.002 (0.001) Batch 2.996 (2.883) Remain 101:24:33 loss: 0.1542 Lr: 0.00200
[2025-09-06 14:33:12,992 INFO misc.py line 117 1395871] Train: [14/100][583/1462] Data 0.002 (0.001) Batch 2.682 (2.883) Remain 101:23:46 loss: 0.4438 Lr: 0.00200
[2025-09-06 14:33:15,992 INFO misc.py line 117 1395871] Train: [14/100][584/1462] Data 0.002 (0.001) Batch 3.000 (2.883) Remain 101:24:09 loss: 0.5285 Lr: 0.00200
[2025-09-06 14:33:18,882 INFO misc.py line 117 1395871] Train: [14/100][585/1462] Data 0.001 (0.001) Batch 2.890 (2.883) Remain 101:24:08 loss: 0.2919 Lr: 0.00200
[2025-09-06 14:33:21,862 INFO misc.py line 117 1395871] Train: [14/100][586/1462] Data 0.002 (0.001) Batch 2.979 (2.883) Remain 101:24:26 loss: 0.3259 Lr: 0.00200
[2025-09-06 14:33:24,621 INFO misc.py line 117 1395871] Train: [14/100][587/1462] Data 0.002 (0.001) Batch 2.760 (2.883) Remain 101:23:56 loss: 0.6045 Lr: 0.00200
[2025-09-06 14:33:27,837 INFO misc.py line 117 1395871] Train: [14/100][588/1462] Data 0.002 (0.001) Batch 3.215 (2.884) Remain 101:25:05 loss: 0.2744 Lr: 0.00200
[2025-09-06 14:33:30,650 INFO misc.py line 117 1395871] Train: [14/100][589/1462] Data 0.002 (0.001) Batch 2.813 (2.884) Remain 101:24:47 loss: 0.4070 Lr: 0.00200
[2025-09-06 14:33:33,318 INFO misc.py line 117 1395871] Train: [14/100][590/1462] Data 0.001 (0.001) Batch 2.668 (2.883) Remain 101:23:57 loss: 0.6371 Lr: 0.00200
[2025-09-06 14:33:36,399 INFO misc.py line 117 1395871] Train: [14/100][591/1462] Data 0.001 (0.001) Batch 3.081 (2.884) Remain 101:24:37 loss: 0.3595 Lr: 0.00200
[2025-09-06 14:33:39,416 INFO misc.py line 117 1395871] Train: [14/100][592/1462] Data 0.001 (0.001) Batch 3.016 (2.884) Remain 101:25:03 loss: 0.2345 Lr: 0.00200
[2025-09-06 14:33:42,223 INFO misc.py line 117 1395871] Train: [14/100][593/1462] Data 0.002 (0.001) Batch 2.807 (2.884) Remain 101:24:43 loss: 0.3929 Lr: 0.00200
[2025-09-06 14:33:44,868 INFO misc.py line 117 1395871] Train: [14/100][594/1462] Data 0.001 (0.001) Batch 2.645 (2.883) Remain 101:23:49 loss: 0.6921 Lr: 0.00200
[2025-09-06 14:33:47,534 INFO misc.py line 117 1395871] Train: [14/100][595/1462] Data 0.001 (0.001) Batch 2.666 (2.883) Remain 101:23:00 loss: 0.6455 Lr: 0.00200
[2025-09-06 14:33:50,366 INFO misc.py line 117 1395871] Train: [14/100][596/1462] Data 0.002 (0.001) Batch 2.832 (2.883) Remain 101:22:46 loss: 0.3829 Lr: 0.00200
[2025-09-06 14:33:53,282 INFO misc.py line 117 1395871] Train: [14/100][597/1462] Data 0.002 (0.001) Batch 2.916 (2.883) Remain 101:22:50 loss: 0.6475 Lr: 0.00200
[2025-09-06 14:33:56,350 INFO misc.py line 117 1395871] Train: [14/100][598/1462] Data 0.001 (0.001) Batch 3.068 (2.883) Remain 101:23:27 loss: 0.2977 Lr: 0.00200
[2025-09-06 14:33:59,021 INFO misc.py line 117 1395871] Train: [14/100][599/1462] Data 0.001 (0.001) Batch 2.670 (2.883) Remain 101:22:39 loss: 0.4050 Lr: 0.00200
[2025-09-06 14:34:01,956 INFO misc.py line 117 1395871] Train: [14/100][600/1462] Data 0.002 (0.001) Batch 2.936 (2.883) Remain 101:22:47 loss: 0.4333 Lr: 0.00200
[2025-09-06 14:34:04,831 INFO misc.py line 117 1395871] Train: [14/100][601/1462] Data 0.002 (0.001) Batch 2.875 (2.883) Remain 101:22:42 loss: 0.4133 Lr: 0.00200
[2025-09-06 14:34:07,636 INFO misc.py line 117 1395871] Train: [14/100][602/1462] Data 0.002 (0.001) Batch 2.806 (2.883) Remain 101:22:23 loss: 0.3760 Lr: 0.00200
[2025-09-06 14:34:10,521 INFO misc.py line 117 1395871] Train: [14/100][603/1462] Data 0.001 (0.001) Batch 2.884 (2.883) Remain 101:22:21 loss: 0.3613 Lr: 0.00200
[2025-09-06 14:34:13,655 INFO misc.py line 117 1395871] Train: [14/100][604/1462] Data 0.002 (0.001) Batch 3.135 (2.883) Remain 101:23:11 loss: 0.3892 Lr: 0.00200
[2025-09-06 14:34:16,756 INFO misc.py line 117 1395871] Train: [14/100][605/1462] Data 0.002 (0.001) Batch 3.101 (2.884) Remain 101:23:54 loss: 0.3519 Lr: 0.00200
[2025-09-06 14:34:19,490 INFO misc.py line 117 1395871] Train: [14/100][606/1462] Data 0.001 (0.001) Batch 2.734 (2.883) Remain 101:23:19 loss: 0.4900 Lr: 0.00200
[2025-09-06 14:34:22,353 INFO misc.py line 117 1395871] Train: [14/100][607/1462] Data 0.001 (0.001) Batch 2.863 (2.883) Remain 101:23:12 loss: 0.4043 Lr: 0.00200
[2025-09-06 14:34:25,341 INFO misc.py line 117 1395871] Train: [14/100][608/1462] Data 0.002 (0.001) Batch 2.988 (2.884) Remain 101:23:31 loss: 0.2755 Lr: 0.00200
[2025-09-06 14:34:28,316 INFO misc.py line 117 1395871] Train: [14/100][609/1462] Data 0.001 (0.001) Batch 2.975 (2.884) Remain 101:23:47 loss: 0.4185 Lr: 0.00200
[2025-09-06 14:34:31,105 INFO misc.py line 117 1395871] Train: [14/100][610/1462] Data 0.002 (0.001) Batch 2.789 (2.884) Remain 101:23:25 loss: 0.3033 Lr: 0.00200
[2025-09-06 14:34:34,169 INFO misc.py line 117 1395871] Train: [14/100][611/1462] Data 0.001 (0.001) Batch 3.064 (2.884) Remain 101:24:00 loss: 0.3004 Lr: 0.00200
[2025-09-06 14:34:36,852 INFO misc.py line 117 1395871] Train: [14/100][612/1462] Data 0.001 (0.001) Batch 2.683 (2.883) Remain 101:23:15 loss: 0.3701 Lr: 0.00200
[2025-09-06 14:34:39,804 INFO misc.py line 117 1395871] Train: [14/100][613/1462] Data 0.002 (0.001) Batch 2.952 (2.884) Remain 101:23:26 loss: 0.6042 Lr: 0.00200
[2025-09-06 14:34:42,512 INFO misc.py line 117 1395871] Train: [14/100][614/1462] Data 0.002 (0.001) Batch 2.708 (2.883) Remain 101:22:47 loss: 0.4742 Lr: 0.00200
[2025-09-06 14:34:45,330 INFO misc.py line 117 1395871] Train: [14/100][615/1462] Data 0.001 (0.001) Batch 2.818 (2.883) Remain 101:22:31 loss: 0.7748 Lr: 0.00200
[2025-09-06 14:34:47,939 INFO misc.py line 117 1395871] Train: [14/100][616/1462] Data 0.002 (0.001) Batch 2.609 (2.883) Remain 101:21:31 loss: 0.5949 Lr: 0.00200
[2025-09-06 14:34:50,587 INFO misc.py line 117 1395871] Train: [14/100][617/1462] Data 0.002 (0.001) Batch 2.648 (2.882) Remain 101:20:40 loss: 0.3210 Lr: 0.00200
[2025-09-06 14:34:53,268 INFO misc.py line 117 1395871] Train: [14/100][618/1462] Data 0.002 (0.001) Batch 2.680 (2.882) Remain 101:19:55 loss: 0.5750 Lr: 0.00200
[2025-09-06 14:34:56,231 INFO misc.py line 117 1395871] Train: [14/100][619/1462] Data 0.001 (0.001) Batch 2.963 (2.882) Remain 101:20:09 loss: 0.4186 Lr: 0.00200
[2025-09-06 14:34:59,346 INFO misc.py line 117 1395871] Train: [14/100][620/1462] Data 0.001 (0.001) Batch 3.115 (2.883) Remain 101:20:54 loss: 0.3598 Lr: 0.00200
[2025-09-06 14:35:02,147 INFO misc.py line 117 1395871] Train: [14/100][621/1462] Data 0.002 (0.001) Batch 2.801 (2.882) Remain 101:20:35 loss: 0.2992 Lr: 0.00200
[2025-09-06 14:35:04,721 INFO misc.py line 117 1395871] Train: [14/100][622/1462] Data 0.002 (0.001) Batch 2.574 (2.882) Remain 101:19:29 loss: 0.3669 Lr: 0.00200
[2025-09-06 14:35:07,705 INFO misc.py line 117 1395871] Train: [14/100][623/1462] Data 0.001 (0.001) Batch 2.983 (2.882) Remain 101:19:47 loss: 0.3825 Lr: 0.00200
[2025-09-06 14:35:10,592 INFO misc.py line 117 1395871] Train: [14/100][624/1462] Data 0.001 (0.001) Batch 2.887 (2.882) Remain 101:19:45 loss: 0.5258 Lr: 0.00200
[2025-09-06 14:35:13,343 INFO misc.py line 117 1395871] Train: [14/100][625/1462] Data 0.001 (0.001) Batch 2.751 (2.882) Remain 101:19:15 loss: 0.4765 Lr: 0.00200
[2025-09-06 14:35:16,038 INFO misc.py line 117 1395871] Train: [14/100][626/1462] Data 0.002 (0.001) Batch 2.695 (2.882) Remain 101:18:34 loss: 0.5114 Lr: 0.00200
[2025-09-06 14:35:19,111 INFO misc.py line 117 1395871] Train: [14/100][627/1462] Data 0.001 (0.001) Batch 3.072 (2.882) Remain 101:19:10 loss: 0.5556 Lr: 0.00200
[2025-09-06 14:35:22,024 INFO misc.py line 117 1395871] Train: [14/100][628/1462] Data 0.001 (0.001) Batch 2.913 (2.882) Remain 101:19:14 loss: 0.2275 Lr: 0.00200
[2025-09-06 14:35:24,773 INFO misc.py line 117 1395871] Train: [14/100][629/1462] Data 0.001 (0.001) Batch 2.749 (2.882) Remain 101:18:44 loss: 0.4488 Lr: 0.00200
[2025-09-06 14:35:27,830 INFO misc.py line 117 1395871] Train: [14/100][630/1462] Data 0.002 (0.001) Batch 3.057 (2.882) Remain 101:19:16 loss: 0.2808 Lr: 0.00200
[2025-09-06 14:35:30,883 INFO misc.py line 117 1395871] Train: [14/100][631/1462] Data 0.001 (0.001) Batch 3.053 (2.882) Remain 101:19:48 loss: 0.5155 Lr: 0.00200
[2025-09-06 14:35:33,860 INFO misc.py line 117 1395871] Train: [14/100][632/1462] Data 0.001 (0.001) Batch 2.977 (2.882) Remain 101:20:04 loss: 0.3774 Lr: 0.00200
[2025-09-06 14:35:36,686 INFO misc.py line 117 1395871] Train: [14/100][633/1462] Data 0.001 (0.001) Batch 2.826 (2.882) Remain 101:19:50 loss: 0.5297 Lr: 0.00200
[2025-09-06 14:35:39,451 INFO misc.py line 117 1395871] Train: [14/100][634/1462] Data 0.001 (0.001) Batch 2.766 (2.882) Remain 101:19:24 loss: 0.3536 Lr: 0.00200
[2025-09-06 14:35:42,309 INFO misc.py line 117 1395871] Train: [14/100][635/1462] Data 0.001 (0.001) Batch 2.858 (2.882) Remain 101:19:16 loss: 0.8257 Lr: 0.00200
[2025-09-06 14:35:45,315 INFO misc.py line 117 1395871] Train: [14/100][636/1462] Data 0.002 (0.001) Batch 3.006 (2.882) Remain 101:19:38 loss: 0.5120 Lr: 0.00200
[2025-09-06 14:35:48,318 INFO misc.py line 117 1395871] Train: [14/100][637/1462] Data 0.002 (0.001) Batch 3.002 (2.882) Remain 101:19:59 loss: 0.3482 Lr: 0.00200
[2025-09-06 14:35:51,098 INFO misc.py line 117 1395871] Train: [14/100][638/1462] Data 0.001 (0.001) Batch 2.781 (2.882) Remain 101:19:36 loss: 0.3152 Lr: 0.00200
[2025-09-06 14:35:54,053 INFO misc.py line 117 1395871] Train: [14/100][639/1462] Data 0.002 (0.001) Batch 2.955 (2.882) Remain 101:19:47 loss: 0.2995 Lr: 0.00200
[2025-09-06 14:35:56,824 INFO misc.py line 117 1395871] Train: [14/100][640/1462] Data 0.001 (0.001) Batch 2.771 (2.882) Remain 101:19:22 loss: 0.5633 Lr: 0.00200
[2025-09-06 14:35:59,685 INFO misc.py line 117 1395871] Train: [14/100][641/1462] Data 0.001 (0.001) Batch 2.860 (2.882) Remain 101:19:15 loss: 0.4773 Lr: 0.00200
[2025-09-06 14:36:02,594 INFO misc.py line 117 1395871] Train: [14/100][642/1462] Data 0.002 (0.001) Batch 2.910 (2.882) Remain 101:19:18 loss: 0.3477 Lr: 0.00200
[2025-09-06 14:36:05,624 INFO misc.py line 117 1395871] Train: [14/100][643/1462] Data 0.002 (0.001) Batch 3.030 (2.883) Remain 101:19:44 loss: 0.4119 Lr: 0.00200
[2025-09-06 14:36:08,205 INFO misc.py line 117 1395871] Train: [14/100][644/1462] Data 0.002 (0.001) Batch 2.580 (2.882) Remain 101:18:41 loss: 0.3565 Lr: 0.00200
[2025-09-06 14:36:10,914 INFO misc.py line 117 1395871] Train: [14/100][645/1462] Data 0.002 (0.001) Batch 2.709 (2.882) Remain 101:18:04 loss: 0.4570 Lr: 0.00200
[2025-09-06 14:36:13,958 INFO misc.py line 117 1395871] Train: [14/100][646/1462] Data 0.001 (0.001) Batch 3.044 (2.882) Remain 101:18:33 loss: 0.2994 Lr: 0.00200
[2025-09-06 14:36:16,630 INFO misc.py line 117 1395871] Train: [14/100][647/1462] Data 0.002 (0.001) Batch 2.672 (2.882) Remain 101:17:49 loss: 0.4300 Lr: 0.00200
[2025-09-06 14:36:19,598 INFO misc.py line 117 1395871] Train: [14/100][648/1462] Data 0.001 (0.001) Batch 2.969 (2.882) Remain 101:18:04 loss: 0.3123 Lr: 0.00200
[2025-09-06 14:36:22,376 INFO misc.py line 117 1395871] Train: [14/100][649/1462] Data 0.002 (0.001) Batch 2.778 (2.882) Remain 101:17:40 loss: 0.3565 Lr: 0.00200
[2025-09-06 14:36:25,239 INFO misc.py line 117 1395871] Train: [14/100][650/1462] Data 0.002 (0.001) Batch 2.863 (2.882) Remain 101:17:34 loss: 0.3163 Lr: 0.00200
[2025-09-06 14:36:28,271 INFO misc.py line 117 1395871] Train: [14/100][651/1462] Data 0.001 (0.001) Batch 3.032 (2.882) Remain 101:18:00 loss: 0.2983 Lr: 0.00200
[2025-09-06 14:36:30,922 INFO misc.py line 117 1395871] Train: [14/100][652/1462] Data 0.002 (0.001) Batch 2.652 (2.882) Remain 101:17:12 loss: 0.7298 Lr: 0.00200
[2025-09-06 14:36:33,771 INFO misc.py line 117 1395871] Train: [14/100][653/1462] Data 0.002 (0.001) Batch 2.848 (2.881) Remain 101:17:03 loss: 0.4649 Lr: 0.00200
[2025-09-06 14:36:36,832 INFO misc.py line 117 1395871] Train: [14/100][654/1462] Data 0.002 (0.001) Batch 3.061 (2.882) Remain 101:17:35 loss: 0.3612 Lr: 0.00200
[2025-09-06 14:36:39,630 INFO misc.py line 117 1395871] Train: [14/100][655/1462] Data 0.001 (0.001) Batch 2.798 (2.882) Remain 101:17:16 loss: 0.4942 Lr: 0.00200
[2025-09-06 14:36:42,289 INFO misc.py line 117 1395871] Train: [14/100][656/1462] Data 0.002 (0.001) Batch 2.659 (2.881) Remain 101:16:30 loss: 0.2652 Lr: 0.00200
[2025-09-06 14:36:45,614 INFO misc.py line 117 1395871] Train: [14/100][657/1462] Data 0.001 (0.001) Batch 3.325 (2.882) Remain 101:17:53 loss: 0.3333 Lr: 0.00200
[2025-09-06 14:36:48,135 INFO misc.py line 117 1395871] Train: [14/100][658/1462] Data 0.001 (0.001) Batch 2.521 (2.881) Remain 101:16:40 loss: 0.4839 Lr: 0.00200
[2025-09-06 14:36:50,654 INFO misc.py line 117 1395871] Train: [14/100][659/1462] Data 0.002 (0.001) Batch 2.518 (2.881) Remain 101:15:27 loss: 0.4903 Lr: 0.00200
[2025-09-06 14:36:53,469 INFO misc.py line 117 1395871] Train: [14/100][660/1462] Data 0.001 (0.001) Batch 2.815 (2.881) Remain 101:15:12 loss: 0.3122 Lr: 0.00200
[2025-09-06 14:36:56,580 INFO misc.py line 117 1395871] Train: [14/100][661/1462] Data 0.002 (0.001) Batch 3.112 (2.881) Remain 101:15:53 loss: 0.6001 Lr: 0.00200
[2025-09-06 14:36:59,393 INFO misc.py line 117 1395871] Train: [14/100][662/1462] Data 0.001 (0.001) Batch 2.813 (2.881) Remain 101:15:37 loss: 0.5451 Lr: 0.00200
[2025-09-06 14:37:02,293 INFO misc.py line 117 1395871] Train: [14/100][663/1462] Data 0.002 (0.001) Batch 2.900 (2.881) Remain 101:15:38 loss: 0.2988 Lr: 0.00200
[2025-09-06 14:37:05,096 INFO misc.py line 117 1395871] Train: [14/100][664/1462] Data 0.001 (0.001) Batch 2.803 (2.881) Remain 101:15:20 loss: 0.6005 Lr: 0.00200
[2025-09-06 14:37:08,400 INFO misc.py line 117 1395871] Train: [14/100][665/1462] Data 0.001 (0.001) Batch 3.305 (2.882) Remain 101:16:38 loss: 0.2889 Lr: 0.00200
[2025-09-06 14:37:11,298 INFO misc.py line 117 1395871] Train: [14/100][666/1462] Data 0.001 (0.001) Batch 2.898 (2.882) Remain 101:16:39 loss: 0.3260 Lr: 0.00200
[2025-09-06 14:37:14,292 INFO misc.py line 117 1395871] Train: [14/100][667/1462] Data 0.002 (0.001) Batch 2.993 (2.882) Remain 101:16:57 loss: 0.5098 Lr: 0.00200
[2025-09-06 14:37:17,128 INFO misc.py line 117 1395871] Train: [14/100][668/1462] Data 0.003 (0.001) Batch 2.837 (2.882) Remain 101:16:46 loss: 0.4573 Lr: 0.00200
[2025-09-06 14:37:20,066 INFO misc.py line 117 1395871] Train: [14/100][669/1462] Data 0.002 (0.001) Batch 2.937 (2.882) Remain 101:16:53 loss: 0.8538 Lr: 0.00200
[2025-09-06 14:37:22,861 INFO misc.py line 117 1395871] Train: [14/100][670/1462] Data 0.002 (0.001) Batch 2.795 (2.882) Remain 101:16:34 loss: 0.1932 Lr: 0.00200
[2025-09-06 14:37:25,773 INFO misc.py line 117 1395871] Train: [14/100][671/1462] Data 0.001 (0.001) Batch 2.912 (2.882) Remain 101:16:37 loss: 0.2816 Lr: 0.00200
[2025-09-06 14:37:28,575 INFO misc.py line 117 1395871] Train: [14/100][672/1462] Data 0.002 (0.001) Batch 2.802 (2.882) Remain 101:16:19 loss: 0.6846 Lr: 0.00200
[2025-09-06 14:37:31,300 INFO misc.py line 117 1395871] Train: [14/100][673/1462] Data 0.001 (0.001) Batch 2.725 (2.881) Remain 101:15:46 loss: 0.5499 Lr: 0.00200
[2025-09-06 14:37:34,003 INFO misc.py line 117 1395871] Train: [14/100][674/1462] Data 0.001 (0.001) Batch 2.703 (2.881) Remain 101:15:10 loss: 0.3983 Lr: 0.00200
[2025-09-06 14:37:36,967 INFO misc.py line 117 1395871] Train: [14/100][675/1462] Data 0.002 (0.001) Batch 2.964 (2.881) Remain 101:15:23 loss: 0.4227 Lr: 0.00200
[2025-09-06 14:37:40,254 INFO misc.py line 117 1395871] Train: [14/100][676/1462] Data 0.002 (0.001) Batch 3.286 (2.882) Remain 101:16:36 loss: 0.3412 Lr: 0.00200
[2025-09-06 14:37:43,026 INFO misc.py line 117 1395871] Train: [14/100][677/1462] Data 0.002 (0.001) Batch 2.772 (2.882) Remain 101:16:12 loss: 0.5185 Lr: 0.00200
[2025-09-06 14:37:45,878 INFO misc.py line 117 1395871] Train: [14/100][678/1462] Data 0.002 (0.001) Batch 2.853 (2.882) Remain 101:16:04 loss: 0.4624 Lr: 0.00200
[2025-09-06 14:37:48,855 INFO misc.py line 117 1395871] Train: [14/100][679/1462] Data 0.002 (0.001) Batch 2.977 (2.882) Remain 101:16:19 loss: 0.2772 Lr: 0.00200
[2025-09-06 14:37:51,628 INFO misc.py line 117 1395871] Train: [14/100][680/1462] Data 0.002 (0.001) Batch 2.773 (2.882) Remain 101:15:56 loss: 0.4266 Lr: 0.00200
[2025-09-06 14:37:54,256 INFO misc.py line 117 1395871] Train: [14/100][681/1462] Data 0.002 (0.001) Batch 2.628 (2.881) Remain 101:15:06 loss: 0.3842 Lr: 0.00200
[2025-09-06 14:37:57,097 INFO misc.py line 117 1395871] Train: [14/100][682/1462] Data 0.002 (0.001) Batch 2.841 (2.881) Remain 101:14:55 loss: 0.5011 Lr: 0.00200
[2025-09-06 14:37:59,861 INFO misc.py line 117 1395871] Train: [14/100][683/1462] Data 0.001 (0.001) Batch 2.764 (2.881) Remain 101:14:31 loss: 0.4654 Lr: 0.00200
[2025-09-06 14:38:03,031 INFO misc.py line 117 1395871] Train: [14/100][684/1462] Data 0.001 (0.001) Batch 3.169 (2.881) Remain 101:15:21 loss: 0.7554 Lr: 0.00200
[2025-09-06 14:38:06,288 INFO misc.py line 117 1395871] Train: [14/100][685/1462] Data 0.002 (0.001) Batch 3.258 (2.882) Remain 101:16:28 loss: 0.7192 Lr: 0.00200
[2025-09-06 14:38:09,056 INFO misc.py line 117 1395871] Train: [14/100][686/1462] Data 0.002 (0.001) Batch 2.768 (2.882) Remain 101:16:04 loss: 0.5128 Lr: 0.00200
[2025-09-06 14:38:11,972 INFO misc.py line 117 1395871] Train: [14/100][687/1462] Data 0.001 (0.001) Batch 2.915 (2.882) Remain 101:16:08 loss: 0.2701 Lr: 0.00200
[2025-09-06 14:38:14,594 INFO misc.py line 117 1395871] Train: [14/100][688/1462] Data 0.001 (0.001) Batch 2.622 (2.881) Remain 101:15:17 loss: 0.4013 Lr: 0.00200
[2025-09-06 14:38:17,554 INFO misc.py line 117 1395871] Train: [14/100][689/1462] Data 0.002 (0.001) Batch 2.960 (2.882) Remain 101:15:28 loss: 0.5201 Lr: 0.00200
[2025-09-06 14:38:20,447 INFO misc.py line 117 1395871] Train: [14/100][690/1462] Data 0.001 (0.001) Batch 2.893 (2.882) Remain 101:15:28 loss: 0.2587 Lr: 0.00200
[2025-09-06 14:38:23,154 INFO misc.py line 117 1395871] Train: [14/100][691/1462] Data 0.002 (0.001) Batch 2.707 (2.881) Remain 101:14:53 loss: 0.4227 Lr: 0.00200
[2025-09-06 14:38:25,965 INFO misc.py line 117 1395871] Train: [14/100][692/1462] Data 0.002 (0.001) Batch 2.810 (2.881) Remain 101:14:37 loss: 0.3374 Lr: 0.00200
[2025-09-06 14:38:28,679 INFO misc.py line 117 1395871] Train: [14/100][693/1462] Data 0.002 (0.001) Batch 2.715 (2.881) Remain 101:14:04 loss: 0.2775 Lr: 0.00200
[2025-09-06 14:38:31,435 INFO misc.py line 117 1395871] Train: [14/100][694/1462] Data 0.002 (0.001) Batch 2.756 (2.881) Remain 101:13:38 loss: 0.3436 Lr: 0.00200
[2025-09-06 14:38:34,549 INFO misc.py line 117 1395871] Train: [14/100][695/1462] Data 0.001 (0.001) Batch 3.113 (2.881) Remain 101:14:17 loss: 0.2594 Lr: 0.00200
[2025-09-06 14:38:37,664 INFO misc.py line 117 1395871] Train: [14/100][696/1462] Data 0.001 (0.001) Batch 3.115 (2.881) Remain 101:14:57 loss: 0.5220 Lr: 0.00200
[2025-09-06 14:38:40,313 INFO misc.py line 117 1395871] Train: [14/100][697/1462] Data 0.001 (0.001) Batch 2.649 (2.881) Remain 101:14:12 loss: 0.5944 Lr: 0.00200
[2025-09-06 14:38:43,072 INFO misc.py line 117 1395871] Train: [14/100][698/1462] Data 0.002 (0.001) Batch 2.759 (2.881) Remain 101:13:47 loss: 0.3159 Lr: 0.00200
[2025-09-06 14:38:46,047 INFO misc.py line 117 1395871] Train: [14/100][699/1462] Data 0.001 (0.001) Batch 2.975 (2.881) Remain 101:14:01 loss: 0.3895 Lr: 0.00200
[2025-09-06 14:38:48,572 INFO misc.py line 117 1395871] Train: [14/100][700/1462] Data 0.001 (0.001) Batch 2.525 (2.881) Remain 101:12:54 loss: 0.5221 Lr: 0.00200
[2025-09-06 14:38:51,409 INFO misc.py line 117 1395871] Train: [14/100][701/1462] Data 0.002 (0.001) Batch 2.837 (2.881) Remain 101:12:43 loss: 0.6166 Lr: 0.00200
[2025-09-06 14:38:54,120 INFO misc.py line 117 1395871] Train: [14/100][702/1462] Data 0.001 (0.001) Batch 2.711 (2.880) Remain 101:12:09 loss: 0.3073 Lr: 0.00200
[2025-09-06 14:38:57,006 INFO misc.py line 117 1395871] Train: [14/100][703/1462] Data 0.001 (0.001) Batch 2.886 (2.880) Remain 101:12:07 loss: 0.4879 Lr: 0.00200
[2025-09-06 14:39:00,042 INFO misc.py line 117 1395871] Train: [14/100][704/1462] Data 0.001 (0.001) Batch 3.036 (2.880) Remain 101:12:33 loss: 0.5160 Lr: 0.00200
[2025-09-06 14:39:02,995 INFO misc.py line 117 1395871] Train: [14/100][705/1462] Data 0.001 (0.001) Batch 2.953 (2.881) Remain 101:12:43 loss: 0.4051 Lr: 0.00200
[2025-09-06 14:39:06,114 INFO misc.py line 117 1395871] Train: [14/100][706/1462] Data 0.001 (0.001) Batch 3.119 (2.881) Remain 101:13:23 loss: 1.8240 Lr: 0.00200
[2025-09-06 14:39:09,123 INFO misc.py line 117 1395871] Train: [14/100][707/1462] Data 0.002 (0.001) Batch 3.010 (2.881) Remain 101:13:43 loss: 0.2579 Lr: 0.00200
[2025-09-06 14:39:12,138 INFO misc.py line 117 1395871] Train: [14/100][708/1462] Data 0.002 (0.001) Batch 3.015 (2.881) Remain 101:14:04 loss: 0.4309 Lr: 0.00200
[2025-09-06 14:39:14,912 INFO misc.py line 117 1395871] Train: [14/100][709/1462] Data 0.001 (0.001) Batch 2.774 (2.881) Remain 101:13:42 loss: 0.5082 Lr: 0.00200
[2025-09-06 14:39:17,717 INFO misc.py line 117 1395871] Train: [14/100][710/1462] Data 0.001 (0.001) Batch 2.805 (2.881) Remain 101:13:25 loss: 0.7705 Lr: 0.00200
[2025-09-06 14:39:20,520 INFO misc.py line 117 1395871] Train: [14/100][711/1462] Data 0.002 (0.001) Batch 2.803 (2.881) Remain 101:13:09 loss: 0.4448 Lr: 0.00200
[2025-09-06 14:39:23,361 INFO misc.py line 117 1395871] Train: [14/100][712/1462] Data 0.002 (0.001) Batch 2.841 (2.881) Remain 101:12:59 loss: 0.3431 Lr: 0.00200
[2025-09-06 14:39:26,218 INFO misc.py line 117 1395871] Train: [14/100][713/1462] Data 0.002 (0.001) Batch 2.857 (2.881) Remain 101:12:51 loss: 0.4964 Lr: 0.00200
[2025-09-06 14:39:29,368 INFO misc.py line 117 1395871] Train: [14/100][714/1462] Data 0.001 (0.001) Batch 3.150 (2.881) Remain 101:13:37 loss: 0.4468 Lr: 0.00200
[2025-09-06 14:39:32,214 INFO misc.py line 117 1395871] Train: [14/100][715/1462] Data 0.001 (0.001) Batch 2.846 (2.881) Remain 101:13:27 loss: 0.2612 Lr: 0.00200
[2025-09-06 14:39:35,319 INFO misc.py line 117 1395871] Train: [14/100][716/1462] Data 0.001 (0.001) Batch 3.105 (2.881) Remain 101:14:04 loss: 0.3948 Lr: 0.00200
[2025-09-06 14:39:38,163 INFO misc.py line 117 1395871] Train: [14/100][717/1462] Data 0.001 (0.001) Batch 2.844 (2.881) Remain 101:13:55 loss: 0.5351 Lr: 0.00200
[2025-09-06 14:39:41,198 INFO misc.py line 117 1395871] Train: [14/100][718/1462] Data 0.001 (0.001) Batch 3.034 (2.882) Remain 101:14:19 loss: 1.0175 Lr: 0.00200
[2025-09-06 14:39:43,740 INFO misc.py line 117 1395871] Train: [14/100][719/1462] Data 0.001 (0.001) Batch 2.543 (2.881) Remain 101:13:16 loss: 0.4363 Lr: 0.00200
[2025-09-06 14:39:46,808 INFO misc.py line 117 1395871] Train: [14/100][720/1462] Data 0.001 (0.001) Batch 3.067 (2.881) Remain 101:13:46 loss: 0.3550 Lr: 0.00200
[2025-09-06 14:39:49,545 INFO misc.py line 117 1395871] Train: [14/100][721/1462] Data 0.001 (0.001) Batch 2.737 (2.881) Remain 101:13:18 loss: 0.4884 Lr: 0.00200
[2025-09-06 14:39:52,645 INFO misc.py line 117 1395871] Train: [14/100][722/1462] Data 0.001 (0.001) Batch 3.099 (2.882) Remain 101:13:53 loss: 0.5921 Lr: 0.00200
[2025-09-06 14:39:55,462 INFO misc.py line 117 1395871] Train: [14/100][723/1462] Data 0.001 (0.001) Batch 2.817 (2.881) Remain 101:13:39 loss: 0.4159 Lr: 0.00200
[2025-09-06 14:39:58,470 INFO misc.py line 117 1395871] Train: [14/100][724/1462] Data 0.002 (0.001) Batch 3.008 (2.882) Remain 101:13:58 loss: 0.5140 Lr: 0.00200
[2025-09-06 14:40:01,676 INFO misc.py line 117 1395871] Train: [14/100][725/1462] Data 0.002 (0.001) Batch 3.207 (2.882) Remain 101:14:53 loss: 0.7918 Lr: 0.00200
[2025-09-06 14:40:04,327 INFO misc.py line 117 1395871] Train: [14/100][726/1462] Data 0.001 (0.001) Batch 2.650 (2.882) Remain 101:14:09 loss: 0.3229 Lr: 0.00200
[2025-09-06 14:40:07,088 INFO misc.py line 117 1395871] Train: [14/100][727/1462] Data 0.001 (0.001) Batch 2.761 (2.882) Remain 101:13:45 loss: 0.3848 Lr: 0.00200
[2025-09-06 14:40:10,175 INFO misc.py line 117 1395871] Train: [14/100][728/1462] Data 0.002 (0.001) Batch 3.087 (2.882) Remain 101:14:18 loss: 0.5406 Lr: 0.00200
[2025-09-06 14:40:13,218 INFO misc.py line 117 1395871] Train: [14/100][729/1462] Data 0.001 (0.001) Batch 3.043 (2.882) Remain 101:14:43 loss: 0.3942 Lr: 0.00200
[2025-09-06 14:40:16,097 INFO misc.py line 117 1395871] Train: [14/100][730/1462] Data 0.002 (0.001) Batch 2.879 (2.882) Remain 101:14:40 loss: 0.5878 Lr: 0.00200
[2025-09-06 14:40:18,905 INFO misc.py line 117 1395871] Train: [14/100][731/1462] Data 0.001 (0.001) Batch 2.808 (2.882) Remain 101:14:24 loss: 0.7142 Lr: 0.00200
[2025-09-06 14:40:21,952 INFO misc.py line 117 1395871] Train: [14/100][732/1462] Data 0.002 (0.001) Batch 3.047 (2.882) Remain 101:14:50 loss: 0.5774 Lr: 0.00200
[2025-09-06 14:40:25,027 INFO misc.py line 117 1395871] Train: [14/100][733/1462] Data 0.001 (0.001) Batch 3.075 (2.882) Remain 101:15:20 loss: 0.5689 Lr: 0.00200
[2025-09-06 14:40:27,805 INFO misc.py line 117 1395871] Train: [14/100][734/1462] Data 0.001 (0.001) Batch 2.778 (2.882) Remain 101:14:59 loss: 1.1299 Lr: 0.00200
[2025-09-06 14:40:30,410 INFO misc.py line 117 1395871] Train: [14/100][735/1462] Data 0.001 (0.001) Batch 2.606 (2.882) Remain 101:14:09 loss: 0.9584 Lr: 0.00200
[2025-09-06 14:40:32,961 INFO misc.py line 117 1395871] Train: [14/100][736/1462] Data 0.002 (0.001) Batch 2.551 (2.882) Remain 101:13:09 loss: 0.3664 Lr: 0.00200
[2025-09-06 14:40:35,786 INFO misc.py line 117 1395871] Train: [14/100][737/1462] Data 0.001 (0.001) Batch 2.825 (2.881) Remain 101:12:56 loss: 0.4583 Lr: 0.00200
[2025-09-06 14:40:38,580 INFO misc.py line 117 1395871] Train: [14/100][738/1462] Data 0.001 (0.001) Batch 2.794 (2.881) Remain 101:12:38 loss: 1.2713 Lr: 0.00200
[2025-09-06 14:40:41,901 INFO misc.py line 117 1395871] Train: [14/100][739/1462] Data 0.001 (0.001) Batch 3.321 (2.882) Remain 101:13:51 loss: 0.5031 Lr: 0.00200
[2025-09-06 14:40:44,230 INFO misc.py line 117 1395871] Train: [14/100][740/1462] Data 0.001 (0.001) Batch 2.330 (2.881) Remain 101:12:13 loss: 0.3667 Lr: 0.00200
[2025-09-06 14:40:47,165 INFO misc.py line 117 1395871] Train: [14/100][741/1462] Data 0.001 (0.001) Batch 2.935 (2.881) Remain 101:12:19 loss: 0.5160 Lr: 0.00200
[2025-09-06 14:40:50,238 INFO misc.py line 117 1395871] Train: [14/100][742/1462] Data 0.001 (0.001) Batch 3.073 (2.881) Remain 101:12:49 loss: 0.4357 Lr: 0.00200
[2025-09-06 14:40:53,234 INFO misc.py line 117 1395871] Train: [14/100][743/1462] Data 0.001 (0.001) Batch 2.996 (2.882) Remain 101:13:06 loss: 0.4678 Lr: 0.00200
[2025-09-06 14:40:56,231 INFO misc.py line 117 1395871] Train: [14/100][744/1462] Data 0.001 (0.001) Batch 2.997 (2.882) Remain 101:13:23 loss: 0.5544 Lr: 0.00200
[2025-09-06 14:40:59,015 INFO misc.py line 117 1395871] Train: [14/100][745/1462] Data 0.002 (0.001) Batch 2.784 (2.882) Remain 101:13:03 loss: 0.5376 Lr: 0.00200
[2025-09-06 14:41:01,583 INFO misc.py line 117 1395871] Train: [14/100][746/1462] Data 0.002 (0.001) Batch 2.568 (2.881) Remain 101:12:07 loss: 0.5258 Lr: 0.00200
[2025-09-06 14:41:04,509 INFO misc.py line 117 1395871] Train: [14/100][747/1462] Data 0.002 (0.001) Batch 2.927 (2.881) Remain 101:12:12 loss: 0.3616 Lr: 0.00200
[2025-09-06 14:41:07,256 INFO misc.py line 117 1395871] Train: [14/100][748/1462] Data 0.001 (0.001) Batch 2.747 (2.881) Remain 101:11:46 loss: 0.5874 Lr: 0.00200
[2025-09-06 14:41:09,978 INFO misc.py line 117 1395871] Train: [14/100][749/1462] Data 0.001 (0.001) Batch 2.722 (2.881) Remain 101:11:16 loss: 0.5918 Lr: 0.00200
[2025-09-06 14:41:13,048 INFO misc.py line 117 1395871] Train: [14/100][750/1462] Data 0.001 (0.001) Batch 3.070 (2.881) Remain 101:11:45 loss: 0.6536 Lr: 0.00200
[2025-09-06 14:41:15,447 INFO misc.py line 117 1395871] Train: [14/100][751/1462] Data 0.002 (0.001) Batch 2.399 (2.881) Remain 101:10:21 loss: 0.3457 Lr: 0.00200
[2025-09-06 14:41:18,375 INFO misc.py line 117 1395871] Train: [14/100][752/1462] Data 0.002 (0.001) Batch 2.927 (2.881) Remain 101:10:26 loss: 0.3475 Lr: 0.00200
[2025-09-06 14:41:21,127 INFO misc.py line 117 1395871] Train: [14/100][753/1462] Data 0.001 (0.001) Batch 2.752 (2.880) Remain 101:10:01 loss: 0.3231 Lr: 0.00200
[2025-09-06 14:41:23,851 INFO misc.py line 117 1395871] Train: [14/100][754/1462] Data 0.001 (0.001) Batch 2.725 (2.880) Remain 101:09:32 loss: 0.3367 Lr: 0.00200
[2025-09-06 14:41:26,693 INFO misc.py line 117 1395871] Train: [14/100][755/1462] Data 0.001 (0.001) Batch 2.841 (2.880) Remain 101:09:23 loss: 0.4606 Lr: 0.00200
[2025-09-06 14:41:29,819 INFO misc.py line 117 1395871] Train: [14/100][756/1462] Data 0.002 (0.001) Batch 3.126 (2.880) Remain 101:10:01 loss: 0.4123 Lr: 0.00200
[2025-09-06 14:41:32,643 INFO misc.py line 117 1395871] Train: [14/100][757/1462] Data 0.001 (0.001) Batch 2.825 (2.880) Remain 101:09:49 loss: 0.3468 Lr: 0.00200
[2025-09-06 14:41:35,507 INFO misc.py line 117 1395871] Train: [14/100][758/1462] Data 0.001 (0.001) Batch 2.863 (2.880) Remain 101:09:43 loss: 0.5927 Lr: 0.00200
[2025-09-06 14:41:38,269 INFO misc.py line 117 1395871] Train: [14/100][759/1462] Data 0.002 (0.001) Batch 2.762 (2.880) Remain 101:09:21 loss: 0.2924 Lr: 0.00200
[2025-09-06 14:41:41,255 INFO misc.py line 117 1395871] Train: [14/100][760/1462] Data 0.001 (0.001) Batch 2.986 (2.880) Remain 101:09:35 loss: 0.3780 Lr: 0.00200
[2025-09-06 14:41:44,638 INFO misc.py line 117 1395871] Train: [14/100][761/1462] Data 0.001 (0.001) Batch 3.383 (2.881) Remain 101:10:56 loss: 0.2747 Lr: 0.00200
[2025-09-06 14:41:47,353 INFO misc.py line 117 1395871] Train: [14/100][762/1462] Data 0.001 (0.001) Batch 2.715 (2.881) Remain 101:10:26 loss: 0.4870 Lr: 0.00200
[2025-09-06 14:41:50,231 INFO misc.py line 117 1395871] Train: [14/100][763/1462] Data 0.001 (0.001) Batch 2.878 (2.881) Remain 101:10:23 loss: 0.5085 Lr: 0.00200
[2025-09-06 14:41:53,288 INFO misc.py line 117 1395871] Train: [14/100][764/1462] Data 0.001 (0.001) Batch 3.057 (2.881) Remain 101:10:49 loss: 0.2855 Lr: 0.00200
[2025-09-06 14:41:56,269 INFO misc.py line 117 1395871] Train: [14/100][765/1462] Data 0.001 (0.001) Batch 2.981 (2.881) Remain 101:11:03 loss: 0.3602 Lr: 0.00200
[2025-09-06 14:41:59,308 INFO misc.py line 117 1395871] Train: [14/100][766/1462] Data 0.002 (0.001) Batch 3.039 (2.881) Remain 101:11:26 loss: 0.5626 Lr: 0.00200
[2025-09-06 14:42:02,292 INFO misc.py line 117 1395871] Train: [14/100][767/1462] Data 0.002 (0.001) Batch 2.984 (2.882) Remain 101:11:40 loss: 0.3734 Lr: 0.00200
[2025-09-06 14:42:04,950 INFO misc.py line 117 1395871] Train: [14/100][768/1462] Data 0.001 (0.001) Batch 2.657 (2.881) Remain 101:11:00 loss: 0.3137 Lr: 0.00200
[2025-09-06 14:42:07,761 INFO misc.py line 117 1395871] Train: [14/100][769/1462] Data 0.001 (0.001) Batch 2.811 (2.881) Remain 101:10:46 loss: 0.6811 Lr: 0.00200
[2025-09-06 14:42:10,471 INFO misc.py line 117 1395871] Train: [14/100][770/1462] Data 0.002 (0.001) Batch 2.710 (2.881) Remain 101:10:15 loss: 0.3566 Lr: 0.00200
[2025-09-06 14:42:13,594 INFO misc.py line 117 1395871] Train: [14/100][771/1462] Data 0.001 (0.001) Batch 3.123 (2.881) Remain 101:10:52 loss: 0.3420 Lr: 0.00200
[2025-09-06 14:42:16,269 INFO misc.py line 117 1395871] Train: [14/100][772/1462] Data 0.001 (0.001) Batch 2.674 (2.881) Remain 101:10:15 loss: 0.3049 Lr: 0.00200
[2025-09-06 14:42:19,133 INFO misc.py line 117 1395871] Train: [14/100][773/1462] Data 0.001 (0.001) Batch 2.865 (2.881) Remain 101:10:09 loss: 0.4706 Lr: 0.00200
[2025-09-06 14:42:22,536 INFO misc.py line 117 1395871] Train: [14/100][774/1462] Data 0.001 (0.001) Batch 3.403 (2.882) Remain 101:11:32 loss: 0.8681 Lr: 0.00200
[2025-09-06 14:42:25,387 INFO misc.py line 117 1395871] Train: [14/100][775/1462] Data 0.001 (0.001) Batch 2.851 (2.882) Remain 101:11:24 loss: 0.5480 Lr: 0.00200
[2025-09-06 14:42:28,100 INFO misc.py line 117 1395871] Train: [14/100][776/1462] Data 0.001 (0.001) Batch 2.713 (2.881) Remain 101:10:54 loss: 0.3399 Lr: 0.00200
[2025-09-06 14:42:31,590 INFO misc.py line 117 1395871] Train: [14/100][777/1462] Data 0.002 (0.001) Batch 3.490 (2.882) Remain 101:12:30 loss: 0.2649 Lr: 0.00200
[2025-09-06 14:42:34,780 INFO misc.py line 117 1395871] Train: [14/100][778/1462] Data 0.001 (0.001) Batch 3.191 (2.883) Remain 101:13:18 loss: 0.5039 Lr: 0.00200
[2025-09-06 14:42:37,499 INFO misc.py line 117 1395871] Train: [14/100][779/1462] Data 0.001 (0.001) Batch 2.719 (2.882) Remain 101:12:48 loss: 0.2671 Lr: 0.00200
[2025-09-06 14:42:40,538 INFO misc.py line 117 1395871] Train: [14/100][780/1462] Data 0.002 (0.001) Batch 3.039 (2.883) Remain 101:13:11 loss: 0.2650 Lr: 0.00200
[2025-09-06 14:42:43,193 INFO misc.py line 117 1395871] Train: [14/100][781/1462] Data 0.001 (0.001) Batch 2.655 (2.882) Remain 101:12:31 loss: 0.5020 Lr: 0.00200
[2025-09-06 14:42:46,083 INFO misc.py line 117 1395871] Train: [14/100][782/1462] Data 0.002 (0.001) Batch 2.890 (2.882) Remain 101:12:29 loss: 0.5034 Lr: 0.00200
[2025-09-06 14:42:49,087 INFO misc.py line 117 1395871] Train: [14/100][783/1462] Data 0.002 (0.001) Batch 3.004 (2.882) Remain 101:12:46 loss: 0.1948 Lr: 0.00200
[2025-09-06 14:42:52,337 INFO misc.py line 117 1395871] Train: [14/100][784/1462] Data 0.002 (0.001) Batch 3.249 (2.883) Remain 101:13:42 loss: 0.6509 Lr: 0.00200
[2025-09-06 14:42:55,080 INFO misc.py line 117 1395871] Train: [14/100][785/1462] Data 0.001 (0.001) Batch 2.744 (2.883) Remain 101:13:17 loss: 0.7952 Lr: 0.00200
[2025-09-06 14:42:57,900 INFO misc.py line 117 1395871] Train: [14/100][786/1462] Data 0.001 (0.001) Batch 2.820 (2.883) Remain 101:13:04 loss: 0.2368 Lr: 0.00200
[2025-09-06 14:43:00,665 INFO misc.py line 117 1395871] Train: [14/100][787/1462] Data 0.001 (0.001) Batch 2.765 (2.882) Remain 101:12:42 loss: 0.6377 Lr: 0.00200
[2025-09-06 14:43:03,146 INFO misc.py line 117 1395871] Train: [14/100][788/1462] Data 0.002 (0.001) Batch 2.481 (2.882) Remain 101:11:35 loss: 0.5003 Lr: 0.00200
[2025-09-06 14:43:06,316 INFO misc.py line 117 1395871] Train: [14/100][789/1462] Data 0.002 (0.001) Batch 3.170 (2.882) Remain 101:12:18 loss: 0.4192 Lr: 0.00200
[2025-09-06 14:43:09,188 INFO misc.py line 117 1395871] Train: [14/100][790/1462] Data 0.002 (0.001) Batch 2.873 (2.882) Remain 101:12:14 loss: 0.4999 Lr: 0.00200
[2025-09-06 14:43:11,838 INFO misc.py line 117 1395871] Train: [14/100][791/1462] Data 0.002 (0.001) Batch 2.650 (2.882) Remain 101:11:34 loss: 0.3579 Lr: 0.00200
[2025-09-06 14:43:14,731 INFO misc.py line 117 1395871] Train: [14/100][792/1462] Data 0.001 (0.001) Batch 2.893 (2.882) Remain 101:11:32 loss: 0.2749 Lr: 0.00200
[2025-09-06 14:43:17,563 INFO misc.py line 117 1395871] Train: [14/100][793/1462] Data 0.002 (0.001) Batch 2.832 (2.882) Remain 101:11:22 loss: 0.3472 Lr: 0.00200
[2025-09-06 14:43:20,571 INFO misc.py line 117 1395871] Train: [14/100][794/1462] Data 0.002 (0.001) Batch 3.007 (2.882) Remain 101:11:39 loss: 0.3983 Lr: 0.00200
[2025-09-06 14:43:23,371 INFO misc.py line 117 1395871] Train: [14/100][795/1462] Data 0.002 (0.001) Batch 2.800 (2.882) Remain 101:11:23 loss: 0.4071 Lr: 0.00200
[2025-09-06 14:43:26,450 INFO misc.py line 117 1395871] Train: [14/100][796/1462] Data 0.002 (0.001) Batch 3.079 (2.882) Remain 101:11:51 loss: 0.3113 Lr: 0.00200
[2025-09-06 14:43:29,515 INFO misc.py line 117 1395871] Train: [14/100][797/1462] Data 0.002 (0.001) Batch 3.065 (2.882) Remain 101:12:17 loss: 0.3854 Lr: 0.00200
[2025-09-06 14:43:32,492 INFO misc.py line 117 1395871] Train: [14/100][798/1462] Data 0.002 (0.001) Batch 2.977 (2.883) Remain 101:12:30 loss: 0.3159 Lr: 0.00200
[2025-09-06 14:43:35,473 INFO misc.py line 117 1395871] Train: [14/100][799/1462] Data 0.001 (0.001) Batch 2.982 (2.883) Remain 101:12:42 loss: 0.2616 Lr: 0.00200
[2025-09-06 14:43:38,213 INFO misc.py line 117 1395871] Train: [14/100][800/1462] Data 0.001 (0.001) Batch 2.740 (2.883) Remain 101:12:17 loss: 0.4601 Lr: 0.00200
[2025-09-06 14:43:41,134 INFO misc.py line 117 1395871] Train: [14/100][801/1462] Data 0.001 (0.001) Batch 2.921 (2.883) Remain 101:12:20 loss: 0.4051 Lr: 0.00200
[2025-09-06 14:43:44,498 INFO misc.py line 117 1395871] Train: [14/100][802/1462] Data 0.001 (0.001) Batch 3.363 (2.883) Remain 101:13:33 loss: 0.3360 Lr: 0.00200
[2025-09-06 14:43:47,219 INFO misc.py line 117 1395871] Train: [14/100][803/1462] Data 0.001 (0.001) Batch 2.722 (2.883) Remain 101:13:05 loss: 0.1288 Lr: 0.00200
[2025-09-06 14:43:50,445 INFO misc.py line 117 1395871] Train: [14/100][804/1462] Data 0.001 (0.001) Batch 3.225 (2.883) Remain 101:13:56 loss: 0.4773 Lr: 0.00200
[2025-09-06 14:43:52,910 INFO misc.py line 117 1395871] Train: [14/100][805/1462] Data 0.001 (0.001) Batch 2.465 (2.883) Remain 101:12:47 loss: 0.2700 Lr: 0.00200
[2025-09-06 14:43:55,706 INFO misc.py line 117 1395871] Train: [14/100][806/1462] Data 0.001 (0.001) Batch 2.796 (2.883) Remain 101:12:31 loss: 0.3854 Lr: 0.00200
[2025-09-06 14:43:58,597 INFO misc.py line 117 1395871] Train: [14/100][807/1462] Data 0.001 (0.001) Batch 2.891 (2.883) Remain 101:12:29 loss: 0.8038 Lr: 0.00200
[2025-09-06 14:44:01,406 INFO misc.py line 117 1395871] Train: [14/100][808/1462] Data 0.002 (0.001) Batch 2.809 (2.883) Remain 101:12:15 loss: 0.3333 Lr: 0.00200
[2025-09-06 14:44:04,123 INFO misc.py line 117 1395871] Train: [14/100][809/1462] Data 0.001 (0.001) Batch 2.717 (2.883) Remain 101:11:46 loss: 0.3702 Lr: 0.00200
[2025-09-06 14:44:07,024 INFO misc.py line 117 1395871] Train: [14/100][810/1462] Data 0.001 (0.001) Batch 2.901 (2.883) Remain 101:11:46 loss: 0.3229 Lr: 0.00200
[2025-09-06 14:44:10,100 INFO misc.py line 117 1395871] Train: [14/100][811/1462] Data 0.002 (0.001) Batch 3.076 (2.883) Remain 101:12:13 loss: 0.4547 Lr: 0.00200
[2025-09-06 14:44:12,833 INFO misc.py line 117 1395871] Train: [14/100][812/1462] Data 0.002 (0.001) Batch 2.733 (2.883) Remain 101:11:47 loss: 0.2354 Lr: 0.00200
[2025-09-06 14:44:16,069 INFO misc.py line 117 1395871] Train: [14/100][813/1462] Data 0.001 (0.001) Batch 3.236 (2.883) Remain 101:12:39 loss: 0.2890 Lr: 0.00200
[2025-09-06 14:44:18,872 INFO misc.py line 117 1395871] Train: [14/100][814/1462] Data 0.001 (0.001) Batch 2.803 (2.883) Remain 101:12:24 loss: 0.2699 Lr: 0.00200
[2025-09-06 14:44:21,647 INFO misc.py line 117 1395871] Train: [14/100][815/1462] Data 0.002 (0.001) Batch 2.775 (2.883) Remain 101:12:04 loss: 0.4883 Lr: 0.00200
[2025-09-06 14:44:24,799 INFO misc.py line 117 1395871] Train: [14/100][816/1462] Data 0.002 (0.001) Batch 3.152 (2.883) Remain 101:12:43 loss: 0.3996 Lr: 0.00200
[2025-09-06 14:44:27,703 INFO misc.py line 117 1395871] Train: [14/100][817/1462] Data 0.002 (0.001) Batch 2.904 (2.883) Remain 101:12:43 loss: 0.4577 Lr: 0.00200
[2025-09-06 14:44:30,331 INFO misc.py line 117 1395871] Train: [14/100][818/1462] Data 0.002 (0.001) Batch 2.628 (2.883) Remain 101:12:01 loss: 0.3552 Lr: 0.00200
[2025-09-06 14:44:33,005 INFO misc.py line 117 1395871] Train: [14/100][819/1462] Data 0.001 (0.001) Batch 2.674 (2.883) Remain 101:11:26 loss: 0.6260 Lr: 0.00200
[2025-09-06 14:44:35,569 INFO misc.py line 117 1395871] Train: [14/100][820/1462] Data 0.001 (0.001) Batch 2.565 (2.882) Remain 101:10:34 loss: 0.4223 Lr: 0.00200
[2025-09-06 14:44:38,326 INFO misc.py line 117 1395871] Train: [14/100][821/1462] Data 0.002 (0.001) Batch 2.757 (2.882) Remain 101:10:11 loss: 0.4144 Lr: 0.00200
[2025-09-06 14:44:41,542 INFO misc.py line 117 1395871] Train: [14/100][822/1462] Data 0.002 (0.001) Batch 3.215 (2.882) Remain 101:11:00 loss: 0.2652 Lr: 0.00200
[2025-09-06 14:44:44,521 INFO misc.py line 117 1395871] Train: [14/100][823/1462] Data 0.001 (0.001) Batch 2.979 (2.883) Remain 101:11:12 loss: 0.2958 Lr: 0.00200
[2025-09-06 14:44:47,395 INFO misc.py line 117 1395871] Train: [14/100][824/1462] Data 0.001 (0.001) Batch 2.873 (2.883) Remain 101:11:08 loss: 0.3348 Lr: 0.00200
[2025-09-06 14:44:50,422 INFO misc.py line 117 1395871] Train: [14/100][825/1462] Data 0.001 (0.001) Batch 3.027 (2.883) Remain 101:11:27 loss: 0.3214 Lr: 0.00200
[2025-09-06 14:44:53,455 INFO misc.py line 117 1395871] Train: [14/100][826/1462] Data 0.001 (0.001) Batch 3.033 (2.883) Remain 101:11:47 loss: 0.3131 Lr: 0.00200
[2025-09-06 14:44:56,479 INFO misc.py line 117 1395871] Train: [14/100][827/1462] Data 0.001 (0.001) Batch 3.024 (2.883) Remain 101:12:06 loss: 0.3846 Lr: 0.00200
[2025-09-06 14:44:59,367 INFO misc.py line 117 1395871] Train: [14/100][828/1462] Data 0.001 (0.001) Batch 2.889 (2.883) Remain 101:12:04 loss: 0.4857 Lr: 0.00200
[2025-09-06 14:45:01,963 INFO misc.py line 117 1395871] Train: [14/100][829/1462] Data 0.001 (0.001) Batch 2.595 (2.883) Remain 101:11:17 loss: 0.2780 Lr: 0.00200
[2025-09-06 14:45:04,849 INFO misc.py line 117 1395871] Train: [14/100][830/1462] Data 0.002 (0.001) Batch 2.886 (2.883) Remain 101:11:15 loss: 0.3404 Lr: 0.00200
[2025-09-06 14:45:07,874 INFO misc.py line 117 1395871] Train: [14/100][831/1462] Data 0.001 (0.001) Batch 3.025 (2.883) Remain 101:11:34 loss: 0.4091 Lr: 0.00200
[2025-09-06 14:45:10,529 INFO misc.py line 117 1395871] Train: [14/100][832/1462] Data 0.001 (0.001) Batch 2.655 (2.883) Remain 101:10:56 loss: 0.2865 Lr: 0.00200
[2025-09-06 14:45:13,531 INFO misc.py line 117 1395871] Train: [14/100][833/1462] Data 0.002 (0.001) Batch 3.002 (2.883) Remain 101:11:11 loss: 0.3290 Lr: 0.00200
[2025-09-06 14:45:16,331 INFO misc.py line 117 1395871] Train: [14/100][834/1462] Data 0.001 (0.001) Batch 2.800 (2.883) Remain 101:10:56 loss: 0.5113 Lr: 0.00200
[2025-09-06 14:45:19,317 INFO misc.py line 117 1395871] Train: [14/100][835/1462] Data 0.002 (0.001) Batch 2.986 (2.883) Remain 101:11:08 loss: 0.3028 Lr: 0.00200
[2025-09-06 14:45:22,569 INFO misc.py line 117 1395871] Train: [14/100][836/1462] Data 0.002 (0.001) Batch 3.252 (2.883) Remain 101:12:02 loss: 0.2480 Lr: 0.00200
[2025-09-06 14:45:25,296 INFO misc.py line 117 1395871] Train: [14/100][837/1462] Data 0.002 (0.001) Batch 2.726 (2.883) Remain 101:11:35 loss: 0.5919 Lr: 0.00200
[2025-09-06 14:45:28,227 INFO misc.py line 117 1395871] Train: [14/100][838/1462] Data 0.001 (0.001) Batch 2.932 (2.883) Remain 101:11:39 loss: 0.4225 Lr: 0.00200
[2025-09-06 14:45:31,088 INFO misc.py line 117 1395871] Train: [14/100][839/1462] Data 0.001 (0.001) Batch 2.861 (2.883) Remain 101:11:33 loss: 0.3664 Lr: 0.00200
[2025-09-06 14:45:34,126 INFO misc.py line 117 1395871] Train: [14/100][840/1462] Data 0.002 (0.001) Batch 3.038 (2.883) Remain 101:11:54 loss: 0.3090 Lr: 0.00200
[2025-09-06 14:45:36,999 INFO misc.py line 117 1395871] Train: [14/100][841/1462] Data 0.001 (0.001) Batch 2.872 (2.883) Remain 101:11:49 loss: 0.3460 Lr: 0.00200
[2025-09-06 14:45:39,783 INFO misc.py line 117 1395871] Train: [14/100][842/1462] Data 0.001 (0.001) Batch 2.784 (2.883) Remain 101:11:31 loss: 0.3798 Lr: 0.00200
[2025-09-06 14:45:42,391 INFO misc.py line 117 1395871] Train: [14/100][843/1462] Data 0.001 (0.001) Batch 2.608 (2.883) Remain 101:10:47 loss: 0.2715 Lr: 0.00200
[2025-09-06 14:45:45,363 INFO misc.py line 117 1395871] Train: [14/100][844/1462] Data 0.001 (0.001) Batch 2.971 (2.883) Remain 101:10:58 loss: 0.2304 Lr: 0.00200
[2025-09-06 14:45:48,324 INFO misc.py line 117 1395871] Train: [14/100][845/1462] Data 0.001 (0.001) Batch 2.962 (2.883) Remain 101:11:07 loss: 0.3213 Lr: 0.00200
[2025-09-06 14:45:51,360 INFO misc.py line 117 1395871] Train: [14/100][846/1462] Data 0.001 (0.001) Batch 3.036 (2.883) Remain 101:11:27 loss: 0.4768 Lr: 0.00200
[2025-09-06 14:45:54,181 INFO misc.py line 117 1395871] Train: [14/100][847/1462] Data 0.001 (0.001) Batch 2.821 (2.883) Remain 101:11:14 loss: 0.5296 Lr: 0.00200
[2025-09-06 14:45:56,920 INFO misc.py line 117 1395871] Train: [14/100][848/1462] Data 0.002 (0.001) Batch 2.738 (2.883) Remain 101:10:50 loss: 0.3189 Lr: 0.00200
[2025-09-06 14:45:59,855 INFO misc.py line 117 1395871] Train: [14/100][849/1462] Data 0.002 (0.001) Batch 2.936 (2.883) Remain 101:10:55 loss: 0.4036 Lr: 0.00200
[2025-09-06 14:46:02,802 INFO misc.py line 117 1395871] Train: [14/100][850/1462] Data 0.002 (0.001) Batch 2.947 (2.883) Remain 101:11:02 loss: 0.4379 Lr: 0.00200
[2025-09-06 14:46:05,941 INFO misc.py line 117 1395871] Train: [14/100][851/1462] Data 0.002 (0.001) Batch 3.139 (2.883) Remain 101:11:37 loss: 0.3937 Lr: 0.00200
[2025-09-06 14:46:08,601 INFO misc.py line 117 1395871] Train: [14/100][852/1462] Data 0.002 (0.001) Batch 2.660 (2.883) Remain 101:11:01 loss: 0.3314 Lr: 0.00200
[2025-09-06 14:46:11,493 INFO misc.py line 117 1395871] Train: [14/100][853/1462] Data 0.001 (0.001) Batch 2.893 (2.883) Remain 101:10:59 loss: 0.5903 Lr: 0.00200
[2025-09-06 14:46:14,650 INFO misc.py line 117 1395871] Train: [14/100][854/1462] Data 0.001 (0.001) Batch 3.157 (2.883) Remain 101:11:37 loss: 0.3524 Lr: 0.00200
[2025-09-06 14:46:17,472 INFO misc.py line 117 1395871] Train: [14/100][855/1462] Data 0.001 (0.001) Batch 2.822 (2.883) Remain 101:11:25 loss: 0.2514 Lr: 0.00200
[2025-09-06 14:46:20,225 INFO misc.py line 117 1395871] Train: [14/100][856/1462] Data 0.001 (0.001) Batch 2.753 (2.883) Remain 101:11:03 loss: 0.4448 Lr: 0.00200
[2025-09-06 14:46:23,199 INFO misc.py line 117 1395871] Train: [14/100][857/1462] Data 0.001 (0.001) Batch 2.975 (2.883) Remain 101:11:13 loss: 1.2217 Lr: 0.00200
[2025-09-06 14:46:26,135 INFO misc.py line 117 1395871] Train: [14/100][858/1462] Data 0.001 (0.001) Batch 2.935 (2.883) Remain 101:11:18 loss: 0.3141 Lr: 0.00200
[2025-09-06 14:46:28,684 INFO misc.py line 117 1395871] Train: [14/100][859/1462] Data 0.001 (0.001) Batch 2.550 (2.883) Remain 101:10:26 loss: 0.5485 Lr: 0.00200
[2025-09-06 14:46:31,408 INFO misc.py line 117 1395871] Train: [14/100][860/1462] Data 0.001 (0.001) Batch 2.724 (2.883) Remain 101:10:00 loss: 0.4134 Lr: 0.00200
[2025-09-06 14:46:34,216 INFO misc.py line 117 1395871] Train: [14/100][861/1462] Data 0.001 (0.001) Batch 2.807 (2.883) Remain 101:09:46 loss: 0.2626 Lr: 0.00200
[2025-09-06 14:46:37,185 INFO misc.py line 117 1395871] Train: [14/100][862/1462] Data 0.001 (0.001) Batch 2.969 (2.883) Remain 101:09:55 loss: 0.4749 Lr: 0.00200
[2025-09-06 14:46:39,754 INFO misc.py line 117 1395871] Train: [14/100][863/1462] Data 0.001 (0.001) Batch 2.569 (2.882) Remain 101:09:06 loss: 0.3417 Lr: 0.00200
[2025-09-06 14:46:42,743 INFO misc.py line 117 1395871] Train: [14/100][864/1462] Data 0.001 (0.001) Batch 2.988 (2.883) Remain 101:09:19 loss: 0.3076 Lr: 0.00200
[2025-09-06 14:46:45,664 INFO misc.py line 117 1395871] Train: [14/100][865/1462] Data 0.002 (0.001) Batch 2.922 (2.883) Remain 101:09:22 loss: 0.4150 Lr: 0.00200
[2025-09-06 14:46:48,357 INFO misc.py line 117 1395871] Train: [14/100][866/1462] Data 0.001 (0.001) Batch 2.693 (2.882) Remain 101:08:51 loss: 0.3870 Lr: 0.00200
[2025-09-06 14:46:50,776 INFO misc.py line 117 1395871] Train: [14/100][867/1462] Data 0.002 (0.001) Batch 2.419 (2.882) Remain 101:07:41 loss: 0.8082 Lr: 0.00200
[2025-09-06 14:46:53,570 INFO misc.py line 117 1395871] Train: [14/100][868/1462] Data 0.001 (0.001) Batch 2.793 (2.882) Remain 101:07:25 loss: 0.5569 Lr: 0.00200
[2025-09-06 14:46:56,407 INFO misc.py line 117 1395871] Train: [14/100][869/1462] Data 0.002 (0.001) Batch 2.837 (2.882) Remain 101:07:15 loss: 0.4298 Lr: 0.00200
[2025-09-06 14:46:58,921 INFO misc.py line 117 1395871] Train: [14/100][870/1462] Data 0.001 (0.001) Batch 2.514 (2.881) Remain 101:06:19 loss: 0.7359 Lr: 0.00200
[2025-09-06 14:47:01,672 INFO misc.py line 117 1395871] Train: [14/100][871/1462] Data 0.002 (0.001) Batch 2.751 (2.881) Remain 101:05:57 loss: 0.3901 Lr: 0.00200
[2025-09-06 14:47:04,447 INFO misc.py line 117 1395871] Train: [14/100][872/1462] Data 0.002 (0.001) Batch 2.775 (2.881) Remain 101:05:39 loss: 0.4434 Lr: 0.00200
[2025-09-06 14:47:07,552 INFO misc.py line 117 1395871] Train: [14/100][873/1462] Data 0.001 (0.001) Batch 3.104 (2.881) Remain 101:06:08 loss: 0.3788 Lr: 0.00200
[2025-09-06 14:47:10,459 INFO misc.py line 117 1395871] Train: [14/100][874/1462] Data 0.001 (0.001) Batch 2.908 (2.881) Remain 101:06:09 loss: 0.2477 Lr: 0.00200
[2025-09-06 14:47:13,348 INFO misc.py line 117 1395871] Train: [14/100][875/1462] Data 0.002 (0.001) Batch 2.889 (2.881) Remain 101:06:08 loss: 0.3537 Lr: 0.00200
[2025-09-06 14:47:15,748 INFO misc.py line 117 1395871] Train: [14/100][876/1462] Data 0.001 (0.001) Batch 2.400 (2.881) Remain 101:04:55 loss: 0.2589 Lr: 0.00200
[2025-09-06 14:47:18,569 INFO misc.py line 117 1395871] Train: [14/100][877/1462] Data 0.001 (0.001) Batch 2.820 (2.881) Remain 101:04:43 loss: 0.3964 Lr: 0.00200
[2025-09-06 14:47:21,470 INFO misc.py line 117 1395871] Train: [14/100][878/1462] Data 0.002 (0.001) Batch 2.900 (2.881) Remain 101:04:43 loss: 0.3559 Lr: 0.00200
[2025-09-06 14:47:24,169 INFO misc.py line 117 1395871] Train: [14/100][879/1462] Data 0.002 (0.001) Batch 2.699 (2.881) Remain 101:04:14 loss: 0.4050 Lr: 0.00200
[2025-09-06 14:47:26,983 INFO misc.py line 117 1395871] Train: [14/100][880/1462] Data 0.003 (0.001) Batch 2.814 (2.880) Remain 101:04:02 loss: 0.2039 Lr: 0.00200
[2025-09-06 14:47:29,680 INFO misc.py line 117 1395871] Train: [14/100][881/1462] Data 0.002 (0.001) Batch 2.698 (2.880) Remain 101:03:33 loss: 0.6076 Lr: 0.00200
[2025-09-06 14:47:32,581 INFO misc.py line 117 1395871] Train: [14/100][882/1462] Data 0.001 (0.001) Batch 2.901 (2.880) Remain 101:03:33 loss: 0.3416 Lr: 0.00200
[2025-09-06 14:47:35,346 INFO misc.py line 117 1395871] Train: [14/100][883/1462] Data 0.001 (0.001) Batch 2.765 (2.880) Remain 101:03:13 loss: 0.5518 Lr: 0.00200
[2025-09-06 14:47:38,387 INFO misc.py line 117 1395871] Train: [14/100][884/1462] Data 0.001 (0.001) Batch 3.041 (2.880) Remain 101:03:34 loss: 0.4308 Lr: 0.00200
[2025-09-06 14:47:41,421 INFO misc.py line 117 1395871] Train: [14/100][885/1462] Data 0.002 (0.001) Batch 3.034 (2.881) Remain 101:03:53 loss: 0.3876 Lr: 0.00200
[2025-09-06 14:47:44,150 INFO misc.py line 117 1395871] Train: [14/100][886/1462] Data 0.001 (0.001) Batch 2.730 (2.880) Remain 101:03:28 loss: 0.3759 Lr: 0.00200
[2025-09-06 14:47:47,172 INFO misc.py line 117 1395871] Train: [14/100][887/1462] Data 0.001 (0.001) Batch 3.022 (2.880) Remain 101:03:46 loss: 0.4407 Lr: 0.00200
[2025-09-06 14:47:49,974 INFO misc.py line 117 1395871] Train: [14/100][888/1462] Data 0.001 (0.001) Batch 2.802 (2.880) Remain 101:03:31 loss: 0.1837 Lr: 0.00200
[2025-09-06 14:47:52,673 INFO misc.py line 117 1395871] Train: [14/100][889/1462] Data 0.001 (0.001) Batch 2.698 (2.880) Remain 101:03:03 loss: 0.4127 Lr: 0.00200
[2025-09-06 14:47:55,467 INFO misc.py line 117 1395871] Train: [14/100][890/1462] Data 0.001 (0.001) Batch 2.794 (2.880) Remain 101:02:48 loss: 0.5659 Lr: 0.00200
[2025-09-06 14:47:58,355 INFO misc.py line 117 1395871] Train: [14/100][891/1462] Data 0.001 (0.001) Batch 2.888 (2.880) Remain 101:02:46 loss: 0.4266 Lr: 0.00200
[2025-09-06 14:48:01,179 INFO misc.py line 117 1395871] Train: [14/100][892/1462] Data 0.002 (0.001) Batch 2.825 (2.880) Remain 101:02:35 loss: 0.7429 Lr: 0.00200
[2025-09-06 14:48:03,915 INFO misc.py line 117 1395871] Train: [14/100][893/1462] Data 0.001 (0.001) Batch 2.735 (2.880) Remain 101:02:12 loss: 0.2270 Lr: 0.00200
[2025-09-06 14:48:06,992 INFO misc.py line 117 1395871] Train: [14/100][894/1462] Data 0.002 (0.001) Batch 3.077 (2.880) Remain 101:02:37 loss: 0.6012 Lr: 0.00200
[2025-09-06 14:48:10,030 INFO misc.py line 117 1395871] Train: [14/100][895/1462] Data 0.002 (0.001) Batch 3.038 (2.880) Remain 101:02:56 loss: 0.2980 Lr: 0.00200
[2025-09-06 14:48:12,703 INFO misc.py line 117 1395871] Train: [14/100][896/1462] Data 0.002 (0.001) Batch 2.673 (2.880) Remain 101:02:24 loss: 0.2632 Lr: 0.00200
[2025-09-06 14:48:16,000 INFO misc.py line 117 1395871] Train: [14/100][897/1462] Data 0.001 (0.001) Batch 3.297 (2.881) Remain 101:03:20 loss: 0.3450 Lr: 0.00200
[2025-09-06 14:48:18,553 INFO misc.py line 117 1395871] Train: [14/100][898/1462] Data 0.001 (0.001) Batch 2.553 (2.880) Remain 101:02:31 loss: 0.3840 Lr: 0.00200
[2025-09-06 14:48:21,638 INFO misc.py line 117 1395871] Train: [14/100][899/1462] Data 0.001 (0.001) Batch 3.084 (2.880) Remain 101:02:57 loss: 0.4277 Lr: 0.00200
[2025-09-06 14:48:24,355 INFO misc.py line 117 1395871] Train: [14/100][900/1462] Data 0.002 (0.001) Batch 2.717 (2.880) Remain 101:02:31 loss: 0.4421 Lr: 0.00200
[2025-09-06 14:48:27,486 INFO misc.py line 117 1395871] Train: [14/100][901/1462] Data 0.002 (0.001) Batch 3.132 (2.880) Remain 101:03:03 loss: 0.3605 Lr: 0.00200
[2025-09-06 14:48:30,398 INFO misc.py line 117 1395871] Train: [14/100][902/1462] Data 0.001 (0.001) Batch 2.912 (2.881) Remain 101:03:05 loss: 0.6588 Lr: 0.00200
[2025-09-06 14:48:33,441 INFO misc.py line 117 1395871] Train: [14/100][903/1462] Data 0.001 (0.001) Batch 3.043 (2.881) Remain 101:03:25 loss: 0.6217 Lr: 0.00200
[2025-09-06 14:48:35,994 INFO misc.py line 117 1395871] Train: [14/100][904/1462] Data 0.002 (0.001) Batch 2.553 (2.880) Remain 101:02:36 loss: 0.2969 Lr: 0.00200
[2025-09-06 14:48:38,831 INFO misc.py line 117 1395871] Train: [14/100][905/1462] Data 0.002 (0.001) Batch 2.837 (2.880) Remain 101:02:27 loss: 0.4757 Lr: 0.00200
[2025-09-06 14:48:41,517 INFO misc.py line 117 1395871] Train: [14/100][906/1462] Data 0.001 (0.001) Batch 2.686 (2.880) Remain 101:01:57 loss: 0.4588 Lr: 0.00200
[2025-09-06 14:48:44,429 INFO misc.py line 117 1395871] Train: [14/100][907/1462] Data 0.002 (0.001) Batch 2.912 (2.880) Remain 101:01:59 loss: 0.5696 Lr: 0.00200
[2025-09-06 14:48:47,191 INFO misc.py line 117 1395871] Train: [14/100][908/1462] Data 0.001 (0.001) Batch 2.762 (2.880) Remain 101:01:39 loss: 0.3668 Lr: 0.00200
[2025-09-06 14:48:49,874 INFO misc.py line 117 1395871] Train: [14/100][909/1462] Data 0.001 (0.001) Batch 2.684 (2.880) Remain 101:01:09 loss: 0.4080 Lr: 0.00200
[2025-09-06 14:48:52,697 INFO misc.py line 117 1395871] Train: [14/100][910/1462] Data 0.002 (0.001) Batch 2.823 (2.880) Remain 101:00:58 loss: 0.2987 Lr: 0.00200
[2025-09-06 14:48:55,584 INFO misc.py line 117 1395871] Train: [14/100][911/1462] Data 0.001 (0.001) Batch 2.887 (2.880) Remain 101:00:56 loss: 0.3962 Lr: 0.00200
[2025-09-06 14:48:58,660 INFO misc.py line 117 1395871] Train: [14/100][912/1462] Data 0.001 (0.001) Batch 3.076 (2.880) Remain 101:01:21 loss: 0.2284 Lr: 0.00200
[2025-09-06 14:49:01,294 INFO misc.py line 117 1395871] Train: [14/100][913/1462] Data 0.002 (0.001) Batch 2.634 (2.880) Remain 101:00:44 loss: 0.6339 Lr: 0.00200
[2025-09-06 14:49:04,456 INFO misc.py line 117 1395871] Train: [14/100][914/1462] Data 0.001 (0.001) Batch 3.162 (2.880) Remain 101:01:20 loss: 0.4354 Lr: 0.00200
[2025-09-06 14:49:07,031 INFO misc.py line 117 1395871] Train: [14/100][915/1462] Data 0.001 (0.001) Batch 2.576 (2.880) Remain 101:00:35 loss: 0.5752 Lr: 0.00200
[2025-09-06 14:49:09,746 INFO misc.py line 117 1395871] Train: [14/100][916/1462] Data 0.002 (0.001) Batch 2.714 (2.879) Remain 101:00:09 loss: 0.4401 Lr: 0.00200
[2025-09-06 14:49:13,110 INFO misc.py line 117 1395871] Train: [14/100][917/1462] Data 0.001 (0.001) Batch 3.364 (2.880) Remain 101:01:13 loss: 0.4333 Lr: 0.00200
[2025-09-06 14:49:16,150 INFO misc.py line 117 1395871] Train: [14/100][918/1462] Data 0.001 (0.001) Batch 3.040 (2.880) Remain 101:01:32 loss: 0.2369 Lr: 0.00200
[2025-09-06 14:49:18,965 INFO misc.py line 117 1395871] Train: [14/100][919/1462] Data 0.002 (0.001) Batch 2.815 (2.880) Remain 101:01:21 loss: 0.4809 Lr: 0.00200
[2025-09-06 14:49:21,461 INFO misc.py line 117 1395871] Train: [14/100][920/1462] Data 0.001 (0.001) Batch 2.496 (2.880) Remain 101:00:25 loss: 0.5877 Lr: 0.00200
[2025-09-06 14:49:24,500 INFO misc.py line 117 1395871] Train: [14/100][921/1462] Data 0.001 (0.001) Batch 3.039 (2.880) Remain 101:00:44 loss: 0.4768 Lr: 0.00200
[2025-09-06 14:49:27,149 INFO misc.py line 117 1395871] Train: [14/100][922/1462] Data 0.002 (0.001) Batch 2.648 (2.880) Remain 101:00:09 loss: 0.2542 Lr: 0.00200
[2025-09-06 14:49:30,217 INFO misc.py line 117 1395871] Train: [14/100][923/1462] Data 0.002 (0.001) Batch 3.068 (2.880) Remain 101:00:32 loss: 0.3207 Lr: 0.00200
[2025-09-06 14:49:33,362 INFO misc.py line 117 1395871] Train: [14/100][924/1462] Data 0.002 (0.001) Batch 3.145 (2.880) Remain 101:01:06 loss: 0.3132 Lr: 0.00200
[2025-09-06 14:49:36,157 INFO misc.py line 117 1395871] Train: [14/100][925/1462] Data 0.002 (0.001) Batch 2.796 (2.880) Remain 101:00:51 loss: 0.4576 Lr: 0.00200
[2025-09-06 14:49:39,331 INFO misc.py line 117 1395871] Train: [14/100][926/1462] Data 0.002 (0.001) Batch 3.173 (2.880) Remain 101:01:28 loss: 0.3378 Lr: 0.00200
[2025-09-06 14:49:42,048 INFO misc.py line 117 1395871] Train: [14/100][927/1462] Data 0.001 (0.001) Batch 2.717 (2.880) Remain 101:01:03 loss: 0.5204 Lr: 0.00200
[2025-09-06 14:49:45,465 INFO misc.py line 117 1395871] Train: [14/100][928/1462] Data 0.002 (0.001) Batch 3.418 (2.881) Remain 101:02:14 loss: 0.6169 Lr: 0.00200
[2025-09-06 14:49:48,433 INFO misc.py line 117 1395871] Train: [14/100][929/1462] Data 0.001 (0.001) Batch 2.967 (2.881) Remain 101:02:23 loss: 0.5066 Lr: 0.00200
[2025-09-06 14:49:51,205 INFO misc.py line 117 1395871] Train: [14/100][930/1462] Data 0.001 (0.001) Batch 2.772 (2.881) Remain 101:02:05 loss: 0.3427 Lr: 0.00200
[2025-09-06 14:49:53,726 INFO misc.py line 117 1395871] Train: [14/100][931/1462] Data 0.001 (0.001) Batch 2.522 (2.880) Remain 101:01:13 loss: 0.3155 Lr: 0.00200
[2025-09-06 14:49:56,760 INFO misc.py line 117 1395871] Train: [14/100][932/1462] Data 0.001 (0.001) Batch 3.034 (2.880) Remain 101:01:31 loss: 0.4754 Lr: 0.00200
[2025-09-06 14:49:59,665 INFO misc.py line 117 1395871] Train: [14/100][933/1462] Data 0.002 (0.001) Batch 2.905 (2.880) Remain 101:01:32 loss: 0.4455 Lr: 0.00200
[2025-09-06 14:50:02,170 INFO misc.py line 117 1395871] Train: [14/100][934/1462] Data 0.001 (0.001) Batch 2.506 (2.880) Remain 101:00:38 loss: 0.3161 Lr: 0.00200
[2025-09-06 14:50:05,089 INFO misc.py line 117 1395871] Train: [14/100][935/1462] Data 0.002 (0.001) Batch 2.919 (2.880) Remain 101:00:40 loss: 0.3337 Lr: 0.00200
[2025-09-06 14:50:07,814 INFO misc.py line 117 1395871] Train: [14/100][936/1462] Data 0.002 (0.001) Batch 2.725 (2.880) Remain 101:00:16 loss: 0.5060 Lr: 0.00200
[2025-09-06 14:50:10,864 INFO misc.py line 117 1395871] Train: [14/100][937/1462] Data 0.002 (0.001) Batch 3.050 (2.880) Remain 101:00:37 loss: 0.5013 Lr: 0.00200
[2025-09-06 14:50:13,544 INFO misc.py line 117 1395871] Train: [14/100][938/1462] Data 0.001 (0.001) Batch 2.680 (2.880) Remain 101:00:07 loss: 0.5813 Lr: 0.00200
[2025-09-06 14:50:16,917 INFO misc.py line 117 1395871] Train: [14/100][939/1462] Data 0.001 (0.001) Batch 3.373 (2.880) Remain 101:01:10 loss: 0.4926 Lr: 0.00200
[2025-09-06 14:50:19,686 INFO misc.py line 117 1395871] Train: [14/100][940/1462] Data 0.002 (0.001) Batch 2.769 (2.880) Remain 101:00:52 loss: 0.3360 Lr: 0.00200
[2025-09-06 14:50:22,491 INFO misc.py line 117 1395871] Train: [14/100][941/1462] Data 0.002 (0.001) Batch 2.804 (2.880) Remain 101:00:39 loss: 0.3761 Lr: 0.00200
[2025-09-06 14:50:25,814 INFO misc.py line 117 1395871] Train: [14/100][942/1462] Data 0.001 (0.001) Batch 3.323 (2.881) Remain 101:01:36 loss: 0.3532 Lr: 0.00200
[2025-09-06 14:50:28,708 INFO misc.py line 117 1395871] Train: [14/100][943/1462] Data 0.001 (0.001) Batch 2.893 (2.881) Remain 101:01:35 loss: 0.1830 Lr: 0.00200
[2025-09-06 14:50:31,554 INFO misc.py line 117 1395871] Train: [14/100][944/1462] Data 0.002 (0.001) Batch 2.847 (2.881) Remain 101:01:27 loss: 0.5281 Lr: 0.00200
[2025-09-06 14:50:34,716 INFO misc.py line 117 1395871] Train: [14/100][945/1462] Data 0.001 (0.001) Batch 3.162 (2.881) Remain 101:02:02 loss: 0.4733 Lr: 0.00200
[2025-09-06 14:50:37,602 INFO misc.py line 117 1395871] Train: [14/100][946/1462] Data 0.001 (0.001) Batch 2.886 (2.881) Remain 101:02:00 loss: 0.3292 Lr: 0.00200
[2025-09-06 14:50:40,423 INFO misc.py line 117 1395871] Train: [14/100][947/1462] Data 0.002 (0.001) Batch 2.821 (2.881) Remain 101:01:49 loss: 0.3111 Lr: 0.00200
[2025-09-06 14:50:43,390 INFO misc.py line 117 1395871] Train: [14/100][948/1462] Data 0.001 (0.001) Batch 2.967 (2.881) Remain 101:01:58 loss: 0.5270 Lr: 0.00200
[2025-09-06 14:50:46,228 INFO misc.py line 117 1395871] Train: [14/100][949/1462] Data 0.001 (0.001) Batch 2.837 (2.881) Remain 101:01:49 loss: 0.7723 Lr: 0.00200
[2025-09-06 14:50:49,247 INFO misc.py line 117 1395871] Train: [14/100][950/1462] Data 0.002 (0.001) Batch 3.019 (2.881) Remain 101:02:05 loss: 0.5973 Lr: 0.00200
[2025-09-06 14:50:51,838 INFO misc.py line 117 1395871] Train: [14/100][951/1462] Data 0.001 (0.001) Batch 2.591 (2.881) Remain 101:01:23 loss: 0.2958 Lr: 0.00200
[2025-09-06 14:50:54,706 INFO misc.py line 117 1395871] Train: [14/100][952/1462] Data 0.001 (0.001) Batch 2.868 (2.881) Remain 101:01:18 loss: 0.3818 Lr: 0.00200
[2025-09-06 14:50:57,423 INFO misc.py line 117 1395871] Train: [14/100][953/1462] Data 0.001 (0.001) Batch 2.718 (2.881) Remain 101:00:54 loss: 0.3220 Lr: 0.00200
[2025-09-06 14:50:59,916 INFO misc.py line 117 1395871] Train: [14/100][954/1462] Data 0.001 (0.001) Batch 2.493 (2.880) Remain 100:59:59 loss: 0.2956 Lr: 0.00200
[2025-09-06 14:51:02,566 INFO misc.py line 117 1395871] Train: [14/100][955/1462] Data 0.001 (0.001) Batch 2.650 (2.880) Remain 100:59:26 loss: 0.4220 Lr: 0.00200
[2025-09-06 14:51:05,569 INFO misc.py line 117 1395871] Train: [14/100][956/1462] Data 0.001 (0.001) Batch 3.003 (2.880) Remain 100:59:40 loss: 0.4074 Lr: 0.00200
[2025-09-06 14:51:08,240 INFO misc.py line 117 1395871] Train: [14/100][957/1462] Data 0.001 (0.001) Batch 2.670 (2.880) Remain 100:59:09 loss: 0.2826 Lr: 0.00200
[2025-09-06 14:51:10,869 INFO misc.py line 117 1395871] Train: [14/100][958/1462] Data 0.001 (0.001) Batch 2.629 (2.880) Remain 100:58:33 loss: 0.6956 Lr: 0.00200
[2025-09-06 14:51:13,639 INFO misc.py line 117 1395871] Train: [14/100][959/1462] Data 0.001 (0.001) Batch 2.770 (2.880) Remain 100:58:15 loss: 0.4705 Lr: 0.00200
[2025-09-06 14:51:16,501 INFO misc.py line 117 1395871] Train: [14/100][960/1462] Data 0.002 (0.001) Batch 2.862 (2.879) Remain 100:58:10 loss: 0.2338 Lr: 0.00200
[2025-09-06 14:51:19,232 INFO misc.py line 117 1395871] Train: [14/100][961/1462] Data 0.001 (0.001) Batch 2.731 (2.879) Remain 100:57:48 loss: 0.2940 Lr: 0.00200
[2025-09-06 14:51:22,177 INFO misc.py line 117 1395871] Train: [14/100][962/1462] Data 0.001 (0.001) Batch 2.946 (2.879) Remain 100:57:54 loss: 0.3079 Lr: 0.00200
[2025-09-06 14:51:24,997 INFO misc.py line 117 1395871] Train: [14/100][963/1462] Data 0.001 (0.001) Batch 2.819 (2.879) Remain 100:57:43 loss: 0.2432 Lr: 0.00200
[2025-09-06 14:51:27,734 INFO misc.py line 117 1395871] Train: [14/100][964/1462] Data 0.001 (0.001) Batch 2.737 (2.879) Remain 100:57:21 loss: 0.6493 Lr: 0.00200
[2025-09-06 14:51:30,602 INFO misc.py line 117 1395871] Train: [14/100][965/1462] Data 0.001 (0.001) Batch 2.868 (2.879) Remain 100:57:17 loss: 0.4898 Lr: 0.00200
[2025-09-06 14:51:33,266 INFO misc.py line 117 1395871] Train: [14/100][966/1462] Data 0.001 (0.001) Batch 2.664 (2.879) Remain 100:56:46 loss: 0.4671 Lr: 0.00200
[2025-09-06 14:51:36,060 INFO misc.py line 117 1395871] Train: [14/100][967/1462] Data 0.001 (0.001) Batch 2.794 (2.879) Remain 100:56:32 loss: 0.6678 Lr: 0.00200
[2025-09-06 14:51:39,158 INFO misc.py line 117 1395871] Train: [14/100][968/1462] Data 0.001 (0.001) Batch 3.099 (2.879) Remain 100:56:58 loss: 0.5464 Lr: 0.00200
[2025-09-06 14:51:42,046 INFO misc.py line 117 1395871] Train: [14/100][969/1462] Data 0.001 (0.001) Batch 2.888 (2.879) Remain 100:56:56 loss: 0.4146 Lr: 0.00200
[2025-09-06 14:51:45,051 INFO misc.py line 117 1395871] Train: [14/100][970/1462] Data 0.001 (0.001) Batch 3.005 (2.879) Remain 100:57:10 loss: 0.3621 Lr: 0.00200
[2025-09-06 14:51:48,066 INFO misc.py line 117 1395871] Train: [14/100][971/1462] Data 0.002 (0.001) Batch 3.015 (2.879) Remain 100:57:24 loss: 0.6569 Lr: 0.00200
[2025-09-06 14:51:51,066 INFO misc.py line 117 1395871] Train: [14/100][972/1462] Data 0.001 (0.001) Batch 3.000 (2.880) Remain 100:57:37 loss: 0.3794 Lr: 0.00200
[2025-09-06 14:51:53,966 INFO misc.py line 117 1395871] Train: [14/100][973/1462] Data 0.001 (0.001) Batch 2.900 (2.880) Remain 100:57:37 loss: 0.3672 Lr: 0.00199
[2025-09-06 14:51:56,850 INFO misc.py line 117 1395871] Train: [14/100][974/1462] Data 0.002 (0.001) Batch 2.884 (2.880) Remain 100:57:35 loss: 0.6282 Lr: 0.00199
[2025-09-06 14:51:59,695 INFO misc.py line 117 1395871] Train: [14/100][975/1462] Data 0.001 (0.001) Batch 2.845 (2.880) Remain 100:57:27 loss: 0.5014 Lr: 0.00199
[2025-09-06 14:52:02,487 INFO misc.py line 117 1395871] Train: [14/100][976/1462] Data 0.002 (0.001) Batch 2.791 (2.879) Remain 100:57:13 loss: 0.6067 Lr: 0.00199
[2025-09-06 14:52:05,249 INFO misc.py line 117 1395871] Train: [14/100][977/1462] Data 0.002 (0.001) Batch 2.763 (2.879) Remain 100:56:55 loss: 0.3152 Lr: 0.00199
[2025-09-06 14:52:07,801 INFO misc.py line 117 1395871] Train: [14/100][978/1462] Data 0.001 (0.001) Batch 2.551 (2.879) Remain 100:56:10 loss: 0.2414 Lr: 0.00199
[2025-09-06 14:52:10,822 INFO misc.py line 117 1395871] Train: [14/100][979/1462] Data 0.002 (0.001) Batch 3.021 (2.879) Remain 100:56:25 loss: 0.4104 Lr: 0.00199
[2025-09-06 14:52:13,545 INFO misc.py line 117 1395871] Train: [14/100][980/1462] Data 0.001 (0.001) Batch 2.723 (2.879) Remain 100:56:02 loss: 0.4264 Lr: 0.00199
[2025-09-06 14:52:16,380 INFO misc.py line 117 1395871] Train: [14/100][981/1462] Data 0.001 (0.001) Batch 2.835 (2.879) Remain 100:55:54 loss: 0.3735 Lr: 0.00199
[2025-09-06 14:52:19,087 INFO misc.py line 117 1395871] Train: [14/100][982/1462] Data 0.001 (0.001) Batch 2.707 (2.879) Remain 100:55:29 loss: 0.5427 Lr: 0.00199
[2025-09-06 14:52:21,935 INFO misc.py line 117 1395871] Train: [14/100][983/1462] Data 0.001 (0.001) Batch 2.848 (2.879) Remain 100:55:22 loss: 0.3150 Lr: 0.00199
[2025-09-06 14:52:25,219 INFO misc.py line 117 1395871] Train: [14/100][984/1462] Data 0.001 (0.001) Batch 3.284 (2.879) Remain 100:56:11 loss: 0.5667 Lr: 0.00199
[2025-09-06 14:52:27,805 INFO misc.py line 117 1395871] Train: [14/100][985/1462] Data 0.001 (0.001) Batch 2.586 (2.879) Remain 100:55:31 loss: 0.4831 Lr: 0.00199
[2025-09-06 14:52:31,040 INFO misc.py line 117 1395871] Train: [14/100][986/1462] Data 0.001 (0.001) Batch 3.235 (2.879) Remain 100:56:13 loss: 0.4429 Lr: 0.00199
[2025-09-06 14:52:33,782 INFO misc.py line 117 1395871] Train: [14/100][987/1462] Data 0.001 (0.001) Batch 2.742 (2.879) Remain 100:55:53 loss: 0.2642 Lr: 0.00199
[2025-09-06 14:52:36,635 INFO misc.py line 117 1395871] Train: [14/100][988/1462] Data 0.001 (0.001) Batch 2.853 (2.879) Remain 100:55:47 loss: 0.2339 Lr: 0.00199
[2025-09-06 14:52:39,488 INFO misc.py line 117 1395871] Train: [14/100][989/1462] Data 0.002 (0.001) Batch 2.853 (2.879) Remain 100:55:40 loss: 0.2449 Lr: 0.00199
[2025-09-06 14:52:42,622 INFO misc.py line 117 1395871] Train: [14/100][990/1462] Data 0.002 (0.001) Batch 3.134 (2.879) Remain 100:56:10 loss: 0.3666 Lr: 0.00199
[2025-09-06 14:52:45,392 INFO misc.py line 117 1395871] Train: [14/100][991/1462] Data 0.001 (0.001) Batch 2.770 (2.879) Remain 100:55:53 loss: 0.6436 Lr: 0.00199
[2025-09-06 14:52:48,466 INFO misc.py line 117 1395871] Train: [14/100][992/1462] Data 0.001 (0.001) Batch 3.074 (2.879) Remain 100:56:15 loss: 0.3419 Lr: 0.00199
[2025-09-06 14:52:51,071 INFO misc.py line 117 1395871] Train: [14/100][993/1462] Data 0.001 (0.001) Batch 2.605 (2.879) Remain 100:55:38 loss: 0.6438 Lr: 0.00199
[2025-09-06 14:52:53,857 INFO misc.py line 117 1395871] Train: [14/100][994/1462] Data 0.001 (0.001) Batch 2.785 (2.879) Remain 100:55:23 loss: 0.5569 Lr: 0.00199
[2025-09-06 14:52:56,475 INFO misc.py line 117 1395871] Train: [14/100][995/1462] Data 0.001 (0.001) Batch 2.618 (2.879) Remain 100:54:47 loss: 0.5038 Lr: 0.00199
[2025-09-06 14:52:59,032 INFO misc.py line 117 1395871] Train: [14/100][996/1462] Data 0.002 (0.001) Batch 2.557 (2.878) Remain 100:54:03 loss: 0.4059 Lr: 0.00199
[2025-09-06 14:53:02,067 INFO misc.py line 117 1395871] Train: [14/100][997/1462] Data 0.002 (0.001) Batch 3.035 (2.879) Remain 100:54:20 loss: 0.4220 Lr: 0.00199
[2025-09-06 14:53:05,055 INFO misc.py line 117 1395871] Train: [14/100][998/1462] Data 0.002 (0.001) Batch 2.988 (2.879) Remain 100:54:31 loss: 0.3052 Lr: 0.00199
[2025-09-06 14:53:07,832 INFO misc.py line 117 1395871] Train: [14/100][999/1462] Data 0.001 (0.001) Batch 2.777 (2.879) Remain 100:54:15 loss: 0.3694 Lr: 0.00199
[2025-09-06 14:53:10,893 INFO misc.py line 117 1395871] Train: [14/100][1000/1462] Data 0.003 (0.001) Batch 3.061 (2.879) Remain 100:54:35 loss: 0.4715 Lr: 0.00199
[2025-09-06 14:53:13,950 INFO misc.py line 117 1395871] Train: [14/100][1001/1462] Data 0.001 (0.001) Batch 3.058 (2.879) Remain 100:54:55 loss: 0.3769 Lr: 0.00199
[2025-09-06 14:53:16,720 INFO misc.py line 117 1395871] Train: [14/100][1002/1462] Data 0.001 (0.001) Batch 2.770 (2.879) Remain 100:54:38 loss: 0.2894 Lr: 0.00199
[2025-09-06 14:53:19,766 INFO misc.py line 117 1395871] Train: [14/100][1003/1462] Data 0.001 (0.001) Batch 3.046 (2.879) Remain 100:54:57 loss: 0.3032 Lr: 0.00199
[2025-09-06 14:53:22,640 INFO misc.py line 117 1395871] Train: [14/100][1004/1462] Data 0.001 (0.001) Batch 2.875 (2.879) Remain 100:54:53 loss: 0.2869 Lr: 0.00199
[2025-09-06 14:53:25,536 INFO misc.py line 117 1395871] Train: [14/100][1005/1462] Data 0.002 (0.001) Batch 2.895 (2.879) Remain 100:54:52 loss: 0.3099 Lr: 0.00199
[2025-09-06 14:53:28,656 INFO misc.py line 117 1395871] Train: [14/100][1006/1462] Data 0.002 (0.001) Batch 3.120 (2.879) Remain 100:55:20 loss: 0.6365 Lr: 0.00199
[2025-09-06 14:53:31,566 INFO misc.py line 117 1395871] Train: [14/100][1007/1462] Data 0.001 (0.001) Batch 2.910 (2.879) Remain 100:55:21 loss: 0.3757 Lr: 0.00199
[2025-09-06 14:53:34,305 INFO misc.py line 117 1395871] Train: [14/100][1008/1462] Data 0.001 (0.001) Batch 2.739 (2.879) Remain 100:55:00 loss: 0.7638 Lr: 0.00199
[2025-09-06 14:53:36,976 INFO misc.py line 117 1395871] Train: [14/100][1009/1462] Data 0.002 (0.001) Batch 2.671 (2.879) Remain 100:54:31 loss: 0.5943 Lr: 0.00199
[2025-09-06 14:53:40,021 INFO misc.py line 117 1395871] Train: [14/100][1010/1462] Data 0.002 (0.001) Batch 3.045 (2.879) Remain 100:54:49 loss: 0.2890 Lr: 0.00199
[2025-09-06 14:53:43,182 INFO misc.py line 117 1395871] Train: [14/100][1011/1462] Data 0.001 (0.001) Batch 3.161 (2.879) Remain 100:55:22 loss: 0.5048 Lr: 0.00199
[2025-09-06 14:53:46,040 INFO misc.py line 117 1395871] Train: [14/100][1012/1462] Data 0.001 (0.001) Batch 2.858 (2.879) Remain 100:55:16 loss: 0.3787 Lr: 0.00199
[2025-09-06 14:53:49,007 INFO misc.py line 117 1395871] Train: [14/100][1013/1462] Data 0.001 (0.001) Batch 2.968 (2.879) Remain 100:55:24 loss: 0.3945 Lr: 0.00199
[2025-09-06 14:53:51,662 INFO misc.py line 117 1395871] Train: [14/100][1014/1462] Data 0.001 (0.001) Batch 2.654 (2.879) Remain 100:54:53 loss: 0.2598 Lr: 0.00199
[2025-09-06 14:53:54,181 INFO misc.py line 117 1395871] Train: [14/100][1015/1462] Data 0.001 (0.001) Batch 2.519 (2.879) Remain 100:54:06 loss: 0.7884 Lr: 0.00199
[2025-09-06 14:53:57,031 INFO misc.py line 117 1395871] Train: [14/100][1016/1462] Data 0.002 (0.001) Batch 2.850 (2.879) Remain 100:53:59 loss: 0.2568 Lr: 0.00199
[2025-09-06 14:54:00,295 INFO misc.py line 117 1395871] Train: [14/100][1017/1462] Data 0.002 (0.001) Batch 3.264 (2.879) Remain 100:54:44 loss: 0.3641 Lr: 0.00199
[2025-09-06 14:54:03,537 INFO misc.py line 117 1395871] Train: [14/100][1018/1462] Data 0.001 (0.001) Batch 3.242 (2.880) Remain 100:55:26 loss: 0.2531 Lr: 0.00199
[2025-09-06 14:54:06,330 INFO misc.py line 117 1395871] Train: [14/100][1019/1462] Data 0.001 (0.001) Batch 2.793 (2.879) Remain 100:55:13 loss: 0.3810 Lr: 0.00199
[2025-09-06 14:54:09,289 INFO misc.py line 117 1395871] Train: [14/100][1020/1462] Data 0.001 (0.001) Batch 2.959 (2.880) Remain 100:55:20 loss: 0.4048 Lr: 0.00199
[2025-09-06 14:54:11,801 INFO misc.py line 117 1395871] Train: [14/100][1021/1462] Data 0.001 (0.001) Batch 2.512 (2.879) Remain 100:54:31 loss: 0.4937 Lr: 0.00199
[2025-09-06 14:54:14,693 INFO misc.py line 117 1395871] Train: [14/100][1022/1462] Data 0.002 (0.001) Batch 2.892 (2.879) Remain 100:54:30 loss: 0.5065 Lr: 0.00199
[2025-09-06 14:54:17,946 INFO misc.py line 117 1395871] Train: [14/100][1023/1462] Data 0.002 (0.001) Batch 3.253 (2.880) Remain 100:55:13 loss: 0.2949 Lr: 0.00199
[2025-09-06 14:54:20,692 INFO misc.py line 117 1395871] Train: [14/100][1024/1462] Data 0.002 (0.001) Batch 2.745 (2.879) Remain 100:54:54 loss: 0.2745 Lr: 0.00199
[2025-09-06 14:54:23,315 INFO misc.py line 117 1395871] Train: [14/100][1025/1462] Data 0.001 (0.001) Batch 2.623 (2.879) Remain 100:54:20 loss: 0.4316 Lr: 0.00199
[2025-09-06 14:54:26,253 INFO misc.py line 117 1395871] Train: [14/100][1026/1462] Data 0.001 (0.001) Batch 2.938 (2.879) Remain 100:54:24 loss: 0.5600 Lr: 0.00199
[2025-09-06 14:54:29,264 INFO misc.py line 117 1395871] Train: [14/100][1027/1462] Data 0.001 (0.001) Batch 3.011 (2.879) Remain 100:54:37 loss: 0.5685 Lr: 0.00199
[2025-09-06 14:54:32,210 INFO misc.py line 117 1395871] Train: [14/100][1028/1462] Data 0.002 (0.001) Batch 2.945 (2.879) Remain 100:54:43 loss: 0.2837 Lr: 0.00199
[2025-09-06 14:54:34,964 INFO misc.py line 117 1395871] Train: [14/100][1029/1462] Data 0.002 (0.001) Batch 2.754 (2.879) Remain 100:54:24 loss: 0.2205 Lr: 0.00199
[2025-09-06 14:54:37,558 INFO misc.py line 117 1395871] Train: [14/100][1030/1462] Data 0.002 (0.001) Batch 2.594 (2.879) Remain 100:53:46 loss: 0.4642 Lr: 0.00199
[2025-09-06 14:54:40,254 INFO misc.py line 117 1395871] Train: [14/100][1031/1462] Data 0.002 (0.001) Batch 2.696 (2.879) Remain 100:53:21 loss: 0.3107 Lr: 0.00199
[2025-09-06 14:54:43,275 INFO misc.py line 117 1395871] Train: [14/100][1032/1462] Data 0.002 (0.001) Batch 3.021 (2.879) Remain 100:53:36 loss: 0.2792 Lr: 0.00199
[2025-09-06 14:54:46,258 INFO misc.py line 117 1395871] Train: [14/100][1033/1462] Data 0.001 (0.001) Batch 2.983 (2.879) Remain 100:53:45 loss: 0.2478 Lr: 0.00199
[2025-09-06 14:54:48,917 INFO misc.py line 117 1395871] Train: [14/100][1034/1462] Data 0.001 (0.001) Batch 2.659 (2.879) Remain 100:53:16 loss: 0.3134 Lr: 0.00199
[2025-09-06 14:54:51,912 INFO misc.py line 117 1395871] Train: [14/100][1035/1462] Data 0.001 (0.001) Batch 2.996 (2.879) Remain 100:53:27 loss: 0.2666 Lr: 0.00199
[2025-09-06 14:54:54,873 INFO misc.py line 117 1395871] Train: [14/100][1036/1462] Data 0.001 (0.001) Batch 2.961 (2.879) Remain 100:53:34 loss: 0.4634 Lr: 0.00199
[2025-09-06 14:54:57,789 INFO misc.py line 117 1395871] Train: [14/100][1037/1462] Data 0.001 (0.001) Batch 2.915 (2.879) Remain 100:53:36 loss: 0.3070 Lr: 0.00199
[2025-09-06 14:55:00,649 INFO misc.py line 117 1395871] Train: [14/100][1038/1462] Data 0.002 (0.001) Batch 2.860 (2.879) Remain 100:53:30 loss: 0.6985 Lr: 0.00199
[2025-09-06 14:55:03,903 INFO misc.py line 117 1395871] Train: [14/100][1039/1462] Data 0.001 (0.001) Batch 3.255 (2.879) Remain 100:54:13 loss: 0.4524 Lr: 0.00199
[2025-09-06 14:55:06,596 INFO misc.py line 117 1395871] Train: [14/100][1040/1462] Data 0.001 (0.001) Batch 2.692 (2.879) Remain 100:53:48 loss: 0.6209 Lr: 0.00199
[2025-09-06 14:55:09,404 INFO misc.py line 117 1395871] Train: [14/100][1041/1462] Data 0.002 (0.001) Batch 2.808 (2.879) Remain 100:53:36 loss: 0.5747 Lr: 0.00199
[2025-09-06 14:55:12,237 INFO misc.py line 117 1395871] Train: [14/100][1042/1462] Data 0.001 (0.001) Batch 2.833 (2.879) Remain 100:53:28 loss: 0.4589 Lr: 0.00199
[2025-09-06 14:55:15,341 INFO misc.py line 117 1395871] Train: [14/100][1043/1462] Data 0.002 (0.001) Batch 3.104 (2.879) Remain 100:53:52 loss: 0.3703 Lr: 0.00199
[2025-09-06 14:55:18,529 INFO misc.py line 117 1395871] Train: [14/100][1044/1462] Data 0.001 (0.001) Batch 3.187 (2.880) Remain 100:54:27 loss: 0.6654 Lr: 0.00199
[2025-09-06 14:55:21,062 INFO misc.py line 117 1395871] Train: [14/100][1045/1462] Data 0.001 (0.001) Batch 2.533 (2.879) Remain 100:53:42 loss: 0.3318 Lr: 0.00199
[2025-09-06 14:55:24,317 INFO misc.py line 117 1395871] Train: [14/100][1046/1462] Data 0.001 (0.001) Batch 3.255 (2.880) Remain 100:54:24 loss: 0.2414 Lr: 0.00199
[2025-09-06 14:55:27,243 INFO misc.py line 117 1395871] Train: [14/100][1047/1462] Data 0.001 (0.001) Batch 2.926 (2.880) Remain 100:54:27 loss: 0.3196 Lr: 0.00199
[2025-09-06 14:55:30,059 INFO misc.py line 117 1395871] Train: [14/100][1048/1462] Data 0.002 (0.001) Batch 2.817 (2.880) Remain 100:54:17 loss: 0.4589 Lr: 0.00199
[2025-09-06 14:55:33,214 INFO misc.py line 117 1395871] Train: [14/100][1049/1462] Data 0.002 (0.001) Batch 3.155 (2.880) Remain 100:54:47 loss: 0.4907 Lr: 0.00199
[2025-09-06 14:55:36,308 INFO misc.py line 117 1395871] Train: [14/100][1050/1462] Data 0.001 (0.001) Batch 3.094 (2.880) Remain 100:55:10 loss: 0.4050 Lr: 0.00199
[2025-09-06 14:55:38,790 INFO misc.py line 117 1395871] Train: [14/100][1051/1462] Data 0.001 (0.001) Batch 2.481 (2.880) Remain 100:54:19 loss: 0.7594 Lr: 0.00199
[2025-09-06 14:55:41,539 INFO misc.py line 117 1395871] Train: [14/100][1052/1462] Data 0.001 (0.001) Batch 2.750 (2.880) Remain 100:54:00 loss: 0.5633 Lr: 0.00199
[2025-09-06 14:55:44,376 INFO misc.py line 117 1395871] Train: [14/100][1053/1462] Data 0.002 (0.001) Batch 2.837 (2.880) Remain 100:53:52 loss: 0.4001 Lr: 0.00199
[2025-09-06 14:55:46,869 INFO misc.py line 117 1395871] Train: [14/100][1054/1462] Data 0.001 (0.001) Batch 2.493 (2.879) Remain 100:53:03 loss: 0.2634 Lr: 0.00199
[2025-09-06 14:55:49,384 INFO misc.py line 117 1395871] Train: [14/100][1055/1462] Data 0.001 (0.001) Batch 2.515 (2.879) Remain 100:52:16 loss: 0.4034 Lr: 0.00199
[2025-09-06 14:55:52,177 INFO misc.py line 117 1395871] Train: [14/100][1056/1462] Data 0.002 (0.001) Batch 2.794 (2.879) Remain 100:52:03 loss: 0.3901 Lr: 0.00199
[2025-09-06 14:55:54,970 INFO misc.py line 117 1395871] Train: [14/100][1057/1462] Data 0.001 (0.001) Batch 2.792 (2.879) Remain 100:51:50 loss: 0.5146 Lr: 0.00199
[2025-09-06 14:55:57,795 INFO misc.py line 117 1395871] Train: [14/100][1058/1462] Data 0.002 (0.001) Batch 2.825 (2.879) Remain 100:51:41 loss: 0.3485 Lr: 0.00199
[2025-09-06 14:56:00,655 INFO misc.py line 117 1395871] Train: [14/100][1059/1462] Data 0.002 (0.001) Batch 2.860 (2.879) Remain 100:51:36 loss: 0.4469 Lr: 0.00199
[2025-09-06 14:56:03,314 INFO misc.py line 117 1395871] Train: [14/100][1060/1462] Data 0.002 (0.001) Batch 2.659 (2.878) Remain 100:51:07 loss: 0.2879 Lr: 0.00199
[2025-09-06 14:56:06,202 INFO misc.py line 117 1395871] Train: [14/100][1061/1462] Data 0.001 (0.001) Batch 2.888 (2.878) Remain 100:51:05 loss: 0.6296 Lr: 0.00199
[2025-09-06 14:56:08,846 INFO misc.py line 117 1395871] Train: [14/100][1062/1462] Data 0.001 (0.001) Batch 2.644 (2.878) Remain 100:50:34 loss: 0.2989 Lr: 0.00199
[2025-09-06 14:56:11,705 INFO misc.py line 117 1395871] Train: [14/100][1063/1462] Data 0.001 (0.001) Batch 2.859 (2.878) Remain 100:50:29 loss: 1.0226 Lr: 0.00199
[2025-09-06 14:56:14,422 INFO misc.py line 117 1395871] Train: [14/100][1064/1462] Data 0.001 (0.001) Batch 2.718 (2.878) Remain 100:50:07 loss: 0.4055 Lr: 0.00199
[2025-09-06 14:56:17,326 INFO misc.py line 117 1395871] Train: [14/100][1065/1462] Data 0.001 (0.001) Batch 2.904 (2.878) Remain 100:50:07 loss: 0.3964 Lr: 0.00199
[2025-09-06 14:56:20,258 INFO misc.py line 117 1395871] Train: [14/100][1066/1462] Data 0.002 (0.001) Batch 2.933 (2.878) Remain 100:50:11 loss: 0.2813 Lr: 0.00199
[2025-09-06 14:56:23,123 INFO misc.py line 117 1395871] Train: [14/100][1067/1462] Data 0.002 (0.001) Batch 2.865 (2.878) Remain 100:50:06 loss: 0.6602 Lr: 0.00199
[2025-09-06 14:56:26,339 INFO misc.py line 117 1395871] Train: [14/100][1068/1462] Data 0.001 (0.001) Batch 3.216 (2.878) Remain 100:50:43 loss: 0.3320 Lr: 0.00199
[2025-09-06 14:56:29,000 INFO misc.py line 117 1395871] Train: [14/100][1069/1462] Data 0.001 (0.001) Batch 2.661 (2.878) Remain 100:50:15 loss: 0.2527 Lr: 0.00199
[2025-09-06 14:56:32,004 INFO misc.py line 117 1395871] Train: [14/100][1070/1462] Data 0.002 (0.001) Batch 3.004 (2.878) Remain 100:50:27 loss: 0.3332 Lr: 0.00199
[2025-09-06 14:56:34,900 INFO misc.py line 117 1395871] Train: [14/100][1071/1462] Data 0.001 (0.001) Batch 2.897 (2.878) Remain 100:50:26 loss: 0.5166 Lr: 0.00199
[2025-09-06 14:56:37,789 INFO misc.py line 117 1395871] Train: [14/100][1072/1462] Data 0.001 (0.001) Batch 2.888 (2.878) Remain 100:50:24 loss: 0.4380 Lr: 0.00199
[2025-09-06 14:56:40,532 INFO misc.py line 117 1395871] Train: [14/100][1073/1462] Data 0.002 (0.001) Batch 2.743 (2.878) Remain 100:50:06 loss: 0.3757 Lr: 0.00199
[2025-09-06 14:56:43,277 INFO misc.py line 117 1395871] Train: [14/100][1074/1462] Data 0.001 (0.001) Batch 2.745 (2.878) Remain 100:49:47 loss: 0.3711 Lr: 0.00199
[2025-09-06 14:56:46,270 INFO misc.py line 117 1395871] Train: [14/100][1075/1462] Data 0.002 (0.001) Batch 2.993 (2.878) Remain 100:49:58 loss: 0.4324 Lr: 0.00199
[2025-09-06 14:56:49,063 INFO misc.py line 117 1395871] Train: [14/100][1076/1462] Data 0.001 (0.001) Batch 2.793 (2.878) Remain 100:49:45 loss: 0.4063 Lr: 0.00199
[2025-09-06 14:56:51,717 INFO misc.py line 117 1395871] Train: [14/100][1077/1462] Data 0.002 (0.001) Batch 2.654 (2.878) Remain 100:49:15 loss: 0.4085 Lr: 0.00199
[2025-09-06 14:56:54,541 INFO misc.py line 117 1395871] Train: [14/100][1078/1462] Data 0.001 (0.001) Batch 2.824 (2.878) Remain 100:49:06 loss: 0.2906 Lr: 0.00199
[2025-09-06 14:56:57,227 INFO misc.py line 117 1395871] Train: [14/100][1079/1462] Data 0.001 (0.001) Batch 2.685 (2.878) Remain 100:48:41 loss: 0.2625 Lr: 0.00199
[2025-09-06 14:56:59,875 INFO misc.py line 117 1395871] Train: [14/100][1080/1462] Data 0.002 (0.001) Batch 2.649 (2.877) Remain 100:48:11 loss: 0.3751 Lr: 0.00199
[2025-09-06 14:57:02,510 INFO misc.py line 117 1395871] Train: [14/100][1081/1462] Data 0.001 (0.001) Batch 2.635 (2.877) Remain 100:47:40 loss: 0.7699 Lr: 0.00199
[2025-09-06 14:57:05,691 INFO misc.py line 117 1395871] Train: [14/100][1082/1462] Data 0.002 (0.001) Batch 3.181 (2.878) Remain 100:48:12 loss: 0.2423 Lr: 0.00199
[2025-09-06 14:57:08,332 INFO misc.py line 117 1395871] Train: [14/100][1083/1462] Data 0.001 (0.001) Batch 2.641 (2.877) Remain 100:47:42 loss: 0.4385 Lr: 0.00199
[2025-09-06 14:57:11,109 INFO misc.py line 117 1395871] Train: [14/100][1084/1462] Data 0.001 (0.001) Batch 2.777 (2.877) Remain 100:47:27 loss: 0.4725 Lr: 0.00199
[2025-09-06 14:57:13,977 INFO misc.py line 117 1395871] Train: [14/100][1085/1462] Data 0.001 (0.001) Batch 2.868 (2.877) Remain 100:47:23 loss: 0.4778 Lr: 0.00199
[2025-09-06 14:57:16,680 INFO misc.py line 117 1395871] Train: [14/100][1086/1462] Data 0.002 (0.001) Batch 2.703 (2.877) Remain 100:47:00 loss: 0.3296 Lr: 0.00199
[2025-09-06 14:57:19,650 INFO misc.py line 117 1395871] Train: [14/100][1087/1462] Data 0.001 (0.001) Batch 2.969 (2.877) Remain 100:47:08 loss: 0.3313 Lr: 0.00199
[2025-09-06 14:57:22,839 INFO misc.py line 117 1395871] Train: [14/100][1088/1462] Data 0.002 (0.001) Batch 3.190 (2.877) Remain 100:47:42 loss: 0.2552 Lr: 0.00199
[2025-09-06 14:57:25,891 INFO misc.py line 117 1395871] Train: [14/100][1089/1462] Data 0.001 (0.001) Batch 3.052 (2.878) Remain 100:47:59 loss: 0.6726 Lr: 0.00199
[2025-09-06 14:57:28,291 INFO misc.py line 117 1395871] Train: [14/100][1090/1462] Data 0.001 (0.001) Batch 2.400 (2.877) Remain 100:47:01 loss: 0.5150 Lr: 0.00199
[2025-09-06 14:57:31,294 INFO misc.py line 117 1395871] Train: [14/100][1091/1462] Data 0.002 (0.001) Batch 3.003 (2.877) Remain 100:47:12 loss: 0.2294 Lr: 0.00199
[2025-09-06 14:57:34,086 INFO misc.py line 117 1395871] Train: [14/100][1092/1462] Data 0.001 (0.001) Batch 2.792 (2.877) Remain 100:47:00 loss: 0.3862 Lr: 0.00199
[2025-09-06 14:57:36,904 INFO misc.py line 117 1395871] Train: [14/100][1093/1462] Data 0.001 (0.001) Batch 2.817 (2.877) Remain 100:46:50 loss: 0.7933 Lr: 0.00199
[2025-09-06 14:57:39,770 INFO misc.py line 117 1395871] Train: [14/100][1094/1462] Data 0.002 (0.001) Batch 2.866 (2.877) Remain 100:46:46 loss: 0.3381 Lr: 0.00199
[2025-09-06 14:57:42,659 INFO misc.py line 117 1395871] Train: [14/100][1095/1462] Data 0.001 (0.001) Batch 2.889 (2.877) Remain 100:46:44 loss: 0.7543 Lr: 0.00199
[2025-09-06 14:57:45,483 INFO misc.py line 117 1395871] Train: [14/100][1096/1462] Data 0.002 (0.001) Batch 2.824 (2.877) Remain 100:46:35 loss: 0.3567 Lr: 0.00199
[2025-09-06 14:57:48,435 INFO misc.py line 117 1395871] Train: [14/100][1097/1462] Data 0.002 (0.001) Batch 2.952 (2.877) Remain 100:46:41 loss: 0.4000 Lr: 0.00199
[2025-09-06 14:57:51,195 INFO misc.py line 117 1395871] Train: [14/100][1098/1462] Data 0.001 (0.001) Batch 2.760 (2.877) Remain 100:46:25 loss: 0.3408 Lr: 0.00199
[2025-09-06 14:57:54,091 INFO misc.py line 117 1395871] Train: [14/100][1099/1462] Data 0.002 (0.001) Batch 2.897 (2.877) Remain 100:46:24 loss: 0.3541 Lr: 0.00199
[2025-09-06 14:57:56,656 INFO misc.py line 117 1395871] Train: [14/100][1100/1462] Data 0.002 (0.001) Batch 2.565 (2.877) Remain 100:45:45 loss: 0.6956 Lr: 0.00199
[2025-09-06 14:57:59,613 INFO misc.py line 117 1395871] Train: [14/100][1101/1462] Data 0.002 (0.001) Batch 2.957 (2.877) Remain 100:45:52 loss: 0.5267 Lr: 0.00199
[2025-09-06 14:58:02,411 INFO misc.py line 117 1395871] Train: [14/100][1102/1462] Data 0.002 (0.001) Batch 2.798 (2.877) Remain 100:45:40 loss: 0.3216 Lr: 0.00199
[2025-09-06 14:58:05,136 INFO misc.py line 117 1395871] Train: [14/100][1103/1462] Data 0.002 (0.001) Batch 2.725 (2.877) Remain 100:45:19 loss: 0.3114 Lr: 0.00199
[2025-09-06 14:58:07,716 INFO misc.py line 117 1395871] Train: [14/100][1104/1462] Data 0.002 (0.001) Batch 2.580 (2.876) Remain 100:44:42 loss: 0.6187 Lr: 0.00199
[2025-09-06 14:58:10,511 INFO misc.py line 117 1395871] Train: [14/100][1105/1462] Data 0.002 (0.001) Batch 2.796 (2.876) Remain 100:44:30 loss: 0.6663 Lr: 0.00199
[2025-09-06 14:58:13,478 INFO misc.py line 117 1395871] Train: [14/100][1106/1462] Data 0.002 (0.001) Batch 2.967 (2.876) Remain 100:44:38 loss: 0.3892 Lr: 0.00199
[2025-09-06 14:58:16,489 INFO misc.py line 117 1395871] Train: [14/100][1107/1462] Data 0.002 (0.001) Batch 3.010 (2.877) Remain 100:44:50 loss: 0.5513 Lr: 0.00199
[2025-09-06 14:58:19,405 INFO misc.py line 117 1395871] Train: [14/100][1108/1462] Data 0.001 (0.001) Batch 2.916 (2.877) Remain 100:44:52 loss: 0.4355 Lr: 0.00199
[2025-09-06 14:58:22,208 INFO misc.py line 117 1395871] Train: [14/100][1109/1462] Data 0.001 (0.001) Batch 2.803 (2.876) Remain 100:44:41 loss: 0.4749 Lr: 0.00199
[2025-09-06 14:58:25,233 INFO misc.py line 117 1395871] Train: [14/100][1110/1462] Data 0.002 (0.001) Batch 3.026 (2.877) Remain 100:44:55 loss: 0.4500 Lr: 0.00199
[2025-09-06 14:58:28,080 INFO misc.py line 117 1395871] Train: [14/100][1111/1462] Data 0.002 (0.001) Batch 2.846 (2.877) Remain 100:44:48 loss: 0.3348 Lr: 0.00199
[2025-09-06 14:58:30,897 INFO misc.py line 117 1395871] Train: [14/100][1112/1462] Data 0.002 (0.001) Batch 2.817 (2.877) Remain 100:44:39 loss: 0.6428 Lr: 0.00199
[2025-09-06 14:58:33,609 INFO misc.py line 117 1395871] Train: [14/100][1113/1462] Data 0.001 (0.001) Batch 2.712 (2.876) Remain 100:44:17 loss: 0.4438 Lr: 0.00199
[2025-09-06 14:58:36,453 INFO misc.py line 117 1395871] Train: [14/100][1114/1462] Data 0.001 (0.001) Batch 2.844 (2.876) Remain 100:44:11 loss: 0.3974 Lr: 0.00199
[2025-09-06 14:58:39,554 INFO misc.py line 117 1395871] Train: [14/100][1115/1462] Data 0.002 (0.001) Batch 3.101 (2.877) Remain 100:44:33 loss: 0.3230 Lr: 0.00199
[2025-09-06 14:58:42,704 INFO misc.py line 117 1395871] Train: [14/100][1116/1462] Data 0.001 (0.001) Batch 3.149 (2.877) Remain 100:45:01 loss: 0.6094 Lr: 0.00199
[2025-09-06 14:58:45,251 INFO misc.py line 117 1395871] Train: [14/100][1117/1462] Data 0.002 (0.001) Batch 2.547 (2.877) Remain 100:44:21 loss: 0.5806 Lr: 0.00199
[2025-09-06 14:58:48,391 INFO misc.py line 117 1395871] Train: [14/100][1118/1462] Data 0.002 (0.001) Batch 3.140 (2.877) Remain 100:44:48 loss: 0.3086 Lr: 0.00199
[2025-09-06 14:58:51,204 INFO misc.py line 117 1395871] Train: [14/100][1119/1462] Data 0.001 (0.001) Batch 2.813 (2.877) Remain 100:44:38 loss: 0.4593 Lr: 0.00199
[2025-09-06 14:58:54,318 INFO misc.py line 117 1395871] Train: [14/100][1120/1462] Data 0.002 (0.001) Batch 3.114 (2.877) Remain 100:45:02 loss: 0.4016 Lr: 0.00199
[2025-09-06 14:58:57,020 INFO misc.py line 117 1395871] Train: [14/100][1121/1462] Data 0.001 (0.001) Batch 2.702 (2.877) Remain 100:44:39 loss: 0.2150 Lr: 0.00199
[2025-09-06 14:59:00,061 INFO misc.py line 117 1395871] Train: [14/100][1122/1462] Data 0.002 (0.001) Batch 3.041 (2.877) Remain 100:44:55 loss: 0.4301 Lr: 0.00199
[2025-09-06 14:59:03,420 INFO misc.py line 117 1395871] Train: [14/100][1123/1462] Data 0.001 (0.001) Batch 3.358 (2.877) Remain 100:45:46 loss: 0.4512 Lr: 0.00199
[2025-09-06 14:59:06,288 INFO misc.py line 117 1395871] Train: [14/100][1124/1462] Data 0.001 (0.001) Batch 2.869 (2.877) Remain 100:45:42 loss: 0.4089 Lr: 0.00199
[2025-09-06 14:59:09,247 INFO misc.py line 117 1395871] Train: [14/100][1125/1462] Data 0.002 (0.001) Batch 2.959 (2.877) Remain 100:45:49 loss: 0.5580 Lr: 0.00199
[2025-09-06 14:59:11,885 INFO misc.py line 117 1395871] Train: [14/100][1126/1462] Data 0.002 (0.001) Batch 2.638 (2.877) Remain 100:45:19 loss: 0.5590 Lr: 0.00199
[2025-09-06 14:59:14,743 INFO misc.py line 117 1395871] Train: [14/100][1127/1462] Data 0.001 (0.001) Batch 2.858 (2.877) Remain 100:45:14 loss: 0.6945 Lr: 0.00199
[2025-09-06 14:59:17,682 INFO misc.py line 117 1395871] Train: [14/100][1128/1462] Data 0.002 (0.001) Batch 2.939 (2.877) Remain 100:45:18 loss: 0.6215 Lr: 0.00199
[2025-09-06 14:59:20,316 INFO misc.py line 117 1395871] Train: [14/100][1129/1462] Data 0.002 (0.001) Batch 2.634 (2.877) Remain 100:44:48 loss: 0.2034 Lr: 0.00199
[2025-09-06 14:59:22,905 INFO misc.py line 117 1395871] Train: [14/100][1130/1462] Data 0.002 (0.001) Batch 2.589 (2.877) Remain 100:44:13 loss: 0.3287 Lr: 0.00199
[2025-09-06 14:59:25,826 INFO misc.py line 117 1395871] Train: [14/100][1131/1462] Data 0.002 (0.001) Batch 2.921 (2.877) Remain 100:44:15 loss: 0.3763 Lr: 0.00199
[2025-09-06 14:59:28,500 INFO misc.py line 117 1395871] Train: [14/100][1132/1462] Data 0.002 (0.001) Batch 2.674 (2.877) Remain 100:43:49 loss: 0.3947 Lr: 0.00199
[2025-09-06 14:59:31,315 INFO misc.py line 117 1395871] Train: [14/100][1133/1462] Data 0.001 (0.001) Batch 2.815 (2.877) Remain 100:43:40 loss: 0.2578 Lr: 0.00199
[2025-09-06 14:59:34,179 INFO misc.py line 117 1395871] Train: [14/100][1134/1462] Data 0.002 (0.001) Batch 2.863 (2.877) Remain 100:43:35 loss: 0.2465 Lr: 0.00199
[2025-09-06 14:59:37,022 INFO misc.py line 117 1395871] Train: [14/100][1135/1462] Data 0.002 (0.001) Batch 2.843 (2.877) Remain 100:43:29 loss: 0.4912 Lr: 0.00199
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [64,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [65,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [66,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [67,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [68,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [69,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [70,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [71,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [72,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [73,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [74,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [75,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [76,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [77,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [78,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [79,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [80,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [81,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [82,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [83,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [84,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [85,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [86,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [87,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [88,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [89,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [90,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [91,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [92,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [93,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [94,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [95,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [96,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [97,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [98,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [99,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [100,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [101,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [102,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [103,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [104,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [105,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [106,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [107,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [108,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [109,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [110,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [111,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [112,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [113,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [114,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [115,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [116,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [117,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [118,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [119,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [120,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [121,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [122,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [123,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [124,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [125,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [126,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [90,0,0], thread: [127,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [32,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [33,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [34,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [35,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [36,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [37,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [38,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [39,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [40,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [41,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [42,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [43,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [44,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [45,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [46,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [47,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [48,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [49,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [50,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [51,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [52,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [53,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [54,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [55,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [56,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [57,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [58,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [59,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [60,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [61,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [62,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [132,0,0], thread: [63,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
[2025-09-06 14:59:39,069 ERROR events.py line 611 1395872] Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 175, in train
    self.run_step()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 202, in run_step
    self.scaler.scale(loss).backward()
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_tensor.py", line 581, in backward
    torch.autograd.backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/__init__.py", line 347, in backward
    _engine_run_backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/graph.py", line 825, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


[rank1]:[W906 14:59:39.566015527 CUDAGuardImpl.h:119] Warning: CUDA warning: device-side assert triggered (function destroyEvent)
terminate called after throwing an instance of 'c10::Error'
  what():  CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

Exception raised from c10_cuda_check_implementation at /opt/conda/conda-bld/pytorch_1728929546833/work/c10/cuda/CUDAException.cpp:43 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::string) + 0x96 (0x7f94cef25446 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: c10::detail::torchCheckFail(char const*, char const*, unsigned int, std::string const&) + 0x64 (0x7f94ceecf6e4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #2: c10::cuda::c10_cuda_check_implementation(int, char const*, char const*, int, bool) + 0x118 (0x7f952119fa18 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #3: <unknown function> + 0x1f92e (0x7f952116692e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #4: <unknown function> + 0x20a57 (0x7f9521167a57 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #5: <unknown function> + 0x20c5f (0x7f9521167c5f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #6: <unknown function> + 0x5faf70 (0x7f95200c6f70 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #7: <unknown function> + 0x6f69f (0x7f94cef0669f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #8: c10::TensorImpl::~TensorImpl() + 0x21b (0x7f94ceeff37b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #9: c10::TensorImpl::~TensorImpl() + 0x9 (0x7f94ceeff529 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #10: c10d::Reducer::~Reducer() + 0x5c4 (0x7f9517d863b4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #11: std::_Sp_counted_ptr<c10d::Reducer*, (__gnu_cxx::_Lock_policy)2>::_M_dispose() + 0x12 (0x7f952086ebf2 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #12: std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release() + 0x48 (0x7f951ff8a078 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #13: <unknown function> + 0xdadc21 (0x7f9520879c21 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #14: <unknown function> + 0x4c90f3 (0x7f951ff950f3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #15: <unknown function> + 0x4c9c71 (0x7f951ff95c71 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #16: <unknown function> + 0x128b86 (0x559a7bed3b86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #17: <unknown function> + 0x14b2a0 (0x559a7bef62a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #18: <unknown function> + 0x128b86 (0x559a7bed3b86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #19: <unknown function> + 0x14b2a0 (0x559a7bef62a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #20: <unknown function> + 0x134be7 (0x559a7bedfbe7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #21: <unknown function> + 0x134ccb (0x559a7bedfccb in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #22: <unknown function> + 0x144c3b (0x559a7beefc3b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #23: <unknown function> + 0x149f1c (0x559a7bef4f1c in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #24: <unknown function> + 0x121412 (0x559a7becc412 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #25: <unknown function> + 0x203bb1 (0x559a7bfaebb1 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #26: _PyEval_EvalFrameDefault + 0x46cf (0x559a7bedb77f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #27: _PyFunction_Vectorcall + 0x6c (0x559a7bee70ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #28: _PyEval_EvalFrameDefault + 0x700 (0x559a7bed77b0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #29: _PyFunction_Vectorcall + 0x6c (0x559a7bee70ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #30: _PyEval_EvalFrameDefault + 0x30c (0x559a7bed73bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #31: _PyFunction_Vectorcall + 0x6c (0x559a7bee70ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #32: _PyEval_EvalFrameDefault + 0x1340 (0x559a7bed83f0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #33: <unknown function> + 0x1cc3bc (0x559a7bf773bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #34: PyEval_EvalCode + 0x87 (0x559a7bf77307 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #35: <unknown function> + 0x1fc96a (0x559a7bfa796a in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #36: <unknown function> + 0x1f7df3 (0x559a7bfa2df3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #37: PyRun_StringFlags + 0x7d (0x559a7bf9b4ad in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #38: PyRun_SimpleStringFlags + 0x3c (0x559a7bf9b3ac in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #39: Py_RunMain + 0x3ba (0x559a7bf9a5ba in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #40: Py_BytesMain + 0x37 (0x559a7bf6aef7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #41: <unknown function> + 0x29d90 (0x7f9529332d90 in /lib/x86_64-linux-gnu/libc.so.6)
frame #42: __libc_start_main + 0x80 (0x7f9529332e40 in /lib/x86_64-linux-gnu/libc.so.6)
frame #43: <unknown function> + 0x1bfe0e (0x559a7bf6ae0e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)

W0906 14:59:39.806000 1395737 site-packages/torch/multiprocessing/spawn.py:160] Terminating process 1395871 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 38, in <module>
    main()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 27, in main
    launch(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 74, in launch
    mp.spawn(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 328, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 284, in start_processes
    while not context.join():
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 184, in join
    raise ProcessExitedException(
torch.multiprocessing.spawn.ProcessExitedException: process 1 terminated with signal SIGABRT
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 35 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
