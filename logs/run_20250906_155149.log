Starting training at 2025年 09月 06日 星期六 15:51:49 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-06 15:52:10,484 INFO train.py line 136 1411560] => Loading config ...
[2025-09-06 15:52:10,485 INFO train.py line 138 1411560] Save path: exp/powscan/splitSections
[2025-09-06 15:52:10,650 INFO train.py line 139 1411560] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 12
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 6
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-06 15:52:10,650 INFO train.py line 140 1411560] => Building model ...
[2025-09-06 15:52:10,703 INFO train.py line 241 1411560] Num params: 3908496
[2025-09-06 15:52:10,750 INFO train.py line 142 1411560] => Building writer ...
[2025-09-06 15:52:10,752 INFO train.py line 251 1411560] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-06 15:52:10,752 INFO train.py line 144 1411560] => Building train dataset & dataloader ...
[2025-09-06 15:52:29,616 INFO defaults.py line 70 1411560] Totally 585 x 30 samples in powscan train set.
[2025-09-06 15:52:29,617 INFO train.py line 146 1411560] => Building val dataset & dataloader ...
[2025-09-06 15:52:31,013 INFO defaults.py line 70 1411560] Totally 80 x 1 samples in powscan val set.
[2025-09-06 15:52:31,013 INFO train.py line 148 1411560] => Building optimize, scheduler, scaler(amp) ...
[2025-09-06 15:52:31,163 INFO train.py line 152 1411560] => Building hooks ...
[2025-09-06 15:52:31,164 INFO misc.py line 237 1411560] => Loading checkpoint & weight ...
[2025-09-06 15:52:31,164 INFO misc.py line 239 1411560] Loading weight at: exp/powscan/splitSections/model/model_last.pth
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
[2025-09-06 15:52:31,393 INFO misc.py line 245 1411560] Loading layer weights with keyword: , replace keyword with: 
[2025-09-06 15:52:31,422 INFO misc.py line 262 1411560] Missing keys: []
[2025-09-06 15:52:31,422 INFO misc.py line 264 1411560] Resuming train at eval epoch: 13
[2025-09-06 15:52:31,429 INFO train.py line 159 1411560] >>>>>>>>>>>>>>>> Start Training >>>>>>>>>>>>>>>>
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
[2025-09-06 15:52:48,405 INFO misc.py line 117 1411560] Train: [14/100][1/1462] Data 13.421 (13.421) Batch 16.936 (16.936) Remain 598:23:17 loss: 0.2806 Lr: 0.00200
[2025-09-06 15:52:51,677 INFO misc.py line 117 1411560] Train: [14/100][2/1462] Data 0.002 (0.002) Batch 3.272 (3.272) Remain 115:36:39 loss: 0.5064 Lr: 0.00200
[2025-09-06 15:52:54,968 INFO misc.py line 117 1411560] Train: [14/100][3/1462] Data 0.002 (0.002) Batch 3.291 (3.291) Remain 116:15:33 loss: 0.3336 Lr: 0.00200
[2025-09-06 15:52:57,794 INFO misc.py line 117 1411560] Train: [14/100][4/1462] Data 0.002 (0.002) Batch 2.827 (2.827) Remain 99:52:16 loss: 0.3845 Lr: 0.00200
[2025-09-06 15:53:00,449 INFO misc.py line 117 1411560] Train: [14/100][5/1462] Data 0.001 (0.002) Batch 2.654 (2.741) Remain 96:49:30 loss: 0.3848 Lr: 0.00200
[2025-09-06 15:53:03,139 INFO misc.py line 117 1411560] Train: [14/100][6/1462] Data 0.002 (0.002) Batch 2.690 (2.724) Remain 96:13:37 loss: 0.4506 Lr: 0.00200
[2025-09-06 15:53:05,993 INFO misc.py line 117 1411560] Train: [14/100][7/1462] Data 0.001 (0.002) Batch 2.854 (2.756) Remain 97:22:37 loss: 0.2723 Lr: 0.00200
[2025-09-06 15:53:08,541 INFO misc.py line 117 1411560] Train: [14/100][8/1462] Data 0.001 (0.002) Batch 2.548 (2.715) Remain 95:54:22 loss: 0.3736 Lr: 0.00200
[2025-09-06 15:53:11,347 INFO misc.py line 117 1411560] Train: [14/100][9/1462] Data 0.002 (0.002) Batch 2.806 (2.730) Remain 96:26:42 loss: 0.7186 Lr: 0.00200
[2025-09-06 15:53:14,521 INFO misc.py line 117 1411560] Train: [14/100][10/1462] Data 0.002 (0.002) Batch 3.174 (2.793) Remain 98:41:13 loss: 0.2464 Lr: 0.00200
[2025-09-06 15:53:17,459 INFO misc.py line 117 1411560] Train: [14/100][11/1462] Data 0.002 (0.002) Batch 2.938 (2.811) Remain 99:19:29 loss: 0.2759 Lr: 0.00200
[2025-09-06 15:53:20,341 INFO misc.py line 117 1411560] Train: [14/100][12/1462] Data 0.002 (0.002) Batch 2.881 (2.819) Remain 99:35:52 loss: 0.3470 Lr: 0.00200
[2025-09-06 15:53:23,048 INFO misc.py line 117 1411560] Train: [14/100][13/1462] Data 0.001 (0.002) Batch 2.707 (2.808) Remain 99:12:05 loss: 0.3210 Lr: 0.00200
[2025-09-06 15:53:25,843 INFO misc.py line 117 1411560] Train: [14/100][14/1462] Data 0.002 (0.002) Batch 2.795 (2.807) Remain 99:09:32 loss: 0.3520 Lr: 0.00200
[2025-09-06 15:53:28,763 INFO misc.py line 117 1411560] Train: [14/100][15/1462] Data 0.001 (0.002) Batch 2.920 (2.816) Remain 99:29:31 loss: 0.3907 Lr: 0.00200
[2025-09-06 15:53:32,055 INFO misc.py line 117 1411560] Train: [14/100][16/1462] Data 0.001 (0.002) Batch 3.292 (2.853) Remain 100:47:03 loss: 0.7960 Lr: 0.00200
[2025-09-06 15:53:35,096 INFO misc.py line 117 1411560] Train: [14/100][17/1462] Data 0.002 (0.002) Batch 3.041 (2.866) Remain 101:15:28 loss: 0.3433 Lr: 0.00200
[2025-09-06 15:53:37,939 INFO misc.py line 117 1411560] Train: [14/100][18/1462] Data 0.001 (0.002) Batch 2.843 (2.865) Remain 101:12:07 loss: 0.3826 Lr: 0.00200
[2025-09-06 15:53:40,997 INFO misc.py line 117 1411560] Train: [14/100][19/1462] Data 0.002 (0.002) Batch 3.058 (2.877) Remain 101:37:39 loss: 0.4264 Lr: 0.00200
[2025-09-06 15:53:43,691 INFO misc.py line 117 1411560] Train: [14/100][20/1462] Data 0.002 (0.002) Batch 2.695 (2.866) Remain 101:14:53 loss: 0.2551 Lr: 0.00200
[2025-09-06 15:53:46,846 INFO misc.py line 117 1411560] Train: [14/100][21/1462] Data 0.001 (0.002) Batch 3.155 (2.882) Remain 101:48:48 loss: 0.3905 Lr: 0.00200
[2025-09-06 15:53:49,560 INFO misc.py line 117 1411560] Train: [14/100][22/1462] Data 0.003 (0.002) Batch 2.714 (2.873) Remain 101:29:59 loss: 0.3014 Lr: 0.00200
[2025-09-06 15:53:52,218 INFO misc.py line 117 1411560] Train: [14/100][23/1462] Data 0.001 (0.002) Batch 2.659 (2.863) Remain 101:07:11 loss: 0.7456 Lr: 0.00200
[2025-09-06 15:53:54,980 INFO misc.py line 117 1411560] Train: [14/100][24/1462] Data 0.001 (0.002) Batch 2.761 (2.858) Remain 100:56:55 loss: 0.6076 Lr: 0.00200
[2025-09-06 15:53:57,938 INFO misc.py line 117 1411560] Train: [14/100][25/1462] Data 0.001 (0.002) Batch 2.958 (2.862) Remain 101:06:35 loss: 0.5505 Lr: 0.00200
[2025-09-06 15:54:00,829 INFO misc.py line 117 1411560] Train: [14/100][26/1462] Data 0.001 (0.002) Batch 2.891 (2.864) Remain 101:09:08 loss: 0.4245 Lr: 0.00200
[2025-09-06 15:54:04,011 INFO misc.py line 117 1411560] Train: [14/100][27/1462] Data 0.001 (0.002) Batch 3.182 (2.877) Remain 101:37:13 loss: 0.3007 Lr: 0.00200
[2025-09-06 15:54:06,812 INFO misc.py line 117 1411560] Train: [14/100][28/1462] Data 0.001 (0.002) Batch 2.801 (2.874) Remain 101:30:43 loss: 0.6282 Lr: 0.00200
[2025-09-06 15:54:09,806 INFO misc.py line 117 1411560] Train: [14/100][29/1462] Data 0.002 (0.002) Batch 2.994 (2.878) Remain 101:40:29 loss: 0.5939 Lr: 0.00200
[2025-09-06 15:54:12,434 INFO misc.py line 117 1411560] Train: [14/100][30/1462] Data 0.002 (0.002) Batch 2.629 (2.869) Remain 101:20:51 loss: 0.7621 Lr: 0.00200
[2025-09-06 15:54:15,360 INFO misc.py line 117 1411560] Train: [14/100][31/1462] Data 0.001 (0.002) Batch 2.926 (2.871) Remain 101:25:06 loss: 0.4903 Lr: 0.00200
[2025-09-06 15:54:18,087 INFO misc.py line 117 1411560] Train: [14/100][32/1462] Data 0.001 (0.002) Batch 2.726 (2.866) Remain 101:14:28 loss: 0.5755 Lr: 0.00200
[2025-09-06 15:54:21,130 INFO misc.py line 117 1411560] Train: [14/100][33/1462] Data 0.002 (0.002) Batch 3.043 (2.872) Remain 101:26:55 loss: 0.3110 Lr: 0.00200
[2025-09-06 15:54:23,885 INFO misc.py line 117 1411560] Train: [14/100][34/1462] Data 0.001 (0.002) Batch 2.755 (2.868) Remain 101:18:52 loss: 0.3312 Lr: 0.00200
[2025-09-06 15:54:27,137 INFO misc.py line 117 1411560] Train: [14/100][35/1462] Data 0.001 (0.002) Batch 3.252 (2.880) Remain 101:44:14 loss: 0.3178 Lr: 0.00200
[2025-09-06 15:54:30,065 INFO misc.py line 117 1411560] Train: [14/100][36/1462] Data 0.001 (0.002) Batch 2.928 (2.882) Remain 101:47:15 loss: 0.3678 Lr: 0.00200
[2025-09-06 15:54:33,122 INFO misc.py line 117 1411560] Train: [14/100][37/1462] Data 0.001 (0.002) Batch 3.057 (2.887) Remain 101:58:08 loss: 0.6043 Lr: 0.00200
[2025-09-06 15:54:36,274 INFO misc.py line 117 1411560] Train: [14/100][38/1462] Data 0.001 (0.002) Batch 3.152 (2.894) Remain 102:14:09 loss: 0.3548 Lr: 0.00200
[2025-09-06 15:54:39,366 INFO misc.py line 117 1411560] Train: [14/100][39/1462] Data 0.001 (0.002) Batch 3.091 (2.900) Remain 102:25:41 loss: 0.3662 Lr: 0.00200
[2025-09-06 15:54:42,253 INFO misc.py line 117 1411560] Train: [14/100][40/1462] Data 0.001 (0.002) Batch 2.888 (2.900) Remain 102:24:57 loss: 0.5984 Lr: 0.00200
[2025-09-06 15:54:45,122 INFO misc.py line 117 1411560] Train: [14/100][41/1462] Data 0.001 (0.002) Batch 2.868 (2.899) Remain 102:23:09 loss: 0.2131 Lr: 0.00200
[2025-09-06 15:54:47,988 INFO misc.py line 117 1411560] Train: [14/100][42/1462] Data 0.001 (0.002) Batch 2.866 (2.898) Remain 102:21:19 loss: 0.2328 Lr: 0.00200
[2025-09-06 15:54:50,816 INFO misc.py line 117 1411560] Train: [14/100][43/1462] Data 0.001 (0.002) Batch 2.828 (2.896) Remain 102:17:35 loss: 0.2901 Lr: 0.00200
[2025-09-06 15:54:53,587 INFO misc.py line 117 1411560] Train: [14/100][44/1462] Data 0.001 (0.002) Batch 2.771 (2.893) Remain 102:11:04 loss: 0.5460 Lr: 0.00200
[2025-09-06 15:54:56,555 INFO misc.py line 117 1411560] Train: [14/100][45/1462] Data 0.002 (0.002) Batch 2.968 (2.895) Remain 102:14:48 loss: 0.3931 Lr: 0.00200
[2025-09-06 15:54:59,582 INFO misc.py line 117 1411560] Train: [14/100][46/1462] Data 0.002 (0.002) Batch 3.026 (2.898) Remain 102:21:14 loss: 0.4379 Lr: 0.00200
[2025-09-06 15:55:02,346 INFO misc.py line 117 1411560] Train: [14/100][47/1462] Data 0.001 (0.002) Batch 2.764 (2.895) Remain 102:14:44 loss: 0.3612 Lr: 0.00200
[2025-09-06 15:55:05,161 INFO misc.py line 117 1411560] Train: [14/100][48/1462] Data 0.001 (0.002) Batch 2.815 (2.893) Remain 102:10:56 loss: 0.4049 Lr: 0.00200
[2025-09-06 15:55:08,096 INFO misc.py line 117 1411560] Train: [14/100][49/1462] Data 0.002 (0.002) Batch 2.935 (2.894) Remain 102:12:50 loss: 0.4247 Lr: 0.00200
[2025-09-06 15:55:11,036 INFO misc.py line 117 1411560] Train: [14/100][50/1462] Data 0.001 (0.002) Batch 2.940 (2.895) Remain 102:14:51 loss: 0.7000 Lr: 0.00200
[2025-09-06 15:55:13,616 INFO misc.py line 117 1411560] Train: [14/100][51/1462] Data 0.001 (0.002) Batch 2.579 (2.888) Remain 102:00:51 loss: 0.5204 Lr: 0.00200
[2025-09-06 15:55:16,312 INFO misc.py line 117 1411560] Train: [14/100][52/1462] Data 0.002 (0.002) Batch 2.696 (2.885) Remain 101:52:30 loss: 0.3152 Lr: 0.00200
[2025-09-06 15:55:19,212 INFO misc.py line 117 1411560] Train: [14/100][53/1462] Data 0.002 (0.002) Batch 2.900 (2.885) Remain 101:53:05 loss: 0.5952 Lr: 0.00200
[2025-09-06 15:55:22,347 INFO misc.py line 117 1411560] Train: [14/100][54/1462] Data 0.002 (0.002) Batch 3.136 (2.890) Remain 102:03:28 loss: 0.2296 Lr: 0.00200
[2025-09-06 15:55:24,883 INFO misc.py line 117 1411560] Train: [14/100][55/1462] Data 0.002 (0.002) Batch 2.535 (2.883) Remain 101:48:58 loss: 0.8914 Lr: 0.00200
[2025-09-06 15:55:27,488 INFO misc.py line 117 1411560] Train: [14/100][56/1462] Data 0.002 (0.002) Batch 2.605 (2.878) Remain 101:37:49 loss: 0.9913 Lr: 0.00200
[2025-09-06 15:55:30,316 INFO misc.py line 117 1411560] Train: [14/100][57/1462] Data 0.002 (0.002) Batch 2.829 (2.877) Remain 101:35:50 loss: 0.3684 Lr: 0.00200
[2025-09-06 15:55:33,203 INFO misc.py line 117 1411560] Train: [14/100][58/1462] Data 0.002 (0.002) Batch 2.886 (2.877) Remain 101:36:10 loss: 0.3908 Lr: 0.00200
[2025-09-06 15:55:36,293 INFO misc.py line 117 1411560] Train: [14/100][59/1462] Data 0.001 (0.002) Batch 3.090 (2.881) Remain 101:44:11 loss: 0.2867 Lr: 0.00200
[2025-09-06 15:55:38,811 INFO misc.py line 117 1411560] Train: [14/100][60/1462] Data 0.001 (0.002) Batch 2.518 (2.874) Remain 101:30:39 loss: 0.2634 Lr: 0.00200
[2025-09-06 15:55:41,385 INFO misc.py line 117 1411560] Train: [14/100][61/1462] Data 0.002 (0.002) Batch 2.574 (2.869) Remain 101:19:38 loss: 0.6207 Lr: 0.00200
[2025-09-06 15:55:44,526 INFO misc.py line 117 1411560] Train: [14/100][62/1462] Data 0.002 (0.002) Batch 3.141 (2.874) Remain 101:29:20 loss: 0.4767 Lr: 0.00200
[2025-09-06 15:55:47,162 INFO misc.py line 117 1411560] Train: [14/100][63/1462] Data 0.001 (0.002) Batch 2.636 (2.870) Remain 101:20:54 loss: 0.3400 Lr: 0.00200
[2025-09-06 15:55:49,826 INFO misc.py line 117 1411560] Train: [14/100][64/1462] Data 0.001 (0.002) Batch 2.664 (2.867) Remain 101:13:41 loss: 0.9026 Lr: 0.00200
[2025-09-06 15:55:52,404 INFO misc.py line 117 1411560] Train: [14/100][65/1462] Data 0.001 (0.002) Batch 2.577 (2.862) Remain 101:03:46 loss: 0.3493 Lr: 0.00200
[2025-09-06 15:55:55,075 INFO misc.py line 117 1411560] Train: [14/100][66/1462] Data 0.002 (0.002) Batch 2.671 (2.859) Remain 100:57:18 loss: 0.3458 Lr: 0.00200
[2025-09-06 15:55:57,749 INFO misc.py line 117 1411560] Train: [14/100][67/1462] Data 0.001 (0.002) Batch 2.674 (2.856) Remain 100:51:08 loss: 0.5726 Lr: 0.00200
[2025-09-06 15:56:00,678 INFO misc.py line 117 1411560] Train: [14/100][68/1462] Data 0.002 (0.002) Batch 2.930 (2.857) Remain 100:53:30 loss: 0.4623 Lr: 0.00200
[2025-09-06 15:56:03,511 INFO misc.py line 117 1411560] Train: [14/100][69/1462] Data 0.001 (0.002) Batch 2.832 (2.857) Remain 100:52:39 loss: 0.6361 Lr: 0.00200
[2025-09-06 15:56:06,127 INFO misc.py line 117 1411560] Train: [14/100][70/1462] Data 0.001 (0.002) Batch 2.616 (2.853) Remain 100:45:00 loss: 0.5465 Lr: 0.00200
[2025-09-06 15:56:08,949 INFO misc.py line 117 1411560] Train: [14/100][71/1462] Data 0.002 (0.002) Batch 2.822 (2.853) Remain 100:43:59 loss: 0.3564 Lr: 0.00200
[2025-09-06 15:56:11,728 INFO misc.py line 117 1411560] Train: [14/100][72/1462] Data 0.001 (0.002) Batch 2.779 (2.852) Remain 100:41:41 loss: 0.4636 Lr: 0.00200
[2025-09-06 15:56:14,425 INFO misc.py line 117 1411560] Train: [14/100][73/1462] Data 0.002 (0.002) Batch 2.696 (2.849) Remain 100:36:56 loss: 0.2378 Lr: 0.00200
[2025-09-06 15:56:17,247 INFO misc.py line 117 1411560] Train: [14/100][74/1462] Data 0.002 (0.002) Batch 2.822 (2.849) Remain 100:36:04 loss: 0.3298 Lr: 0.00200
[2025-09-06 15:56:19,949 INFO misc.py line 117 1411560] Train: [14/100][75/1462] Data 0.002 (0.002) Batch 2.702 (2.847) Remain 100:31:42 loss: 0.5682 Lr: 0.00200
[2025-09-06 15:56:22,655 INFO misc.py line 117 1411560] Train: [14/100][76/1462] Data 0.002 (0.002) Batch 2.706 (2.845) Remain 100:27:34 loss: 0.7360 Lr: 0.00200
[2025-09-06 15:56:25,488 INFO misc.py line 117 1411560] Train: [14/100][77/1462] Data 0.002 (0.002) Batch 2.833 (2.845) Remain 100:27:10 loss: 0.3560 Lr: 0.00200
[2025-09-06 15:56:28,304 INFO misc.py line 117 1411560] Train: [14/100][78/1462] Data 0.002 (0.002) Batch 2.816 (2.844) Remain 100:26:19 loss: 0.2654 Lr: 0.00200
[2025-09-06 15:56:31,745 INFO misc.py line 117 1411560] Train: [14/100][79/1462] Data 0.002 (0.002) Batch 3.441 (2.852) Remain 100:42:53 loss: 0.4198 Lr: 0.00200
[2025-09-06 15:56:34,417 INFO misc.py line 117 1411560] Train: [14/100][80/1462] Data 0.002 (0.002) Batch 2.672 (2.850) Remain 100:37:54 loss: 0.7159 Lr: 0.00200
[2025-09-06 15:56:37,444 INFO misc.py line 117 1411560] Train: [14/100][81/1462] Data 0.002 (0.002) Batch 3.027 (2.852) Remain 100:42:39 loss: 0.6940 Lr: 0.00200
[2025-09-06 15:56:40,178 INFO misc.py line 117 1411560] Train: [14/100][82/1462] Data 0.002 (0.002) Batch 2.734 (2.851) Remain 100:39:26 loss: 0.3905 Lr: 0.00200
[2025-09-06 15:56:43,076 INFO misc.py line 117 1411560] Train: [14/100][83/1462] Data 0.002 (0.002) Batch 2.897 (2.851) Remain 100:40:37 loss: 0.4334 Lr: 0.00200
[2025-09-06 15:56:46,058 INFO misc.py line 117 1411560] Train: [14/100][84/1462] Data 0.001 (0.002) Batch 2.982 (2.853) Remain 100:43:59 loss: 0.5182 Lr: 0.00200
[2025-09-06 15:56:48,974 INFO misc.py line 117 1411560] Train: [14/100][85/1462] Data 0.002 (0.002) Batch 2.917 (2.854) Remain 100:45:35 loss: 0.3576 Lr: 0.00200
[2025-09-06 15:56:51,746 INFO misc.py line 117 1411560] Train: [14/100][86/1462] Data 0.002 (0.002) Batch 2.772 (2.853) Remain 100:43:27 loss: 0.2319 Lr: 0.00200
[2025-09-06 15:56:54,425 INFO misc.py line 117 1411560] Train: [14/100][87/1462] Data 0.002 (0.002) Batch 2.679 (2.851) Remain 100:39:01 loss: 0.5961 Lr: 0.00200
[2025-09-06 15:56:57,082 INFO misc.py line 117 1411560] Train: [14/100][88/1462] Data 0.002 (0.002) Batch 2.657 (2.848) Remain 100:34:08 loss: 0.4026 Lr: 0.00200
[2025-09-06 15:57:00,041 INFO misc.py line 117 1411560] Train: [14/100][89/1462] Data 0.002 (0.002) Batch 2.959 (2.850) Remain 100:36:49 loss: 0.3600 Lr: 0.00200
[2025-09-06 15:57:03,079 INFO misc.py line 117 1411560] Train: [14/100][90/1462] Data 0.002 (0.002) Batch 3.039 (2.852) Remain 100:41:22 loss: 0.4630 Lr: 0.00200
[2025-09-06 15:57:06,065 INFO misc.py line 117 1411560] Train: [14/100][91/1462] Data 0.001 (0.002) Batch 2.986 (2.853) Remain 100:44:33 loss: 0.5893 Lr: 0.00200
[2025-09-06 15:57:09,280 INFO misc.py line 117 1411560] Train: [14/100][92/1462] Data 0.002 (0.002) Batch 3.215 (2.857) Remain 100:53:06 loss: 0.5100 Lr: 0.00200
[2025-09-06 15:57:12,362 INFO misc.py line 117 1411560] Train: [14/100][93/1462] Data 0.002 (0.002) Batch 3.082 (2.860) Remain 100:58:20 loss: 0.3637 Lr: 0.00200
[2025-09-06 15:57:15,071 INFO misc.py line 117 1411560] Train: [14/100][94/1462] Data 0.001 (0.002) Batch 2.709 (2.858) Remain 100:54:47 loss: 0.8141 Lr: 0.00200
[2025-09-06 15:57:17,981 INFO misc.py line 117 1411560] Train: [14/100][95/1462] Data 0.002 (0.002) Batch 2.910 (2.859) Remain 100:55:55 loss: 0.6914 Lr: 0.00200
[2025-09-06 15:57:20,919 INFO misc.py line 117 1411560] Train: [14/100][96/1462] Data 0.001 (0.002) Batch 2.938 (2.860) Remain 100:57:40 loss: 0.5767 Lr: 0.00200
[2025-09-06 15:57:23,634 INFO misc.py line 117 1411560] Train: [14/100][97/1462] Data 0.001 (0.002) Batch 2.715 (2.858) Remain 100:54:22 loss: 0.3902 Lr: 0.00200
[2025-09-06 15:57:26,444 INFO misc.py line 117 1411560] Train: [14/100][98/1462] Data 0.002 (0.002) Batch 2.810 (2.858) Remain 100:53:14 loss: 0.3743 Lr: 0.00200
[2025-09-06 15:57:29,275 INFO misc.py line 117 1411560] Train: [14/100][99/1462] Data 0.002 (0.002) Batch 2.832 (2.857) Remain 100:52:37 loss: 0.2842 Lr: 0.00200
[2025-09-06 15:57:31,877 INFO misc.py line 117 1411560] Train: [14/100][100/1462] Data 0.001 (0.002) Batch 2.601 (2.855) Remain 100:46:59 loss: 0.3643 Lr: 0.00200
[2025-09-06 15:57:34,724 INFO misc.py line 117 1411560] Train: [14/100][101/1462] Data 0.002 (0.002) Batch 2.848 (2.855) Remain 100:46:47 loss: 0.6801 Lr: 0.00200
[2025-09-06 15:57:37,879 INFO misc.py line 117 1411560] Train: [14/100][102/1462] Data 0.001 (0.002) Batch 3.155 (2.858) Remain 100:53:09 loss: 0.5542 Lr: 0.00200
[2025-09-06 15:57:41,132 INFO misc.py line 117 1411560] Train: [14/100][103/1462] Data 0.002 (0.002) Batch 3.253 (2.862) Remain 101:01:28 loss: 0.5186 Lr: 0.00200
[2025-09-06 15:57:43,875 INFO misc.py line 117 1411560] Train: [14/100][104/1462] Data 0.001 (0.002) Batch 2.743 (2.860) Remain 100:58:56 loss: 0.6351 Lr: 0.00200
[2025-09-06 15:57:46,440 INFO misc.py line 117 1411560] Train: [14/100][105/1462] Data 0.002 (0.002) Batch 2.565 (2.858) Remain 100:52:45 loss: 0.5037 Lr: 0.00200
[2025-09-06 15:57:49,000 INFO misc.py line 117 1411560] Train: [14/100][106/1462] Data 0.002 (0.002) Batch 2.560 (2.855) Remain 100:46:36 loss: 0.5311 Lr: 0.00200
[2025-09-06 15:57:52,210 INFO misc.py line 117 1411560] Train: [14/100][107/1462] Data 0.002 (0.002) Batch 3.210 (2.858) Remain 100:53:47 loss: 0.3803 Lr: 0.00200
[2025-09-06 15:57:55,212 INFO misc.py line 117 1411560] Train: [14/100][108/1462] Data 0.002 (0.002) Batch 3.002 (2.859) Remain 100:56:37 loss: 0.5379 Lr: 0.00200
[2025-09-06 15:57:58,309 INFO misc.py line 117 1411560] Train: [14/100][109/1462] Data 0.001 (0.002) Batch 3.098 (2.862) Remain 101:01:20 loss: 0.2218 Lr: 0.00200
[2025-09-06 15:58:01,179 INFO misc.py line 117 1411560] Train: [14/100][110/1462] Data 0.001 (0.002) Batch 2.869 (2.862) Remain 101:01:26 loss: 0.5661 Lr: 0.00200
[2025-09-06 15:58:03,934 INFO misc.py line 117 1411560] Train: [14/100][111/1462] Data 0.002 (0.002) Batch 2.755 (2.861) Remain 100:59:18 loss: 0.3777 Lr: 0.00200
[2025-09-06 15:58:07,329 INFO misc.py line 117 1411560] Train: [14/100][112/1462] Data 0.002 (0.002) Batch 3.395 (2.866) Remain 101:09:38 loss: 0.3172 Lr: 0.00200
[2025-09-06 15:58:10,380 INFO misc.py line 117 1411560] Train: [14/100][113/1462] Data 0.002 (0.002) Batch 3.051 (2.867) Remain 101:13:09 loss: 2.0973 Lr: 0.00200
[2025-09-06 15:58:13,267 INFO misc.py line 117 1411560] Train: [14/100][114/1462] Data 0.002 (0.002) Batch 2.887 (2.868) Remain 101:13:29 loss: 0.3754 Lr: 0.00200
[2025-09-06 15:58:16,243 INFO misc.py line 117 1411560] Train: [14/100][115/1462] Data 0.001 (0.002) Batch 2.976 (2.869) Remain 101:15:29 loss: 0.6177 Lr: 0.00200
[2025-09-06 15:58:19,166 INFO misc.py line 117 1411560] Train: [14/100][116/1462] Data 0.002 (0.002) Batch 2.923 (2.869) Remain 101:16:27 loss: 0.6897 Lr: 0.00200
[2025-09-06 15:58:22,504 INFO misc.py line 117 1411560] Train: [14/100][117/1462] Data 0.001 (0.002) Batch 3.338 (2.873) Remain 101:25:07 loss: 0.4941 Lr: 0.00200
[2025-09-06 15:58:25,550 INFO misc.py line 117 1411560] Train: [14/100][118/1462] Data 0.001 (0.002) Batch 3.046 (2.875) Remain 101:28:15 loss: 0.4423 Lr: 0.00200
[2025-09-06 15:58:28,380 INFO misc.py line 117 1411560] Train: [14/100][119/1462] Data 0.002 (0.002) Batch 2.830 (2.874) Remain 101:27:24 loss: 0.5832 Lr: 0.00200
[2025-09-06 15:58:31,204 INFO misc.py line 117 1411560] Train: [14/100][120/1462] Data 0.002 (0.002) Batch 2.824 (2.874) Remain 101:26:26 loss: 0.6316 Lr: 0.00200
[2025-09-06 15:58:34,048 INFO misc.py line 117 1411560] Train: [14/100][121/1462] Data 0.001 (0.002) Batch 2.844 (2.874) Remain 101:25:52 loss: 0.4269 Lr: 0.00200
[2025-09-06 15:58:36,532 INFO misc.py line 117 1411560] Train: [14/100][122/1462] Data 0.002 (0.002) Batch 2.484 (2.870) Remain 101:18:53 loss: 0.4872 Lr: 0.00200
[2025-09-06 15:58:39,300 INFO misc.py line 117 1411560] Train: [14/100][123/1462] Data 0.001 (0.002) Batch 2.767 (2.869) Remain 101:17:01 loss: 0.6186 Lr: 0.00200
[2025-09-06 15:58:42,272 INFO misc.py line 117 1411560] Train: [14/100][124/1462] Data 0.001 (0.002) Batch 2.972 (2.870) Remain 101:18:46 loss: 0.3356 Lr: 0.00200
[2025-09-06 15:58:45,242 INFO misc.py line 117 1411560] Train: [14/100][125/1462] Data 0.002 (0.002) Batch 2.970 (2.871) Remain 101:20:27 loss: 0.4490 Lr: 0.00200
[2025-09-06 15:58:47,845 INFO misc.py line 117 1411560] Train: [14/100][126/1462] Data 0.002 (0.002) Batch 2.603 (2.869) Remain 101:15:47 loss: 0.5693 Lr: 0.00200
[2025-09-06 15:58:50,742 INFO misc.py line 117 1411560] Train: [14/100][127/1462] Data 0.001 (0.002) Batch 2.897 (2.869) Remain 101:16:13 loss: 0.4114 Lr: 0.00200
[2025-09-06 15:58:53,760 INFO misc.py line 117 1411560] Train: [14/100][128/1462] Data 0.001 (0.002) Batch 3.018 (2.870) Remain 101:18:42 loss: 0.4051 Lr: 0.00200
[2025-09-06 15:58:56,274 INFO misc.py line 117 1411560] Train: [14/100][129/1462] Data 0.002 (0.002) Batch 2.514 (2.868) Remain 101:12:39 loss: 0.2826 Lr: 0.00200
[2025-09-06 15:58:59,008 INFO misc.py line 117 1411560] Train: [14/100][130/1462] Data 0.001 (0.002) Batch 2.734 (2.866) Remain 101:10:23 loss: 0.2982 Lr: 0.00200
[2025-09-06 15:59:02,238 INFO misc.py line 117 1411560] Train: [14/100][131/1462] Data 0.001 (0.002) Batch 3.230 (2.869) Remain 101:16:21 loss: 0.5143 Lr: 0.00200
[2025-09-06 15:59:05,224 INFO misc.py line 117 1411560] Train: [14/100][132/1462] Data 0.002 (0.002) Batch 2.986 (2.870) Remain 101:18:13 loss: 0.3736 Lr: 0.00200
[2025-09-06 15:59:08,270 INFO misc.py line 117 1411560] Train: [14/100][133/1462] Data 0.002 (0.002) Batch 3.046 (2.872) Remain 101:21:02 loss: 0.7286 Lr: 0.00200
[2025-09-06 15:59:11,355 INFO misc.py line 117 1411560] Train: [14/100][134/1462] Data 0.001 (0.002) Batch 3.084 (2.873) Remain 101:24:26 loss: 0.6353 Lr: 0.00200
[2025-09-06 15:59:14,621 INFO misc.py line 117 1411560] Train: [14/100][135/1462] Data 0.001 (0.002) Batch 3.266 (2.876) Remain 101:30:41 loss: 0.3426 Lr: 0.00200
[2025-09-06 15:59:17,500 INFO misc.py line 117 1411560] Train: [14/100][136/1462] Data 0.001 (0.002) Batch 2.879 (2.876) Remain 101:30:41 loss: 0.5958 Lr: 0.00200
[2025-09-06 15:59:20,060 INFO misc.py line 117 1411560] Train: [14/100][137/1462] Data 0.002 (0.002) Batch 2.560 (2.874) Remain 101:25:38 loss: 0.5411 Lr: 0.00200
[2025-09-06 15:59:23,060 INFO misc.py line 117 1411560] Train: [14/100][138/1462] Data 0.001 (0.002) Batch 3.000 (2.875) Remain 101:27:34 loss: 0.3847 Lr: 0.00200
[2025-09-06 15:59:25,910 INFO misc.py line 117 1411560] Train: [14/100][139/1462] Data 0.001 (0.002) Batch 2.850 (2.875) Remain 101:27:09 loss: 0.4764 Lr: 0.00200
[2025-09-06 15:59:28,628 INFO misc.py line 117 1411560] Train: [14/100][140/1462] Data 0.001 (0.002) Batch 2.718 (2.873) Remain 101:24:41 loss: 0.3573 Lr: 0.00200
[2025-09-06 15:59:31,529 INFO misc.py line 117 1411560] Train: [14/100][141/1462] Data 0.001 (0.002) Batch 2.901 (2.874) Remain 101:25:03 loss: 0.3307 Lr: 0.00200
[2025-09-06 15:59:34,336 INFO misc.py line 117 1411560] Train: [14/100][142/1462] Data 0.002 (0.002) Batch 2.807 (2.873) Remain 101:23:59 loss: 0.5821 Lr: 0.00200
[2025-09-06 15:59:36,971 INFO misc.py line 117 1411560] Train: [14/100][143/1462] Data 0.001 (0.002) Batch 2.635 (2.871) Remain 101:20:20 loss: 0.4744 Lr: 0.00200
[2025-09-06 15:59:39,942 INFO misc.py line 117 1411560] Train: [14/100][144/1462] Data 0.002 (0.002) Batch 2.971 (2.872) Remain 101:21:47 loss: 0.2863 Lr: 0.00200
[2025-09-06 15:59:42,648 INFO misc.py line 117 1411560] Train: [14/100][145/1462] Data 0.001 (0.002) Batch 2.706 (2.871) Remain 101:19:15 loss: 0.4108 Lr: 0.00200
[2025-09-06 15:59:45,837 INFO misc.py line 117 1411560] Train: [14/100][146/1462] Data 0.001 (0.002) Batch 3.189 (2.873) Remain 101:23:55 loss: 0.3281 Lr: 0.00200
[2025-09-06 15:59:48,586 INFO misc.py line 117 1411560] Train: [14/100][147/1462] Data 0.001 (0.002) Batch 2.749 (2.872) Remain 101:22:03 loss: 0.7737 Lr: 0.00200
[2025-09-06 15:59:51,304 INFO misc.py line 117 1411560] Train: [14/100][148/1462] Data 0.001 (0.002) Batch 2.718 (2.871) Remain 101:19:44 loss: 0.5481 Lr: 0.00200
[2025-09-06 15:59:54,127 INFO misc.py line 117 1411560] Train: [14/100][149/1462] Data 0.002 (0.002) Batch 2.823 (2.871) Remain 101:18:59 loss: 0.3331 Lr: 0.00200
[2025-09-06 15:59:56,912 INFO misc.py line 117 1411560] Train: [14/100][150/1462] Data 0.002 (0.002) Batch 2.785 (2.870) Remain 101:17:42 loss: 0.5461 Lr: 0.00200
[2025-09-06 15:59:59,568 INFO misc.py line 117 1411560] Train: [14/100][151/1462] Data 0.001 (0.002) Batch 2.656 (2.869) Remain 101:14:35 loss: 0.5094 Lr: 0.00200
[2025-09-06 16:00:02,355 INFO misc.py line 117 1411560] Train: [14/100][152/1462] Data 0.001 (0.002) Batch 2.787 (2.868) Remain 101:13:22 loss: 0.5773 Lr: 0.00200
[2025-09-06 16:00:05,034 INFO misc.py line 117 1411560] Train: [14/100][153/1462] Data 0.001 (0.002) Batch 2.680 (2.867) Remain 101:10:40 loss: 0.3768 Lr: 0.00200
[2025-09-06 16:00:07,850 INFO misc.py line 117 1411560] Train: [14/100][154/1462] Data 0.001 (0.002) Batch 2.816 (2.867) Remain 101:09:54 loss: 0.3186 Lr: 0.00200
[2025-09-06 16:00:10,648 INFO misc.py line 117 1411560] Train: [14/100][155/1462] Data 0.001 (0.002) Batch 2.798 (2.866) Remain 101:08:54 loss: 0.5805 Lr: 0.00200
[2025-09-06 16:00:13,407 INFO misc.py line 117 1411560] Train: [14/100][156/1462] Data 0.001 (0.002) Batch 2.759 (2.866) Remain 101:07:22 loss: 0.2785 Lr: 0.00200
[2025-09-06 16:00:16,126 INFO misc.py line 117 1411560] Train: [14/100][157/1462] Data 0.002 (0.002) Batch 2.718 (2.865) Remain 101:05:17 loss: 0.4345 Lr: 0.00200
[2025-09-06 16:00:19,074 INFO misc.py line 117 1411560] Train: [14/100][158/1462] Data 0.001 (0.002) Batch 2.948 (2.865) Remain 101:06:23 loss: 0.3498 Lr: 0.00200
[2025-09-06 16:00:22,152 INFO misc.py line 117 1411560] Train: [14/100][159/1462] Data 0.001 (0.002) Batch 3.078 (2.867) Remain 101:09:14 loss: 0.5483 Lr: 0.00200
[2025-09-06 16:00:25,164 INFO misc.py line 117 1411560] Train: [14/100][160/1462] Data 0.001 (0.002) Batch 3.011 (2.867) Remain 101:11:08 loss: 0.2249 Lr: 0.00200
[2025-09-06 16:00:27,919 INFO misc.py line 117 1411560] Train: [14/100][161/1462] Data 0.002 (0.002) Batch 2.755 (2.867) Remain 101:09:35 loss: 0.3600 Lr: 0.00200
[2025-09-06 16:00:30,876 INFO misc.py line 117 1411560] Train: [14/100][162/1462] Data 0.001 (0.002) Batch 2.957 (2.867) Remain 101:10:44 loss: 0.4818 Lr: 0.00200
[2025-09-06 16:00:33,989 INFO misc.py line 117 1411560] Train: [14/100][163/1462] Data 0.002 (0.002) Batch 3.114 (2.869) Remain 101:13:57 loss: 0.3490 Lr: 0.00200
[2025-09-06 16:00:36,820 INFO misc.py line 117 1411560] Train: [14/100][164/1462] Data 0.001 (0.002) Batch 2.830 (2.869) Remain 101:13:23 loss: 0.5099 Lr: 0.00200
[2025-09-06 16:00:39,732 INFO misc.py line 117 1411560] Train: [14/100][165/1462] Data 0.001 (0.002) Batch 2.913 (2.869) Remain 101:13:55 loss: 0.4179 Lr: 0.00200
[2025-09-06 16:00:43,723 INFO misc.py line 117 1411560] Train: [14/100][166/1462] Data 0.001 (0.002) Batch 3.990 (2.876) Remain 101:28:26 loss: 0.2575 Lr: 0.00200
[2025-09-06 16:00:46,807 INFO misc.py line 117 1411560] Train: [14/100][167/1462] Data 0.001 (0.002) Batch 3.085 (2.877) Remain 101:31:05 loss: 0.3316 Lr: 0.00200
[2025-09-06 16:00:49,616 INFO misc.py line 117 1411560] Train: [14/100][168/1462] Data 0.002 (0.002) Batch 2.809 (2.877) Remain 101:30:09 loss: 0.3370 Lr: 0.00200
[2025-09-06 16:00:52,474 INFO misc.py line 117 1411560] Train: [14/100][169/1462] Data 0.001 (0.002) Batch 2.858 (2.877) Remain 101:29:52 loss: 0.3391 Lr: 0.00200
[2025-09-06 16:00:55,210 INFO misc.py line 117 1411560] Train: [14/100][170/1462] Data 0.001 (0.002) Batch 2.736 (2.876) Remain 101:28:02 loss: 0.3753 Lr: 0.00200
[2025-09-06 16:00:57,926 INFO misc.py line 117 1411560] Train: [14/100][171/1462] Data 0.001 (0.002) Batch 2.716 (2.875) Remain 101:25:59 loss: 0.3178 Lr: 0.00200
[2025-09-06 16:01:00,852 INFO misc.py line 117 1411560] Train: [14/100][172/1462] Data 0.001 (0.002) Batch 2.926 (2.875) Remain 101:26:35 loss: 0.4575 Lr: 0.00200
[2025-09-06 16:01:04,205 INFO misc.py line 117 1411560] Train: [14/100][173/1462] Data 0.001 (0.002) Batch 3.353 (2.878) Remain 101:32:29 loss: 0.5614 Lr: 0.00200
[2025-09-06 16:01:07,103 INFO misc.py line 117 1411560] Train: [14/100][174/1462] Data 0.001 (0.002) Batch 2.897 (2.878) Remain 101:32:40 loss: 0.2834 Lr: 0.00200
[2025-09-06 16:01:10,001 INFO misc.py line 117 1411560] Train: [14/100][175/1462] Data 0.001 (0.002) Batch 2.898 (2.878) Remain 101:32:53 loss: 0.3290 Lr: 0.00200
[2025-09-06 16:01:13,013 INFO misc.py line 117 1411560] Train: [14/100][176/1462] Data 0.001 (0.002) Batch 3.012 (2.879) Remain 101:34:28 loss: 0.3168 Lr: 0.00200
[2025-09-06 16:01:16,082 INFO misc.py line 117 1411560] Train: [14/100][177/1462] Data 0.001 (0.002) Batch 3.069 (2.880) Remain 101:36:44 loss: 0.3997 Lr: 0.00200
[2025-09-06 16:01:18,753 INFO misc.py line 117 1411560] Train: [14/100][178/1462] Data 0.001 (0.002) Batch 2.671 (2.879) Remain 101:34:09 loss: 0.4136 Lr: 0.00200
[2025-09-06 16:01:21,646 INFO misc.py line 117 1411560] Train: [14/100][179/1462] Data 0.001 (0.002) Batch 2.893 (2.879) Remain 101:34:17 loss: 0.6067 Lr: 0.00200
[2025-09-06 16:01:24,583 INFO misc.py line 117 1411560] Train: [14/100][180/1462] Data 0.001 (0.002) Batch 2.937 (2.879) Remain 101:34:56 loss: 0.2830 Lr: 0.00200
[2025-09-06 16:01:27,402 INFO misc.py line 117 1411560] Train: [14/100][181/1462] Data 0.002 (0.002) Batch 2.819 (2.879) Remain 101:34:10 loss: 0.3289 Lr: 0.00200
[2025-09-06 16:01:30,780 INFO misc.py line 117 1411560] Train: [14/100][182/1462] Data 0.001 (0.002) Batch 3.378 (2.882) Remain 101:40:02 loss: 0.3693 Lr: 0.00200
[2025-09-06 16:01:33,333 INFO misc.py line 117 1411560] Train: [14/100][183/1462] Data 0.001 (0.002) Batch 2.552 (2.880) Remain 101:36:06 loss: 0.3464 Lr: 0.00200
[2025-09-06 16:01:35,916 INFO misc.py line 117 1411560] Train: [14/100][184/1462] Data 0.001 (0.002) Batch 2.583 (2.878) Remain 101:32:35 loss: 0.3416 Lr: 0.00200
[2025-09-06 16:01:38,798 INFO misc.py line 117 1411560] Train: [14/100][185/1462] Data 0.002 (0.002) Batch 2.882 (2.878) Remain 101:32:35 loss: 0.2657 Lr: 0.00200
[2025-09-06 16:01:41,806 INFO misc.py line 117 1411560] Train: [14/100][186/1462] Data 0.002 (0.002) Batch 3.008 (2.879) Remain 101:34:02 loss: 0.3802 Lr: 0.00200
[2025-09-06 16:01:44,324 INFO misc.py line 117 1411560] Train: [14/100][187/1462] Data 0.002 (0.002) Batch 2.518 (2.877) Remain 101:29:50 loss: 0.4513 Lr: 0.00200
[2025-09-06 16:01:47,216 INFO misc.py line 117 1411560] Train: [14/100][188/1462] Data 0.001 (0.002) Batch 2.892 (2.877) Remain 101:29:58 loss: 0.3757 Lr: 0.00200
[2025-09-06 16:01:50,170 INFO misc.py line 117 1411560] Train: [14/100][189/1462] Data 0.002 (0.002) Batch 2.954 (2.877) Remain 101:30:47 loss: 0.3288 Lr: 0.00200
[2025-09-06 16:01:52,795 INFO misc.py line 117 1411560] Train: [14/100][190/1462] Data 0.002 (0.002) Batch 2.625 (2.876) Remain 101:27:53 loss: 0.5855 Lr: 0.00200
[2025-09-06 16:01:55,472 INFO misc.py line 117 1411560] Train: [14/100][191/1462] Data 0.002 (0.002) Batch 2.677 (2.875) Remain 101:25:36 loss: 0.3049 Lr: 0.00200
[2025-09-06 16:01:58,231 INFO misc.py line 117 1411560] Train: [14/100][192/1462] Data 0.002 (0.002) Batch 2.759 (2.874) Remain 101:24:15 loss: 0.3394 Lr: 0.00200
[2025-09-06 16:02:01,047 INFO misc.py line 117 1411560] Train: [14/100][193/1462] Data 0.002 (0.002) Batch 2.815 (2.874) Remain 101:23:33 loss: 0.5607 Lr: 0.00200
[2025-09-06 16:02:03,958 INFO misc.py line 117 1411560] Train: [14/100][194/1462] Data 0.002 (0.002) Batch 2.911 (2.874) Remain 101:23:54 loss: 0.5684 Lr: 0.00200
[2025-09-06 16:02:06,728 INFO misc.py line 117 1411560] Train: [14/100][195/1462] Data 0.002 (0.002) Batch 2.770 (2.874) Remain 101:22:43 loss: 0.4664 Lr: 0.00200
[2025-09-06 16:02:09,444 INFO misc.py line 117 1411560] Train: [14/100][196/1462] Data 0.002 (0.002) Batch 2.716 (2.873) Remain 101:20:56 loss: 0.7406 Lr: 0.00200
[2025-09-06 16:02:12,383 INFO misc.py line 117 1411560] Train: [14/100][197/1462] Data 0.001 (0.002) Batch 2.939 (2.873) Remain 101:21:37 loss: 0.3391 Lr: 0.00200
[2025-09-06 16:02:15,354 INFO misc.py line 117 1411560] Train: [14/100][198/1462] Data 0.001 (0.002) Batch 2.971 (2.874) Remain 101:22:38 loss: 0.4154 Lr: 0.00200
[2025-09-06 16:02:18,292 INFO misc.py line 117 1411560] Train: [14/100][199/1462] Data 0.002 (0.002) Batch 2.937 (2.874) Remain 101:23:16 loss: 0.4569 Lr: 0.00200
[2025-09-06 16:02:21,339 INFO misc.py line 117 1411560] Train: [14/100][200/1462] Data 0.002 (0.002) Batch 3.047 (2.875) Remain 101:25:04 loss: 0.5008 Lr: 0.00200
[2025-09-06 16:02:23,974 INFO misc.py line 117 1411560] Train: [14/100][201/1462] Data 0.002 (0.002) Batch 2.636 (2.874) Remain 101:22:28 loss: 0.7791 Lr: 0.00200
[2025-09-06 16:02:26,968 INFO misc.py line 117 1411560] Train: [14/100][202/1462] Data 0.001 (0.002) Batch 2.994 (2.874) Remain 101:23:42 loss: 0.4561 Lr: 0.00200
[2025-09-06 16:02:29,773 INFO misc.py line 117 1411560] Train: [14/100][203/1462] Data 0.002 (0.002) Batch 2.805 (2.874) Remain 101:22:55 loss: 0.4370 Lr: 0.00200
[2025-09-06 16:02:32,624 INFO misc.py line 117 1411560] Train: [14/100][204/1462] Data 0.002 (0.002) Batch 2.851 (2.874) Remain 101:22:37 loss: 0.3859 Lr: 0.00200
[2025-09-06 16:02:35,936 INFO misc.py line 117 1411560] Train: [14/100][205/1462] Data 0.002 (0.002) Batch 3.312 (2.876) Remain 101:27:10 loss: 0.5455 Lr: 0.00200
[2025-09-06 16:02:38,903 INFO misc.py line 117 1411560] Train: [14/100][206/1462] Data 0.002 (0.002) Batch 2.966 (2.877) Remain 101:28:04 loss: 0.5005 Lr: 0.00200
[2025-09-06 16:02:41,728 INFO misc.py line 117 1411560] Train: [14/100][207/1462] Data 0.002 (0.002) Batch 2.826 (2.876) Remain 101:27:29 loss: 0.5782 Lr: 0.00200
[2025-09-06 16:02:44,675 INFO misc.py line 117 1411560] Train: [14/100][208/1462] Data 0.002 (0.002) Batch 2.946 (2.877) Remain 101:28:10 loss: 0.2449 Lr: 0.00200
[2025-09-06 16:02:47,241 INFO misc.py line 117 1411560] Train: [14/100][209/1462] Data 0.002 (0.002) Batch 2.566 (2.875) Remain 101:24:55 loss: 0.4984 Lr: 0.00200
[2025-09-06 16:02:49,960 INFO misc.py line 117 1411560] Train: [14/100][210/1462] Data 0.002 (0.002) Batch 2.719 (2.874) Remain 101:23:17 loss: 0.4670 Lr: 0.00200
[2025-09-06 16:02:52,635 INFO misc.py line 117 1411560] Train: [14/100][211/1462] Data 0.001 (0.002) Batch 2.675 (2.873) Remain 101:21:12 loss: 0.4085 Lr: 0.00200
[2025-09-06 16:02:55,429 INFO misc.py line 117 1411560] Train: [14/100][212/1462] Data 0.002 (0.002) Batch 2.794 (2.873) Remain 101:20:21 loss: 0.3917 Lr: 0.00200
[2025-09-06 16:02:58,122 INFO misc.py line 117 1411560] Train: [14/100][213/1462] Data 0.002 (0.002) Batch 2.693 (2.872) Remain 101:18:29 loss: 0.5308 Lr: 0.00200
[2025-09-06 16:03:01,011 INFO misc.py line 117 1411560] Train: [14/100][214/1462] Data 0.002 (0.002) Batch 2.890 (2.872) Remain 101:18:37 loss: 0.2985 Lr: 0.00200
[2025-09-06 16:03:03,485 INFO misc.py line 117 1411560] Train: [14/100][215/1462] Data 0.001 (0.002) Batch 2.474 (2.870) Remain 101:14:35 loss: 0.3189 Lr: 0.00200
[2025-09-06 16:03:06,325 INFO misc.py line 117 1411560] Train: [14/100][216/1462] Data 0.002 (0.002) Batch 2.840 (2.870) Remain 101:14:14 loss: 0.3689 Lr: 0.00200
[2025-09-06 16:03:08,917 INFO misc.py line 117 1411560] Train: [14/100][217/1462] Data 0.002 (0.002) Batch 2.591 (2.869) Remain 101:11:26 loss: 0.2465 Lr: 0.00200
[2025-09-06 16:03:11,616 INFO misc.py line 117 1411560] Train: [14/100][218/1462] Data 0.001 (0.002) Batch 2.699 (2.868) Remain 101:09:43 loss: 0.4758 Lr: 0.00200
[2025-09-06 16:03:14,425 INFO misc.py line 117 1411560] Train: [14/100][219/1462] Data 0.002 (0.002) Batch 2.809 (2.868) Remain 101:09:05 loss: 0.4699 Lr: 0.00200
[2025-09-06 16:03:17,387 INFO misc.py line 117 1411560] Train: [14/100][220/1462] Data 0.001 (0.002) Batch 2.963 (2.868) Remain 101:09:58 loss: 0.2911 Lr: 0.00200
[2025-09-06 16:03:20,184 INFO misc.py line 117 1411560] Train: [14/100][221/1462] Data 0.002 (0.002) Batch 2.797 (2.868) Remain 101:09:13 loss: 0.3845 Lr: 0.00200
[2025-09-06 16:03:23,423 INFO misc.py line 117 1411560] Train: [14/100][222/1462] Data 0.002 (0.002) Batch 3.239 (2.870) Remain 101:12:46 loss: 0.3625 Lr: 0.00200
[2025-09-06 16:03:26,267 INFO misc.py line 117 1411560] Train: [14/100][223/1462] Data 0.002 (0.002) Batch 2.844 (2.870) Remain 101:12:28 loss: 0.6422 Lr: 0.00200
[2025-09-06 16:03:28,781 INFO misc.py line 117 1411560] Train: [14/100][224/1462] Data 0.002 (0.002) Batch 2.514 (2.868) Remain 101:09:01 loss: 0.3472 Lr: 0.00200
[2025-09-06 16:03:31,679 INFO misc.py line 117 1411560] Train: [14/100][225/1462] Data 0.002 (0.002) Batch 2.897 (2.868) Remain 101:09:15 loss: 0.3298 Lr: 0.00200
[2025-09-06 16:03:34,532 INFO misc.py line 117 1411560] Train: [14/100][226/1462] Data 0.002 (0.002) Batch 2.853 (2.868) Remain 101:09:04 loss: 0.3484 Lr: 0.00200
[2025-09-06 16:03:37,364 INFO misc.py line 117 1411560] Train: [14/100][227/1462] Data 0.002 (0.002) Batch 2.832 (2.868) Remain 101:08:40 loss: 0.2441 Lr: 0.00200
[2025-09-06 16:03:40,325 INFO misc.py line 117 1411560] Train: [14/100][228/1462] Data 0.001 (0.002) Batch 2.962 (2.868) Remain 101:09:30 loss: 0.5733 Lr: 0.00200
[2025-09-06 16:03:43,436 INFO misc.py line 117 1411560] Train: [14/100][229/1462] Data 0.002 (0.002) Batch 3.111 (2.869) Remain 101:11:44 loss: 0.2912 Lr: 0.00200
[2025-09-06 16:03:46,268 INFO misc.py line 117 1411560] Train: [14/100][230/1462] Data 0.002 (0.002) Batch 2.832 (2.869) Remain 101:11:20 loss: 0.3092 Lr: 0.00200
[2025-09-06 16:03:49,283 INFO misc.py line 117 1411560] Train: [14/100][231/1462] Data 0.002 (0.002) Batch 3.015 (2.870) Remain 101:12:38 loss: 0.3723 Lr: 0.00200
[2025-09-06 16:03:52,199 INFO misc.py line 117 1411560] Train: [14/100][232/1462] Data 0.002 (0.002) Batch 2.916 (2.870) Remain 101:13:01 loss: 0.3266 Lr: 0.00200
[2025-09-06 16:03:55,047 INFO misc.py line 117 1411560] Train: [14/100][233/1462] Data 0.002 (0.002) Batch 2.847 (2.870) Remain 101:12:46 loss: 0.2931 Lr: 0.00200
[2025-09-06 16:03:58,265 INFO misc.py line 117 1411560] Train: [14/100][234/1462] Data 0.002 (0.002) Batch 3.218 (2.871) Remain 101:15:54 loss: 0.6616 Lr: 0.00200
[2025-09-06 16:04:01,549 INFO misc.py line 117 1411560] Train: [14/100][235/1462] Data 0.002 (0.002) Batch 3.284 (2.873) Remain 101:19:37 loss: 0.2971 Lr: 0.00200
[2025-09-06 16:04:04,613 INFO misc.py line 117 1411560] Train: [14/100][236/1462] Data 0.002 (0.002) Batch 3.064 (2.874) Remain 101:21:18 loss: 0.3795 Lr: 0.00200
[2025-09-06 16:04:07,859 INFO misc.py line 117 1411560] Train: [14/100][237/1462] Data 0.002 (0.002) Batch 3.246 (2.876) Remain 101:24:37 loss: 0.3500 Lr: 0.00200
[2025-09-06 16:04:11,068 INFO misc.py line 117 1411560] Train: [14/100][238/1462] Data 0.002 (0.002) Batch 3.209 (2.877) Remain 101:27:34 loss: 0.6212 Lr: 0.00200
[2025-09-06 16:04:13,758 INFO misc.py line 117 1411560] Train: [14/100][239/1462] Data 0.002 (0.002) Batch 2.690 (2.876) Remain 101:25:51 loss: 0.5221 Lr: 0.00200
[2025-09-06 16:04:16,653 INFO misc.py line 117 1411560] Train: [14/100][240/1462] Data 0.002 (0.002) Batch 2.895 (2.876) Remain 101:25:58 loss: 0.3092 Lr: 0.00200
[2025-09-06 16:04:19,466 INFO misc.py line 117 1411560] Train: [14/100][241/1462] Data 0.002 (0.002) Batch 2.813 (2.876) Remain 101:25:21 loss: 0.3486 Lr: 0.00200
[2025-09-06 16:04:22,428 INFO misc.py line 117 1411560] Train: [14/100][242/1462] Data 0.002 (0.002) Batch 2.962 (2.876) Remain 101:26:04 loss: 0.3817 Lr: 0.00200
[2025-09-06 16:04:25,026 INFO misc.py line 117 1411560] Train: [14/100][243/1462] Data 0.002 (0.002) Batch 2.598 (2.875) Remain 101:23:34 loss: 1.7567 Lr: 0.00200
[2025-09-06 16:04:28,727 INFO misc.py line 117 1411560] Train: [14/100][244/1462] Data 0.001 (0.002) Batch 3.701 (2.879) Remain 101:30:46 loss: 0.5856 Lr: 0.00200
[2025-09-06 16:04:31,708 INFO misc.py line 117 1411560] Train: [14/100][245/1462] Data 0.002 (0.002) Batch 2.981 (2.879) Remain 101:31:37 loss: 0.3033 Lr: 0.00200
[2025-09-06 16:04:34,421 INFO misc.py line 117 1411560] Train: [14/100][246/1462] Data 0.001 (0.002) Batch 2.714 (2.878) Remain 101:30:08 loss: 0.2989 Lr: 0.00200
[2025-09-06 16:04:37,059 INFO misc.py line 117 1411560] Train: [14/100][247/1462] Data 0.002 (0.002) Batch 2.638 (2.877) Remain 101:28:00 loss: 0.4504 Lr: 0.00200
[2025-09-06 16:04:40,077 INFO misc.py line 117 1411560] Train: [14/100][248/1462] Data 0.002 (0.002) Batch 3.018 (2.878) Remain 101:29:10 loss: 0.3376 Lr: 0.00200
[2025-09-06 16:04:43,590 INFO misc.py line 117 1411560] Train: [14/100][249/1462] Data 0.001 (0.002) Batch 3.513 (2.881) Remain 101:34:34 loss: 0.5995 Lr: 0.00200
[2025-09-06 16:04:46,508 INFO misc.py line 117 1411560] Train: [14/100][250/1462] Data 0.001 (0.002) Batch 2.918 (2.881) Remain 101:34:51 loss: 0.5621 Lr: 0.00200
[2025-09-06 16:04:49,299 INFO misc.py line 117 1411560] Train: [14/100][251/1462] Data 0.001 (0.002) Batch 2.791 (2.880) Remain 101:34:02 loss: 0.6212 Lr: 0.00200
[2025-09-06 16:04:52,292 INFO misc.py line 117 1411560] Train: [14/100][252/1462] Data 0.001 (0.002) Batch 2.992 (2.881) Remain 101:34:56 loss: 0.5101 Lr: 0.00200
[2025-09-06 16:04:54,948 INFO misc.py line 117 1411560] Train: [14/100][253/1462] Data 0.001 (0.002) Batch 2.656 (2.880) Remain 101:32:59 loss: 0.4212 Lr: 0.00200
[2025-09-06 16:04:58,016 INFO misc.py line 117 1411560] Train: [14/100][254/1462] Data 0.002 (0.002) Batch 3.068 (2.881) Remain 101:34:31 loss: 0.2968 Lr: 0.00200
[2025-09-06 16:05:00,785 INFO misc.py line 117 1411560] Train: [14/100][255/1462] Data 0.001 (0.002) Batch 2.769 (2.880) Remain 101:33:32 loss: 0.3855 Lr: 0.00200
[2025-09-06 16:05:03,573 INFO misc.py line 117 1411560] Train: [14/100][256/1462] Data 0.001 (0.002) Batch 2.788 (2.880) Remain 101:32:43 loss: 0.5524 Lr: 0.00200
[2025-09-06 16:05:06,508 INFO misc.py line 117 1411560] Train: [14/100][257/1462] Data 0.001 (0.002) Batch 2.935 (2.880) Remain 101:33:08 loss: 0.3147 Lr: 0.00200
[2025-09-06 16:05:09,236 INFO misc.py line 117 1411560] Train: [14/100][258/1462] Data 0.001 (0.002) Batch 2.729 (2.879) Remain 101:31:50 loss: 0.3254 Lr: 0.00200
[2025-09-06 16:05:12,405 INFO misc.py line 117 1411560] Train: [14/100][259/1462] Data 0.001 (0.002) Batch 3.169 (2.881) Remain 101:34:10 loss: 0.5076 Lr: 0.00200
[2025-09-06 16:05:15,234 INFO misc.py line 117 1411560] Train: [14/100][260/1462] Data 0.001 (0.002) Batch 2.828 (2.880) Remain 101:33:41 loss: 0.4137 Lr: 0.00200
[2025-09-06 16:05:17,926 INFO misc.py line 117 1411560] Train: [14/100][261/1462] Data 0.001 (0.002) Batch 2.692 (2.880) Remain 101:32:06 loss: 0.5658 Lr: 0.00200
[2025-09-06 16:05:20,376 INFO misc.py line 117 1411560] Train: [14/100][262/1462] Data 0.001 (0.002) Batch 2.450 (2.878) Remain 101:28:33 loss: 0.5208 Lr: 0.00200
[2025-09-06 16:05:23,312 INFO misc.py line 117 1411560] Train: [14/100][263/1462] Data 0.001 (0.002) Batch 2.936 (2.878) Remain 101:28:58 loss: 0.5699 Lr: 0.00200
[2025-09-06 16:05:26,110 INFO misc.py line 117 1411560] Train: [14/100][264/1462] Data 0.001 (0.002) Batch 2.798 (2.878) Remain 101:28:16 loss: 0.7399 Lr: 0.00200
[2025-09-06 16:05:28,861 INFO misc.py line 117 1411560] Train: [14/100][265/1462] Data 0.001 (0.002) Batch 2.752 (2.877) Remain 101:27:12 loss: 0.5825 Lr: 0.00200
[2025-09-06 16:05:31,960 INFO misc.py line 117 1411560] Train: [14/100][266/1462] Data 0.001 (0.002) Batch 3.099 (2.878) Remain 101:28:56 loss: 0.3105 Lr: 0.00200
[2025-09-06 16:05:34,782 INFO misc.py line 117 1411560] Train: [14/100][267/1462] Data 0.002 (0.002) Batch 2.822 (2.878) Remain 101:28:26 loss: 0.5965 Lr: 0.00200
[2025-09-06 16:05:37,695 INFO misc.py line 117 1411560] Train: [14/100][268/1462] Data 0.001 (0.002) Batch 2.912 (2.878) Remain 101:28:39 loss: 0.2795 Lr: 0.00200
[2025-09-06 16:05:40,623 INFO misc.py line 117 1411560] Train: [14/100][269/1462] Data 0.001 (0.002) Batch 2.928 (2.878) Remain 101:29:01 loss: 0.4199 Lr: 0.00200
[2025-09-06 16:05:43,962 INFO misc.py line 117 1411560] Train: [14/100][270/1462] Data 0.001 (0.002) Batch 3.339 (2.880) Remain 101:32:37 loss: 0.4228 Lr: 0.00200
[2025-09-06 16:05:47,024 INFO misc.py line 117 1411560] Train: [14/100][271/1462] Data 0.001 (0.002) Batch 3.062 (2.881) Remain 101:34:00 loss: 0.4119 Lr: 0.00200
[2025-09-06 16:05:49,772 INFO misc.py line 117 1411560] Train: [14/100][272/1462] Data 0.002 (0.002) Batch 2.748 (2.880) Remain 101:32:54 loss: 0.4509 Lr: 0.00200
[2025-09-06 16:05:52,810 INFO misc.py line 117 1411560] Train: [14/100][273/1462] Data 0.002 (0.002) Batch 3.038 (2.881) Remain 101:34:06 loss: 0.4602 Lr: 0.00200
[2025-09-06 16:05:55,624 INFO misc.py line 117 1411560] Train: [14/100][274/1462] Data 0.001 (0.002) Batch 2.814 (2.881) Remain 101:33:32 loss: 0.2293 Lr: 0.00200
[2025-09-06 16:05:58,230 INFO misc.py line 117 1411560] Train: [14/100][275/1462] Data 0.001 (0.002) Batch 2.605 (2.880) Remain 101:31:20 loss: 0.4009 Lr: 0.00200
[2025-09-06 16:06:00,845 INFO misc.py line 117 1411560] Train: [14/100][276/1462] Data 0.002 (0.002) Batch 2.615 (2.879) Remain 101:29:14 loss: 0.2790 Lr: 0.00200
[2025-09-06 16:06:03,734 INFO misc.py line 117 1411560] Train: [14/100][277/1462] Data 0.002 (0.002) Batch 2.889 (2.879) Remain 101:29:16 loss: 0.3642 Lr: 0.00200
[2025-09-06 16:06:06,563 INFO misc.py line 117 1411560] Train: [14/100][278/1462] Data 0.002 (0.002) Batch 2.829 (2.879) Remain 101:28:51 loss: 0.7062 Lr: 0.00200
[2025-09-06 16:06:09,290 INFO misc.py line 117 1411560] Train: [14/100][279/1462] Data 0.002 (0.002) Batch 2.727 (2.878) Remain 101:27:38 loss: 0.5292 Lr: 0.00200
[2025-09-06 16:06:12,494 INFO misc.py line 117 1411560] Train: [14/100][280/1462] Data 0.002 (0.002) Batch 3.204 (2.879) Remain 101:30:04 loss: 0.5638 Lr: 0.00200
[2025-09-06 16:06:15,211 INFO misc.py line 117 1411560] Train: [14/100][281/1462] Data 0.001 (0.002) Batch 2.718 (2.879) Remain 101:28:48 loss: 0.4602 Lr: 0.00200
[2025-09-06 16:06:18,021 INFO misc.py line 117 1411560] Train: [14/100][282/1462] Data 0.001 (0.002) Batch 2.810 (2.878) Remain 101:28:14 loss: 0.4932 Lr: 0.00200
[2025-09-06 16:06:20,849 INFO misc.py line 117 1411560] Train: [14/100][283/1462] Data 0.002 (0.002) Batch 2.828 (2.878) Remain 101:27:48 loss: 0.2648 Lr: 0.00200
[2025-09-06 16:06:23,725 INFO misc.py line 117 1411560] Train: [14/100][284/1462] Data 0.001 (0.002) Batch 2.875 (2.878) Remain 101:27:44 loss: 0.4247 Lr: 0.00200
[2025-09-06 16:06:26,477 INFO misc.py line 117 1411560] Train: [14/100][285/1462] Data 0.002 (0.002) Batch 2.752 (2.878) Remain 101:26:44 loss: 0.5040 Lr: 0.00200
[2025-09-06 16:06:29,288 INFO misc.py line 117 1411560] Train: [14/100][286/1462] Data 0.001 (0.002) Batch 2.812 (2.877) Remain 101:26:12 loss: 0.3933 Lr: 0.00200
[2025-09-06 16:06:32,189 INFO misc.py line 117 1411560] Train: [14/100][287/1462] Data 0.001 (0.002) Batch 2.901 (2.878) Remain 101:26:19 loss: 0.4018 Lr: 0.00200
[2025-09-06 16:06:34,971 INFO misc.py line 117 1411560] Train: [14/100][288/1462] Data 0.002 (0.002) Batch 2.781 (2.877) Remain 101:25:34 loss: 0.3525 Lr: 0.00200
[2025-09-06 16:06:37,570 INFO misc.py line 117 1411560] Train: [14/100][289/1462] Data 0.001 (0.002) Batch 2.599 (2.876) Remain 101:23:27 loss: 0.2341 Lr: 0.00200
[2025-09-06 16:06:40,265 INFO misc.py line 117 1411560] Train: [14/100][290/1462] Data 0.002 (0.002) Batch 2.695 (2.876) Remain 101:22:04 loss: 0.3853 Lr: 0.00200
[2025-09-06 16:06:42,929 INFO misc.py line 117 1411560] Train: [14/100][291/1462] Data 0.001 (0.002) Batch 2.664 (2.875) Remain 101:20:28 loss: 0.3211 Lr: 0.00200
[2025-09-06 16:06:45,522 INFO misc.py line 117 1411560] Train: [14/100][292/1462] Data 0.002 (0.002) Batch 2.593 (2.874) Remain 101:18:22 loss: 0.3743 Lr: 0.00200
[2025-09-06 16:06:48,135 INFO misc.py line 117 1411560] Train: [14/100][293/1462] Data 0.002 (0.002) Batch 2.613 (2.873) Remain 101:16:25 loss: 0.6210 Lr: 0.00200
[2025-09-06 16:06:51,302 INFO misc.py line 117 1411560] Train: [14/100][294/1462] Data 0.002 (0.002) Batch 3.168 (2.874) Remain 101:18:30 loss: 0.3810 Lr: 0.00200
[2025-09-06 16:06:54,109 INFO misc.py line 117 1411560] Train: [14/100][295/1462] Data 0.002 (0.002) Batch 2.807 (2.874) Remain 101:17:58 loss: 0.4513 Lr: 0.00200
[2025-09-06 16:06:56,679 INFO misc.py line 117 1411560] Train: [14/100][296/1462] Data 0.001 (0.002) Batch 2.570 (2.873) Remain 101:15:44 loss: 0.2798 Lr: 0.00200
[2025-09-06 16:06:59,303 INFO misc.py line 117 1411560] Train: [14/100][297/1462] Data 0.002 (0.002) Batch 2.624 (2.872) Remain 101:13:53 loss: 0.6869 Lr: 0.00200
[2025-09-06 16:07:02,401 INFO misc.py line 117 1411560] Train: [14/100][298/1462] Data 0.002 (0.002) Batch 3.097 (2.873) Remain 101:15:28 loss: 0.6656 Lr: 0.00200
[2025-09-06 16:07:05,862 INFO misc.py line 117 1411560] Train: [14/100][299/1462] Data 0.001 (0.002) Batch 3.461 (2.875) Remain 101:19:37 loss: 0.3767 Lr: 0.00200
[2025-09-06 16:07:08,940 INFO misc.py line 117 1411560] Train: [14/100][300/1462] Data 0.001 (0.002) Batch 3.079 (2.875) Remain 101:21:01 loss: 0.3218 Lr: 0.00200
[2025-09-06 16:07:11,769 INFO misc.py line 117 1411560] Train: [14/100][301/1462] Data 0.002 (0.002) Batch 2.828 (2.875) Remain 101:20:38 loss: 0.2581 Lr: 0.00200
[2025-09-06 16:07:14,887 INFO misc.py line 117 1411560] Train: [14/100][302/1462] Data 0.002 (0.002) Batch 3.118 (2.876) Remain 101:22:19 loss: 0.2283 Lr: 0.00200
[2025-09-06 16:07:17,347 INFO misc.py line 117 1411560] Train: [14/100][303/1462] Data 0.002 (0.002) Batch 2.460 (2.875) Remain 101:19:20 loss: 0.4178 Lr: 0.00200
[2025-09-06 16:07:20,180 INFO misc.py line 117 1411560] Train: [14/100][304/1462] Data 0.002 (0.002) Batch 2.833 (2.874) Remain 101:18:59 loss: 0.4508 Lr: 0.00200
[2025-09-06 16:07:23,046 INFO misc.py line 117 1411560] Train: [14/100][305/1462] Data 0.002 (0.002) Batch 2.867 (2.874) Remain 101:18:53 loss: 0.3204 Lr: 0.00200
[2025-09-06 16:07:26,071 INFO misc.py line 117 1411560] Train: [14/100][306/1462] Data 0.002 (0.002) Batch 3.025 (2.875) Remain 101:19:53 loss: 0.2736 Lr: 0.00200
[2025-09-06 16:07:28,749 INFO misc.py line 117 1411560] Train: [14/100][307/1462] Data 0.001 (0.002) Batch 2.678 (2.874) Remain 101:18:28 loss: 0.5401 Lr: 0.00200
[2025-09-06 16:07:31,892 INFO misc.py line 117 1411560] Train: [14/100][308/1462] Data 0.002 (0.002) Batch 3.142 (2.875) Remain 101:20:17 loss: 0.2487 Lr: 0.00200
[2025-09-06 16:07:34,786 INFO misc.py line 117 1411560] Train: [14/100][309/1462] Data 0.001 (0.002) Batch 2.894 (2.875) Remain 101:20:22 loss: 0.5986 Lr: 0.00200
[2025-09-06 16:07:37,513 INFO misc.py line 117 1411560] Train: [14/100][310/1462] Data 0.001 (0.002) Batch 2.728 (2.875) Remain 101:19:18 loss: 0.3929 Lr: 0.00200
[2025-09-06 16:07:40,145 INFO misc.py line 117 1411560] Train: [14/100][311/1462] Data 0.001 (0.002) Batch 2.631 (2.874) Remain 101:17:35 loss: 0.3734 Lr: 0.00200
[2025-09-06 16:07:43,282 INFO misc.py line 117 1411560] Train: [14/100][312/1462] Data 0.002 (0.002) Batch 3.137 (2.875) Remain 101:19:20 loss: 0.6874 Lr: 0.00200
[2025-09-06 16:07:46,239 INFO misc.py line 117 1411560] Train: [14/100][313/1462] Data 0.002 (0.002) Batch 2.957 (2.875) Remain 101:19:51 loss: 0.6048 Lr: 0.00200
[2025-09-06 16:07:48,787 INFO misc.py line 117 1411560] Train: [14/100][314/1462] Data 0.002 (0.002) Batch 2.548 (2.874) Remain 101:17:34 loss: 0.3520 Lr: 0.00200
[2025-09-06 16:07:51,952 INFO misc.py line 117 1411560] Train: [14/100][315/1462] Data 0.002 (0.002) Batch 3.165 (2.875) Remain 101:19:30 loss: 0.6127 Lr: 0.00200
[2025-09-06 16:07:54,862 INFO misc.py line 117 1411560] Train: [14/100][316/1462] Data 0.001 (0.002) Batch 2.911 (2.875) Remain 101:19:41 loss: 0.3784 Lr: 0.00200
[2025-09-06 16:07:57,885 INFO misc.py line 117 1411560] Train: [14/100][317/1462] Data 0.002 (0.002) Batch 3.022 (2.876) Remain 101:20:38 loss: 0.2538 Lr: 0.00200
[2025-09-06 16:08:00,526 INFO misc.py line 117 1411560] Train: [14/100][318/1462] Data 0.002 (0.002) Batch 2.641 (2.875) Remain 101:19:01 loss: 0.5728 Lr: 0.00200
[2025-09-06 16:08:03,376 INFO misc.py line 117 1411560] Train: [14/100][319/1462] Data 0.002 (0.002) Batch 2.849 (2.875) Remain 101:18:48 loss: 0.5479 Lr: 0.00200
[2025-09-06 16:08:06,113 INFO misc.py line 117 1411560] Train: [14/100][320/1462] Data 0.003 (0.002) Batch 2.738 (2.874) Remain 101:17:50 loss: 0.5340 Lr: 0.00200
[2025-09-06 16:08:08,994 INFO misc.py line 117 1411560] Train: [14/100][321/1462] Data 0.002 (0.002) Batch 2.881 (2.874) Remain 101:17:50 loss: 0.3812 Lr: 0.00200
[2025-09-06 16:08:11,752 INFO misc.py line 117 1411560] Train: [14/100][322/1462] Data 0.002 (0.002) Batch 2.758 (2.874) Remain 101:17:01 loss: 0.4533 Lr: 0.00200
[2025-09-06 16:08:14,876 INFO misc.py line 117 1411560] Train: [14/100][323/1462] Data 0.002 (0.002) Batch 3.124 (2.875) Remain 101:18:37 loss: 0.5004 Lr: 0.00200
[2025-09-06 16:08:17,507 INFO misc.py line 117 1411560] Train: [14/100][324/1462] Data 0.002 (0.002) Batch 2.632 (2.874) Remain 101:16:58 loss: 0.4183 Lr: 0.00200
[2025-09-06 16:08:20,144 INFO misc.py line 117 1411560] Train: [14/100][325/1462] Data 0.002 (0.002) Batch 2.636 (2.873) Remain 101:15:21 loss: 0.3856 Lr: 0.00200
[2025-09-06 16:08:23,029 INFO misc.py line 117 1411560] Train: [14/100][326/1462] Data 0.002 (0.002) Batch 2.886 (2.873) Remain 101:15:24 loss: 0.2315 Lr: 0.00200
[2025-09-06 16:08:26,317 INFO misc.py line 117 1411560] Train: [14/100][327/1462] Data 0.001 (0.002) Batch 3.287 (2.875) Remain 101:18:03 loss: 0.2716 Lr: 0.00200
[2025-09-06 16:08:29,246 INFO misc.py line 117 1411560] Train: [14/100][328/1462] Data 0.001 (0.002) Batch 2.930 (2.875) Remain 101:18:21 loss: 0.5838 Lr: 0.00200
[2025-09-06 16:08:31,915 INFO misc.py line 117 1411560] Train: [14/100][329/1462] Data 0.002 (0.002) Batch 2.669 (2.874) Remain 101:16:58 loss: 0.3196 Lr: 0.00200
[2025-09-06 16:08:34,586 INFO misc.py line 117 1411560] Train: [14/100][330/1462] Data 0.002 (0.002) Batch 2.671 (2.873) Remain 101:15:37 loss: 0.3116 Lr: 0.00200
[2025-09-06 16:08:37,502 INFO misc.py line 117 1411560] Train: [14/100][331/1462] Data 0.002 (0.002) Batch 2.915 (2.874) Remain 101:15:50 loss: 0.5910 Lr: 0.00200
[2025-09-06 16:08:40,518 INFO misc.py line 117 1411560] Train: [14/100][332/1462] Data 0.002 (0.002) Batch 3.017 (2.874) Remain 101:16:42 loss: 0.4493 Lr: 0.00200
[2025-09-06 16:08:43,480 INFO misc.py line 117 1411560] Train: [14/100][333/1462] Data 0.002 (0.002) Batch 2.962 (2.874) Remain 101:17:13 loss: 0.3098 Lr: 0.00200
[2025-09-06 16:08:46,117 INFO misc.py line 117 1411560] Train: [14/100][334/1462] Data 0.002 (0.002) Batch 2.637 (2.874) Remain 101:15:40 loss: 0.2237 Lr: 0.00200
[2025-09-06 16:08:48,952 INFO misc.py line 117 1411560] Train: [14/100][335/1462] Data 0.001 (0.002) Batch 2.835 (2.873) Remain 101:15:22 loss: 0.4566 Lr: 0.00200
[2025-09-06 16:08:52,210 INFO misc.py line 117 1411560] Train: [14/100][336/1462] Data 0.001 (0.002) Batch 3.258 (2.875) Remain 101:17:46 loss: 0.2915 Lr: 0.00200
[2025-09-06 16:08:55,169 INFO misc.py line 117 1411560] Train: [14/100][337/1462] Data 0.001 (0.002) Batch 2.958 (2.875) Remain 101:18:15 loss: 0.3554 Lr: 0.00200
[2025-09-06 16:08:57,930 INFO misc.py line 117 1411560] Train: [14/100][338/1462] Data 0.001 (0.002) Batch 2.761 (2.875) Remain 101:17:29 loss: 0.4264 Lr: 0.00200
[2025-09-06 16:09:01,010 INFO misc.py line 117 1411560] Train: [14/100][339/1462] Data 0.001 (0.002) Batch 3.080 (2.875) Remain 101:18:44 loss: 0.3765 Lr: 0.00200
[2025-09-06 16:09:03,672 INFO misc.py line 117 1411560] Train: [14/100][340/1462] Data 0.002 (0.002) Batch 2.662 (2.874) Remain 101:17:20 loss: 0.6125 Lr: 0.00200
[2025-09-06 16:09:06,449 INFO misc.py line 117 1411560] Train: [14/100][341/1462] Data 0.002 (0.002) Batch 2.776 (2.874) Remain 101:16:41 loss: 0.6468 Lr: 0.00200
[2025-09-06 16:09:09,240 INFO misc.py line 117 1411560] Train: [14/100][342/1462] Data 0.002 (0.002) Batch 2.791 (2.874) Remain 101:16:07 loss: 0.4902 Lr: 0.00200
[2025-09-06 16:09:11,875 INFO misc.py line 117 1411560] Train: [14/100][343/1462] Data 0.002 (0.002) Batch 2.636 (2.873) Remain 101:14:35 loss: 0.2830 Lr: 0.00200
[2025-09-06 16:09:14,617 INFO misc.py line 117 1411560] Train: [14/100][344/1462] Data 0.002 (0.002) Batch 2.742 (2.873) Remain 101:13:43 loss: 0.2396 Lr: 0.00200
[2025-09-06 16:09:17,433 INFO misc.py line 117 1411560] Train: [14/100][345/1462] Data 0.002 (0.002) Batch 2.816 (2.873) Remain 101:13:19 loss: 0.4373 Lr: 0.00200
[2025-09-06 16:09:20,546 INFO misc.py line 117 1411560] Train: [14/100][346/1462] Data 0.002 (0.002) Batch 3.113 (2.873) Remain 101:14:45 loss: 0.2846 Lr: 0.00200
[2025-09-06 16:09:23,489 INFO misc.py line 117 1411560] Train: [14/100][347/1462] Data 0.002 (0.002) Batch 2.943 (2.874) Remain 101:15:08 loss: 0.3278 Lr: 0.00200
[2025-09-06 16:09:26,291 INFO misc.py line 117 1411560] Train: [14/100][348/1462] Data 0.002 (0.002) Batch 2.803 (2.873) Remain 101:14:39 loss: 0.3792 Lr: 0.00200
[2025-09-06 16:09:28,997 INFO misc.py line 117 1411560] Train: [14/100][349/1462] Data 0.002 (0.002) Batch 2.705 (2.873) Remain 101:13:34 loss: 0.3708 Lr: 0.00200
[2025-09-06 16:09:31,905 INFO misc.py line 117 1411560] Train: [14/100][350/1462] Data 0.002 (0.002) Batch 2.909 (2.873) Remain 101:13:44 loss: 0.5083 Lr: 0.00200
[2025-09-06 16:09:34,729 INFO misc.py line 117 1411560] Train: [14/100][351/1462] Data 0.002 (0.002) Batch 2.823 (2.873) Remain 101:13:23 loss: 0.2993 Lr: 0.00200
[2025-09-06 16:09:37,321 INFO misc.py line 117 1411560] Train: [14/100][352/1462] Data 0.002 (0.002) Batch 2.593 (2.872) Remain 101:11:39 loss: 0.4746 Lr: 0.00200
[2025-09-06 16:09:40,422 INFO misc.py line 117 1411560] Train: [14/100][353/1462] Data 0.001 (0.002) Batch 3.101 (2.873) Remain 101:12:59 loss: 0.5219 Lr: 0.00200
[2025-09-06 16:09:43,213 INFO misc.py line 117 1411560] Train: [14/100][354/1462] Data 0.001 (0.002) Batch 2.790 (2.872) Remain 101:12:26 loss: 0.3766 Lr: 0.00200
[2025-09-06 16:09:46,139 INFO misc.py line 117 1411560] Train: [14/100][355/1462] Data 0.002 (0.002) Batch 2.926 (2.873) Remain 101:12:43 loss: 0.4137 Lr: 0.00200
[2025-09-06 16:09:49,240 INFO misc.py line 117 1411560] Train: [14/100][356/1462] Data 0.001 (0.002) Batch 3.101 (2.873) Remain 101:14:02 loss: 0.2566 Lr: 0.00200
[2025-09-06 16:09:52,099 INFO misc.py line 117 1411560] Train: [14/100][357/1462] Data 0.001 (0.002) Batch 2.859 (2.873) Remain 101:13:54 loss: 0.2745 Lr: 0.00200
[2025-09-06 16:09:55,123 INFO misc.py line 117 1411560] Train: [14/100][358/1462] Data 0.001 (0.002) Batch 3.025 (2.874) Remain 101:14:45 loss: 0.4444 Lr: 0.00200
[2025-09-06 16:09:58,058 INFO misc.py line 117 1411560] Train: [14/100][359/1462] Data 0.002 (0.002) Batch 2.935 (2.874) Remain 101:15:04 loss: 0.4037 Lr: 0.00200
[2025-09-06 16:10:01,000 INFO misc.py line 117 1411560] Train: [14/100][360/1462] Data 0.001 (0.002) Batch 2.942 (2.874) Remain 101:15:25 loss: 0.4870 Lr: 0.00200
[2025-09-06 16:10:03,812 INFO misc.py line 117 1411560] Train: [14/100][361/1462] Data 0.002 (0.002) Batch 2.812 (2.874) Remain 101:15:00 loss: 0.2630 Lr: 0.00200
[2025-09-06 16:10:06,405 INFO misc.py line 117 1411560] Train: [14/100][362/1462] Data 0.002 (0.002) Batch 2.593 (2.873) Remain 101:13:18 loss: 0.8480 Lr: 0.00200
[2025-09-06 16:10:09,474 INFO misc.py line 117 1411560] Train: [14/100][363/1462] Data 0.001 (0.002) Batch 3.069 (2.874) Remain 101:14:24 loss: 0.4464 Lr: 0.00200
[2025-09-06 16:10:12,158 INFO misc.py line 117 1411560] Train: [14/100][364/1462] Data 0.002 (0.002) Batch 2.685 (2.873) Remain 101:13:15 loss: 0.7077 Lr: 0.00200
[2025-09-06 16:10:14,920 INFO misc.py line 117 1411560] Train: [14/100][365/1462] Data 0.001 (0.002) Batch 2.762 (2.873) Remain 101:12:33 loss: 0.3314 Lr: 0.00200
[2025-09-06 16:10:17,829 INFO misc.py line 117 1411560] Train: [14/100][366/1462] Data 0.001 (0.002) Batch 2.909 (2.873) Remain 101:12:43 loss: 0.6432 Lr: 0.00200
[2025-09-06 16:10:20,592 INFO misc.py line 117 1411560] Train: [14/100][367/1462] Data 0.002 (0.002) Batch 2.763 (2.873) Remain 101:12:02 loss: 0.4662 Lr: 0.00200
[2025-09-06 16:10:23,592 INFO misc.py line 117 1411560] Train: [14/100][368/1462] Data 0.002 (0.002) Batch 3.000 (2.873) Remain 101:12:43 loss: 0.4788 Lr: 0.00200
[2025-09-06 16:10:26,676 INFO misc.py line 117 1411560] Train: [14/100][369/1462] Data 0.001 (0.002) Batch 3.084 (2.874) Remain 101:13:54 loss: 0.3738 Lr: 0.00200
[2025-09-06 16:10:29,577 INFO misc.py line 117 1411560] Train: [14/100][370/1462] Data 0.002 (0.002) Batch 2.901 (2.874) Remain 101:14:00 loss: 0.7420 Lr: 0.00200
[2025-09-06 16:10:32,689 INFO misc.py line 117 1411560] Train: [14/100][371/1462] Data 0.002 (0.002) Batch 3.111 (2.874) Remain 101:15:19 loss: 0.4302 Lr: 0.00200
[2025-09-06 16:10:35,830 INFO misc.py line 117 1411560] Train: [14/100][372/1462] Data 0.002 (0.002) Batch 3.142 (2.875) Remain 101:16:48 loss: 0.4716 Lr: 0.00200
[2025-09-06 16:10:39,011 INFO misc.py line 117 1411560] Train: [14/100][373/1462] Data 0.002 (0.002) Batch 3.180 (2.876) Remain 101:18:30 loss: 0.5275 Lr: 0.00200
[2025-09-06 16:10:41,902 INFO misc.py line 117 1411560] Train: [14/100][374/1462] Data 0.002 (0.002) Batch 2.891 (2.876) Remain 101:18:32 loss: 0.3930 Lr: 0.00200
[2025-09-06 16:10:44,681 INFO misc.py line 117 1411560] Train: [14/100][375/1462] Data 0.002 (0.002) Batch 2.779 (2.876) Remain 101:17:56 loss: 0.4010 Lr: 0.00200
[2025-09-06 16:10:47,805 INFO misc.py line 117 1411560] Train: [14/100][376/1462] Data 0.002 (0.002) Batch 3.125 (2.876) Remain 101:19:18 loss: 0.5357 Lr: 0.00200
[2025-09-06 16:10:50,686 INFO misc.py line 117 1411560] Train: [14/100][377/1462] Data 0.004 (0.002) Batch 2.881 (2.876) Remain 101:19:17 loss: 0.5460 Lr: 0.00200
[2025-09-06 16:10:53,682 INFO misc.py line 117 1411560] Train: [14/100][378/1462] Data 0.001 (0.002) Batch 2.996 (2.877) Remain 101:19:55 loss: 0.2927 Lr: 0.00200
[2025-09-06 16:10:56,659 INFO misc.py line 117 1411560] Train: [14/100][379/1462] Data 0.001 (0.002) Batch 2.977 (2.877) Remain 101:20:26 loss: 0.6794 Lr: 0.00200
[2025-09-06 16:10:59,305 INFO misc.py line 117 1411560] Train: [14/100][380/1462] Data 0.001 (0.002) Batch 2.646 (2.876) Remain 101:19:05 loss: 0.5420 Lr: 0.00200
[2025-09-06 16:11:02,044 INFO misc.py line 117 1411560] Train: [14/100][381/1462] Data 0.002 (0.002) Batch 2.740 (2.876) Remain 101:18:16 loss: 0.6363 Lr: 0.00200
[2025-09-06 16:11:04,750 INFO misc.py line 117 1411560] Train: [14/100][382/1462] Data 0.001 (0.002) Batch 2.706 (2.875) Remain 101:17:16 loss: 0.4110 Lr: 0.00200
[2025-09-06 16:11:07,508 INFO misc.py line 117 1411560] Train: [14/100][383/1462] Data 0.001 (0.002) Batch 2.758 (2.875) Remain 101:16:34 loss: 0.5294 Lr: 0.00200
[2025-09-06 16:11:10,755 INFO misc.py line 117 1411560] Train: [14/100][384/1462] Data 0.001 (0.002) Batch 3.248 (2.876) Remain 101:18:35 loss: 0.3995 Lr: 0.00200
[2025-09-06 16:11:13,172 INFO misc.py line 117 1411560] Train: [14/100][385/1462] Data 0.001 (0.002) Batch 2.417 (2.875) Remain 101:16:00 loss: 0.6777 Lr: 0.00200
[2025-09-06 16:11:16,294 INFO misc.py line 117 1411560] Train: [14/100][386/1462] Data 0.001 (0.002) Batch 3.123 (2.876) Remain 101:17:19 loss: 0.6933 Lr: 0.00200
[2025-09-06 16:11:19,338 INFO misc.py line 117 1411560] Train: [14/100][387/1462] Data 0.002 (0.002) Batch 3.043 (2.876) Remain 101:18:12 loss: 0.2944 Lr: 0.00200
[2025-09-06 16:11:22,161 INFO misc.py line 117 1411560] Train: [14/100][388/1462] Data 0.001 (0.002) Batch 2.823 (2.876) Remain 101:17:51 loss: 0.6706 Lr: 0.00200
[2025-09-06 16:11:24,974 INFO misc.py line 117 1411560] Train: [14/100][389/1462] Data 0.001 (0.002) Batch 2.813 (2.876) Remain 101:17:28 loss: 0.7936 Lr: 0.00200
[2025-09-06 16:11:28,322 INFO misc.py line 117 1411560] Train: [14/100][390/1462] Data 0.001 (0.002) Batch 3.348 (2.877) Remain 101:20:00 loss: 0.3370 Lr: 0.00200
[2025-09-06 16:11:30,886 INFO misc.py line 117 1411560] Train: [14/100][391/1462] Data 0.001 (0.002) Batch 2.563 (2.876) Remain 101:18:15 loss: 0.6113 Lr: 0.00200
[2025-09-06 16:11:33,803 INFO misc.py line 117 1411560] Train: [14/100][392/1462] Data 0.001 (0.002) Batch 2.917 (2.876) Remain 101:18:25 loss: 0.3943 Lr: 0.00200
[2025-09-06 16:11:36,521 INFO misc.py line 117 1411560] Train: [14/100][393/1462] Data 0.002 (0.002) Batch 2.718 (2.876) Remain 101:17:31 loss: 0.6719 Lr: 0.00200
[2025-09-06 16:11:39,469 INFO misc.py line 117 1411560] Train: [14/100][394/1462] Data 0.001 (0.002) Batch 2.948 (2.876) Remain 101:17:51 loss: 0.4467 Lr: 0.00200
[2025-09-06 16:11:42,592 INFO misc.py line 117 1411560] Train: [14/100][395/1462] Data 0.001 (0.002) Batch 3.123 (2.877) Remain 101:19:08 loss: 0.4171 Lr: 0.00200
[2025-09-06 16:11:45,379 INFO misc.py line 117 1411560] Train: [14/100][396/1462] Data 0.002 (0.002) Batch 2.787 (2.876) Remain 101:18:36 loss: 0.5998 Lr: 0.00200
[2025-09-06 16:11:48,466 INFO misc.py line 117 1411560] Train: [14/100][397/1462] Data 0.002 (0.002) Batch 3.087 (2.877) Remain 101:19:41 loss: 0.7498 Lr: 0.00200
[2025-09-06 16:11:51,131 INFO misc.py line 117 1411560] Train: [14/100][398/1462] Data 0.002 (0.002) Batch 2.665 (2.876) Remain 101:18:31 loss: 0.3161 Lr: 0.00200
[2025-09-06 16:11:54,227 INFO misc.py line 117 1411560] Train: [14/100][399/1462] Data 0.001 (0.002) Batch 3.095 (2.877) Remain 101:19:38 loss: 0.4294 Lr: 0.00200
[2025-09-06 16:11:57,381 INFO misc.py line 117 1411560] Train: [14/100][400/1462] Data 0.001 (0.002) Batch 3.154 (2.878) Remain 101:21:04 loss: 0.3080 Lr: 0.00200
[2025-09-06 16:12:00,403 INFO misc.py line 117 1411560] Train: [14/100][401/1462] Data 0.001 (0.002) Batch 3.023 (2.878) Remain 101:21:47 loss: 0.2933 Lr: 0.00200
[2025-09-06 16:12:03,340 INFO misc.py line 117 1411560] Train: [14/100][402/1462] Data 0.002 (0.002) Batch 2.937 (2.878) Remain 101:22:03 loss: 0.4461 Lr: 0.00200
[2025-09-06 16:12:06,006 INFO misc.py line 117 1411560] Train: [14/100][403/1462] Data 0.001 (0.002) Batch 2.666 (2.878) Remain 101:20:53 loss: 0.7139 Lr: 0.00200
[2025-09-06 16:12:08,698 INFO misc.py line 117 1411560] Train: [14/100][404/1462] Data 0.001 (0.002) Batch 2.691 (2.877) Remain 101:19:51 loss: 0.3468 Lr: 0.00200
[2025-09-06 16:12:11,789 INFO misc.py line 117 1411560] Train: [14/100][405/1462] Data 0.001 (0.002) Batch 3.091 (2.878) Remain 101:20:55 loss: 0.2728 Lr: 0.00200
[2025-09-06 16:12:14,703 INFO misc.py line 117 1411560] Train: [14/100][406/1462] Data 0.002 (0.002) Batch 2.914 (2.878) Remain 101:21:04 loss: 0.7782 Lr: 0.00200
[2025-09-06 16:12:17,479 INFO misc.py line 117 1411560] Train: [14/100][407/1462] Data 0.001 (0.002) Batch 2.776 (2.878) Remain 101:20:29 loss: 0.3106 Lr: 0.00200
[2025-09-06 16:12:20,280 INFO misc.py line 117 1411560] Train: [14/100][408/1462] Data 0.001 (0.002) Batch 2.802 (2.877) Remain 101:20:02 loss: 0.2758 Lr: 0.00200
[2025-09-06 16:12:23,299 INFO misc.py line 117 1411560] Train: [14/100][409/1462] Data 0.002 (0.002) Batch 3.019 (2.878) Remain 101:20:44 loss: 0.2953 Lr: 0.00200
[2025-09-06 16:12:26,395 INFO misc.py line 117 1411560] Train: [14/100][410/1462] Data 0.001 (0.002) Batch 3.096 (2.878) Remain 101:21:49 loss: 0.5206 Lr: 0.00200
[2025-09-06 16:12:29,272 INFO misc.py line 117 1411560] Train: [14/100][411/1462] Data 0.001 (0.002) Batch 2.877 (2.878) Remain 101:21:46 loss: 0.3715 Lr: 0.00200
[2025-09-06 16:12:32,094 INFO misc.py line 117 1411560] Train: [14/100][412/1462] Data 0.002 (0.002) Batch 2.823 (2.878) Remain 101:21:25 loss: 0.4867 Lr: 0.00200
[2025-09-06 16:12:35,142 INFO misc.py line 117 1411560] Train: [14/100][413/1462] Data 0.001 (0.002) Batch 3.048 (2.878) Remain 101:22:15 loss: 0.3510 Lr: 0.00200
[2025-09-06 16:12:38,087 INFO misc.py line 117 1411560] Train: [14/100][414/1462] Data 0.002 (0.002) Batch 2.946 (2.879) Remain 101:22:33 loss: 0.6213 Lr: 0.00200
[2025-09-06 16:12:40,980 INFO misc.py line 117 1411560] Train: [14/100][415/1462] Data 0.001 (0.002) Batch 2.892 (2.879) Remain 101:22:34 loss: 0.5011 Lr: 0.00200
[2025-09-06 16:12:43,939 INFO misc.py line 117 1411560] Train: [14/100][416/1462] Data 0.002 (0.002) Batch 2.960 (2.879) Remain 101:22:56 loss: 0.4413 Lr: 0.00200
[2025-09-06 16:12:47,030 INFO misc.py line 117 1411560] Train: [14/100][417/1462] Data 0.002 (0.002) Batch 3.091 (2.879) Remain 101:23:58 loss: 0.4845 Lr: 0.00200
[2025-09-06 16:12:49,955 INFO misc.py line 117 1411560] Train: [14/100][418/1462] Data 0.002 (0.002) Batch 2.925 (2.879) Remain 101:24:09 loss: 0.4239 Lr: 0.00200
[2025-09-06 16:12:52,951 INFO misc.py line 117 1411560] Train: [14/100][419/1462] Data 0.002 (0.002) Batch 2.996 (2.880) Remain 101:24:42 loss: 0.2650 Lr: 0.00200
[2025-09-06 16:12:55,930 INFO misc.py line 117 1411560] Train: [14/100][420/1462] Data 0.001 (0.002) Batch 2.979 (2.880) Remain 101:25:09 loss: 0.3853 Lr: 0.00200
[2025-09-06 16:12:58,561 INFO misc.py line 117 1411560] Train: [14/100][421/1462] Data 0.001 (0.002) Batch 2.631 (2.879) Remain 101:23:51 loss: 0.5789 Lr: 0.00200
[2025-09-06 16:13:01,559 INFO misc.py line 117 1411560] Train: [14/100][422/1462] Data 0.001 (0.002) Batch 2.998 (2.880) Remain 101:24:24 loss: 0.4095 Lr: 0.00200
[2025-09-06 16:13:04,439 INFO misc.py line 117 1411560] Train: [14/100][423/1462] Data 0.002 (0.002) Batch 2.879 (2.880) Remain 101:24:21 loss: 0.3960 Lr: 0.00200
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [0,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [1,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [2,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [3,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [4,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [5,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [6,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [7,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [8,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [9,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [10,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [11,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [12,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [13,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [14,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [15,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [16,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [17,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [18,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [19,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [20,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [21,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [22,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [23,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [24,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [25,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [26,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [27,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [28,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [29,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [30,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
/opt/conda/conda-bld/pytorch_1728929546833/work/aten/src/ATen/native/cuda/ScatterGatherKernel.cu:144: operator(): block: [324,0,0], thread: [31,0,0] Assertion `idx_dim >= 0 && idx_dim < index_size && "index out of bounds"` failed.
[2025-09-06 16:13:06,247 ERROR events.py line 611 1411561] Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 175, in train
    self.run_step()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 202, in run_step
    self.scaler.scale(loss).backward()
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_tensor.py", line 581, in backward
    torch.autograd.backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/__init__.py", line 347, in backward
    _engine_run_backward(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/graph.py", line 825, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


[rank1]:[W906 16:13:06.737123461 CUDAGuardImpl.h:119] Warning: CUDA warning: device-side assert triggered (function destroyEvent)
terminate called after throwing an instance of 'c10::Error'
  what():  CUDA error: device-side assert triggered
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

Exception raised from c10_cuda_check_implementation at /opt/conda/conda-bld/pytorch_1728929546833/work/c10/cuda/CUDAException.cpp:43 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::string) + 0x96 (0x7f1ddbe7a446 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: c10::detail::torchCheckFail(char const*, char const*, unsigned int, std::string const&) + 0x64 (0x7f1ddbe246e4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #2: c10::cuda::c10_cuda_check_implementation(int, char const*, char const*, int, bool) + 0x118 (0x7f1ddbf67a18 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #3: <unknown function> + 0x1f92e (0x7f1ddbf2e92e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #4: <unknown function> + 0x20a57 (0x7f1ddbf2fa57 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #5: <unknown function> + 0x20c5f (0x7f1ddbf2fc5f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10_cuda.so)
frame #6: <unknown function> + 0x5faf70 (0x7f1e2d0c6f70 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #7: <unknown function> + 0x6f69f (0x7f1ddbe5b69f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #8: c10::TensorImpl::~TensorImpl() + 0x21b (0x7f1ddbe5437b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #9: c10::TensorImpl::~TensorImpl() + 0x9 (0x7f1ddbe54529 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #10: c10d::Reducer::~Reducer() + 0x5c4 (0x7f1e24d863b4 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #11: std::_Sp_counted_ptr<c10d::Reducer*, (__gnu_cxx::_Lock_policy)2>::_M_dispose() + 0x12 (0x7f1e2d86ebf2 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #12: std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release() + 0x48 (0x7f1e2cf8a078 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #13: <unknown function> + 0xdadc21 (0x7f1e2d879c21 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #14: <unknown function> + 0x4c90f3 (0x7f1e2cf950f3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #15: <unknown function> + 0x4c9c71 (0x7f1e2cf95c71 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #16: <unknown function> + 0x128b86 (0x55c83fae8b86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #17: <unknown function> + 0x14b2a0 (0x55c83fb0b2a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #18: <unknown function> + 0x128b86 (0x55c83fae8b86 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #19: <unknown function> + 0x14b2a0 (0x55c83fb0b2a0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #20: <unknown function> + 0x134be7 (0x55c83faf4be7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #21: <unknown function> + 0x134ccb (0x55c83faf4ccb in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #22: <unknown function> + 0x144c3b (0x55c83fb04c3b in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #23: <unknown function> + 0x149f1c (0x55c83fb09f1c in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #24: <unknown function> + 0x121412 (0x55c83fae1412 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #25: <unknown function> + 0x203bb1 (0x55c83fbc3bb1 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #26: _PyEval_EvalFrameDefault + 0x46cf (0x55c83faf077f in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #27: _PyFunction_Vectorcall + 0x6c (0x55c83fafc0ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #28: _PyEval_EvalFrameDefault + 0x700 (0x55c83faec7b0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #29: _PyFunction_Vectorcall + 0x6c (0x55c83fafc0ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #30: _PyEval_EvalFrameDefault + 0x30c (0x55c83faec3bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #31: _PyFunction_Vectorcall + 0x6c (0x55c83fafc0ec in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #32: _PyEval_EvalFrameDefault + 0x1340 (0x55c83faed3f0 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #33: <unknown function> + 0x1cc3bc (0x55c83fb8c3bc in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #34: PyEval_EvalCode + 0x87 (0x55c83fb8c307 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #35: <unknown function> + 0x1fc96a (0x55c83fbbc96a in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #36: <unknown function> + 0x1f7df3 (0x55c83fbb7df3 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #37: PyRun_StringFlags + 0x7d (0x55c83fbb04ad in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #38: PyRun_SimpleStringFlags + 0x3c (0x55c83fbb03ac in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #39: Py_RunMain + 0x3ba (0x55c83fbaf5ba in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #40: Py_BytesMain + 0x37 (0x55c83fb7fef7 in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)
frame #41: <unknown function> + 0x29d90 (0x7f1e36275d90 in /lib/x86_64-linux-gnu/libc.so.6)
frame #42: __libc_start_main + 0x80 (0x7f1e36275e40 in /lib/x86_64-linux-gnu/libc.so.6)
frame #43: <unknown function> + 0x1bfe0e (0x55c83fb7fe0e in /home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/bin/python)

W0906 16:13:06.955000 1411428 site-packages/torch/multiprocessing/spawn.py:160] Terminating process 1411560 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 38, in <module>
    main()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 27, in main
    launch(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 74, in launch
    mp.spawn(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 328, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 284, in start_processes
    while not context.join():
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 184, in join
    raise ProcessExitedException(
torch.multiprocessing.spawn.ProcessExitedException: process 1 terminated with signal SIGABRT
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 35 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
