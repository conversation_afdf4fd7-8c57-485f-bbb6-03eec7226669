Starting training at 2025年 09月 06日 星期六 00:23:13 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-06 00:23:36,316 INFO train.py line 136 1319792] => Loading config ...
[2025-09-06 00:23:36,316 INFO train.py line 138 1319792] Save path: exp/powscan/splitSections
[2025-09-06 00:23:36,478 INFO train.py line 139 1319792] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 10
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 5
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-06 00:23:36,479 INFO train.py line 140 1319792] => Building model ...
[2025-09-06 00:23:36,530 INFO train.py line 241 1319792] Num params: 3908496
[2025-09-06 00:23:39,417 INFO train.py line 142 1319792] => Building writer ...
[2025-09-06 00:23:39,419 INFO train.py line 251 1319792] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-06 00:23:39,419 INFO train.py line 144 1319792] => Building train dataset & dataloader ...
[2025-09-06 00:23:58,210 INFO defaults.py line 70 1319792] Totally 585 x 30 samples in powscan train set.
[2025-09-06 00:23:58,211 INFO train.py line 146 1319792] => Building val dataset & dataloader ...
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
[2025-09-06 00:23:59,739 INFO defaults.py line 70 1319792] Totally 80 x 1 samples in powscan val set.
[2025-09-06 00:23:59,740 INFO train.py line 148 1319792] => Building optimize, scheduler, scaler(amp) ...
[2025-09-06 00:23:59,891 INFO train.py line 152 1319792] => Building hooks ...
[2025-09-06 00:23:59,892 INFO misc.py line 237 1319792] => Loading checkpoint & weight ...
[2025-09-06 00:23:59,892 INFO misc.py line 239 1319792] Loading weight at: exp/powscan/splitSections/model/model_last.pth
[2025-09-06 00:24:04,832 INFO misc.py line 245 1319792] Loading layer weights with keyword: , replace keyword with: 
[2025-09-06 00:24:06,709 INFO misc.py line 262 1319792] Missing keys: []
[2025-09-06 00:24:06,709 INFO misc.py line 264 1319792] Resuming train at eval epoch: 4
[2025-09-06 00:24:06,715 INFO train.py line 159 1319792] >>>>>>>>>>>>>>>> Start Training >>>>>>>>>>>>>>>>
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 4, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 5, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [4, 5, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 5, 2].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [4, 4, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [2, 3, 2].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 3, 1].
%%%%%Warning: split data set into [5, 3, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [1, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [3, 1, 1].
%%%%%Warning: split data set into [3, 2, 1].
%%%%%Warning: split data set into [2, 2, 1].
%%%%%Warning: split data set into [3, 3, 1].
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py:632: UserWarning: torch.utils.checkpoint: the use_reentrant parameter should be passed explicitly. In version 2.5 we will raise an exception if use_reentrant is not passed. use_reentrant=False is recommended, but if you need to preserve the current default behavior, you can pass use_reentrant=True. Refer to docs for more details on the differences between the two variants.
  return fn(*args, **kwargs)
[2025-09-06 00:24:20,312 ERROR events.py line 611 1319792] Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 175, in train
    self.run_step()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 198, in run_step
    output_dict = self.model(input_dict)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1643, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1459, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/models/default.py", line 24, in forward
    seg_logits = self.backbone(input_dict)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/models/point_transformer_v2/point_transformer_v2m2_base.py", line 563, in forward
    points = self.patch_embed(points)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/models/point_transformer_v2/point_transformer_v2m2_base.py", line 444, in forward
    return self.blocks([coord, feat, offset])
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/models/point_transformer_v2/point_transformer_v2m2_base.py", line 225, in forward
    points = block(points, reference_index)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/models/point_transformer_v2/point_transformer_v2m2_base.py", line 171, in forward
    else checkpoint(self.attn, feat, coord, reference_index)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_compile.py", line 32, in inner
    return disable_fn(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/_dynamo/eval_frame.py", line 632, in _fn
    return fn(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/checkpoint.py", line 489, in checkpoint
    return CheckpointFunction.apply(function, preserve, *args)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/autograd/function.py", line 575, in apply
    return super().apply(*args, **kwargs)  # type: ignore[misc]
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/utils/checkpoint.py", line 264, in forward
    outputs = run_function(*args)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/models/point_transformer_v2/point_transformer_v2m2_base.py", line 127, in forward
    feat = torch.einsum("n s g i, n s g -> n g i", value, weight)
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/functional.py", line 402, in einsum
    return _VF.einsum(equation, operands)  # type: ignore[attr-defined]
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 440.00 MiB. GPU 0 has a total capacity of 23.55 GiB of which 33.88 MiB is free. Process 1296420 has 18.76 GiB memory in use. Including non-PyTorch memory, this process has 4.73 GiB memory in use. Of the allocated memory 3.81 GiB is allocated by PyTorch, and 397.73 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

[rank0]:[W906 00:24:20.152324516 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())
W0906 00:24:22.194000 1319655 site-packages/torch/multiprocessing/spawn.py:160] Terminating process 1319793 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 38, in <module>
    main()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 27, in main
    launch(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 74, in launch
    mp.spawn(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 328, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 284, in start_processes
    while not context.join():
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 192, in join
    raise ProcessExitedException(
torch.multiprocessing.spawn.ProcessExitedException: process 0 terminated with exit code 1
/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 32 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
