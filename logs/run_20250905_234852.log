Starting training at 2025年 09月 05日 星期五 23:48:52 CST
Arguments: -g 2 -d powscan -c semseg-pt-v2m2-2-lovasz -n splitSections -r true
Experiment name: splitSections
Python interpreter dir: python
Dataset: powscan
Config: semseg-pt-v2m2-2-lovasz
GPU Num: 2
Machine Num: 1
Dist URL: auto
 =========> CREATE EXP DIR <=========
Experiment dir: /home/<USER>/Workspace/Pointcept/exp/powscan/splitSections
Loading config in: exp/powscan/splitSections/config.py
Running code in: exp/powscan/splitSections/code
 =========> RUN TASK <=========
[2025-09-05 23:49:14,572 INFO train.py line 136 1295800] => Loading config ...
[2025-09-05 23:49:14,572 INFO train.py line 138 1295800] Save path: exp/powscan/splitSections
[2025-09-05 23:49:14,713 INFO train.py line 139 1295800] Config:
weight = 'exp/powscan/splitSections/model/model_last.pth'
resume = True
evaluate = True
test_only = False
seed = 59833740
save_path = 'exp/powscan/splitSections'
num_worker = 16
batch_size = 12
batch_size_val = None
batch_size_test = None
epoch = 3000
eval_epoch = 100
clip_grad = None
sync_bn = False
enable_amp = True
amp_dtype = 'float16'
empty_cache = False
empty_cache_per_epoch = False
find_unused_parameters = False
enable_wandb = False
wandb_project = 'pointcept'
wandb_key = None
mix_prob = 0.8
param_dicts = None
hooks = [
    dict(type='CheckpointLoader'),
    dict(type='ModelHook'),
    dict(type='IterationTimer', warmup_iter=2),
    dict(type='InformationWriter'),
    dict(type='SemSegEvaluator'),
    dict(type='CheckpointSaver', save_freq=None),
    dict(type='PreciseEvaluator', test_last=False)
]
train = dict(type='DefaultTrainer')
test = dict(type='SemSegTester', verbose=True)
model = dict(
    type='DefaultSegmentor',
    backbone=dict(
        type='PT-v2m2',
        in_channels=4,
        num_classes=12,
        patch_embed_depth=2,
        patch_embed_channels=48,
        patch_embed_groups=6,
        patch_embed_neighbours=16,
        enc_depths=(2, 6, 2),
        enc_channels=(96, 192, 384),
        enc_groups=(12, 24, 48),
        enc_neighbours=(16, 16, 16),
        dec_depths=(1, 1, 1),
        dec_channels=(48, 96, 192),
        dec_groups=(6, 12, 24),
        dec_neighbours=(16, 16, 16),
        grid_sizes=(0.1, 0.2, 0.4),
        attn_qkv_bias=True,
        pe_multiplier=False,
        pe_bias=True,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        enable_checkpoint=True,
        unpool_backend='interp'),
    criteria=[
        dict(
            type='CrossEntropyLoss',
            weight=[
                0, 5.753576339147521, 0.3181036781804986, 4.6331033270414075,
                5.688047109372652, 4.1993555920488115, 6.285231158810844,
                5.624712115286278, 1.4943104479420062, 5.169177961926781,
                7.75380462244782, 8.642659322546105
            ],
            loss_weight=1.0,
            ignore_index=0),
        dict(
            type='LovaszLoss',
            mode='multiclass',
            loss_weight=1.0,
            ignore_index=0)
    ])
optimizer = dict(type='AdamW', lr=0.002, weight_decay=0.005)
scheduler = dict(
    type='OneCycleLR',
    max_lr=0.002,
    pct_start=0.04,
    anneal_strategy='cos',
    div_factor=10.0,
    final_div_factor=100.0)
dataset_type = 'PowScanDataset'
data_root = 'data/powscan'
data = dict(
    num_classes=12,
    ignore_index=0,
    names=[
        'unlabeled', '铁塔', '中等植被点', '公路', '临时建筑物', '建筑物点', '交叉跨越下', '其他线路',
        '地面点', '导线', '其他', '交叉跨越上'
    ],
    train=dict(
        type='PowScanDataset',
        split='train',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='RandomScale', scale=[0.9, 1.1]),
            dict(type='RandomFlip', p=0.5),
            dict(type='RandomJitter', sigma=0.005, clip=0.02),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ValidateLabels', num_classes=12, ignore_index=0),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False,
        loop=30),
    val=dict(
        type='PowScanDataset',
        split='val',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='Copy', keys_dict=dict(segment='origin_segment')),
            dict(
                type='GridSample',
                grid_size=0.04,
                hash_type='fnv',
                mode='train',
                return_grid_coord=True,
                return_inverse=True),
            dict(type='SphereCrop', point_max=60000, mode='random'),
            dict(type='CenterShift', apply_z=False),
            dict(type='ToTensor'),
            dict(
                type='Collect',
                keys=('coord', 'segment', 'origin_segment'),
                feat_keys=['coord', 'strength'])
        ],
        test_mode=False),
    test=dict(
        type='PowScanDataset',
        split='test',
        data_root='data/powscan',
        transform=[
            dict(type='CenterShift', apply_z=True),
            dict(type='NormalizeColor')
        ],
        test_mode=True,
        test_cfg=dict(
            voxelize=dict(
                type='GridSample',
                grid_size=0.3,
                hash_type='fnv',
                mode='test',
                return_grid_coord=True),
            crop=None,
            post_transform=[
                dict(type='CenterShift', apply_z=False),
                dict(type='ToTensor'),
                dict(
                    type='Collect',
                    keys=('coord', 'grid_coord', 'index'),
                    feat_keys=('coord', 'strength'))
            ],
            aug_transform=[[{
                'type': 'RandomScale',
                'scale': [1, 1]
            }]])))
num_worker_per_gpu = 8
batch_size_per_gpu = 6
batch_size_val_per_gpu = 1
batch_size_test_per_gpu = 1

[2025-09-05 23:49:14,713 INFO train.py line 140 1295800] => Building model ...
[2025-09-05 23:49:14,768 INFO train.py line 241 1295800] Num params: 3908496
[2025-09-05 23:49:14,827 INFO train.py line 142 1295800] => Building writer ...
[2025-09-05 23:49:14,829 INFO train.py line 251 1295800] Tensorboard writer logging dir: exp/powscan/splitSections
[2025-09-05 23:49:14,829 INFO train.py line 144 1295800] => Building train dataset & dataloader ...
[rank0]:[W905 23:49:15.650616869 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())
W0905 23:49:16.413000 1295662 site-packages/torch/multiprocessing/spawn.py:160] Terminating process 1295800 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 38, in <module>
    main()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 27, in main
    launch(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 74, in launch
    mp.spawn(
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 328, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 284, in start_processes
    while not context.join():
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 203, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 1 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 53, in build_from_cfg
    return obj_cls(**args)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/powscan.py", line 37, in __init__
    super(PowScanDataset, self).__init__(
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/defaults.py", line 51, in __init__
    self.transform = Compose(transform)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/transform.py", line 1189, in __init__
    self.transforms.append(TRANSFORMS.build(t_cfg))
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 214, in build
    return self.build_func(*args, **kwargs, registry=self)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 47, in build_from_cfg
    raise KeyError(f"{obj_type} is not in the {registry.name} registry")
KeyError: 'ValidateLabels is not in the transforms registry'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 53, in build_from_cfg
    return obj_cls(**args)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 145, in __init__
    self.train_loader = self.build_train_loader()
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/train.py", line 265, in build_train_loader
    train_data = build_dataset(self.cfg.data.train)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/datasets/builder.py", line 15, in build_dataset
    return DATASETS.build(cfg)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 214, in build
    return self.build_func(*args, **kwargs, registry=self)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 56, in build_from_cfg
    raise type(e)(f"{obj_cls.__name__}: {e}")
KeyError: "PowScanDataset: 'ValidateLabels is not in the transforms registry'"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.conda/envs/pointcept-torch2.5.0-cu12.4/lib/python3.10/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/engines/launch.py", line 137, in _distributed_worker
    main_func(*cfg)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/tools/train.py", line 19, in main_worker
    trainer = TRAINERS.build(dict(type=cfg.train.type, cfg=cfg))
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 214, in build
    return self.build_func(*args, **kwargs, registry=self)
  File "/home/<USER>/Workspace/Pointcept/exp/powscan/splitSections/code/pointcept/utils/registry.py", line 56, in build_from_cfg
    raise type(e)(f"{obj_cls.__name__}: {e}")
KeyError: 'Trainer: "PowScanDataset: \'ValidateLabels is not in the transforms registry\'"'

